"""
Market Regime Detection for Enhanced Trading Algorithm
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime types."""
    BULL_TRENDING = "bull_trending"
    BEAR_TRENDING = "bear_trending"
    SIDEWAYS_RANGING = "sideways_ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    BREAKOUT_PENDING = "breakout_pending"

@dataclass
class RegimeAnalysis:
    """Market regime analysis result."""
    primary_regime: MarketRegime
    secondary_regime: Optional[MarketRegime]
    confidence: float  # 0-100
    trend_strength: float  # -1 to 1 (negative = bearish, positive = bullish)
    volatility_level: float  # 0-1
    volume_trend: float  # -1 to 1
    recommended_strategies: List[str]
    risk_adjustment: float  # 0.5-2.0 (multiplier for position sizing)

class MarketRegimeDetector:
    """
    Advanced market regime detection system.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Regime detection parameters
        self.trend_threshold = config.get('trend_threshold', 0.02)  # 2% for trend detection
        self.volatility_threshold = config.get('volatility_threshold', 0.03)  # 3% for volatility
        self.volume_threshold = config.get('volume_threshold', 1.5)  # 1.5x for volume surge
        self.lookback_period = config.get('lookback_period', 50)  # 50 periods for analysis
        
        logger.info(f"Market Regime Detector initialized with {self.lookback_period} period lookback")
    
    def calculate_trend_strength(self, data: pd.DataFrame) -> float:
        """Calculate trend strength (-1 to 1)."""
        try:
            if len(data) < 20:
                return 0.0
            
            # Linear regression slope
            prices = data['close'].tail(20).values
            x = np.arange(len(prices))
            slope, _ = np.polyfit(x, prices, 1)
            
            # Normalize slope by average price
            avg_price = prices.mean()
            normalized_slope = slope / avg_price
            
            # Convert to -1 to 1 range
            trend_strength = np.tanh(normalized_slope * 100)
            
            return float(trend_strength)
            
        except Exception as e:
            logger.error(f"Error calculating trend strength: {e}")
            return 0.0
    
    def calculate_volatility_level(self, data: pd.DataFrame) -> float:
        """Calculate volatility level (0-1)."""
        try:
            if len(data) < 20:
                return 0.5
            
            # Calculate rolling volatility
            returns = data['close'].pct_change().tail(20)
            volatility = returns.std()
            
            # Normalize to 0-1 range (0.1 = max expected volatility)
            normalized_volatility = min(volatility / 0.1, 1.0)
            
            return float(normalized_volatility)
            
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.5
    
    def calculate_volume_trend(self, data: pd.DataFrame) -> float:
        """Calculate volume trend (-1 to 1)."""
        try:
            if 'volume' not in data.columns or len(data) < 20:
                return 0.0
            
            # Compare recent volume to historical average
            recent_volume = data['volume'].tail(5).mean()
            historical_volume = data['volume'].tail(20).mean()
            
            if historical_volume == 0:
                return 0.0
            
            volume_ratio = recent_volume / historical_volume
            
            # Convert to -1 to 1 range (centered around 1.0)
            volume_trend = np.tanh((volume_ratio - 1.0) * 2)
            
            return float(volume_trend)
            
        except Exception as e:
            logger.error(f"Error calculating volume trend: {e}")
            return 0.0
    
    def detect_ranging_market(self, data: pd.DataFrame) -> bool:
        """Detect if market is in ranging/sideways mode."""
        try:
            if len(data) < 30:
                return False
            
            # Calculate support and resistance levels
            recent_data = data.tail(30)
            high_level = recent_data['high'].max()
            low_level = recent_data['low'].min()
            
            # Check if price is oscillating within a range
            range_size = (high_level - low_level) / recent_data['close'].mean()
            
            # Check if recent prices stay within this range
            recent_prices = recent_data['close'].tail(10)
            within_range = all(low_level <= price <= high_level for price in recent_prices)
            
            # Range market if small range and prices stay within bounds
            is_ranging = range_size < 0.1 and within_range
            
            return is_ranging
            
        except Exception as e:
            logger.error(f"Error detecting ranging market: {e}")
            return False
    
    def detect_breakout_pending(self, data: pd.DataFrame) -> bool:
        """Detect if a breakout is pending."""
        try:
            if len(data) < 30:
                return False
            
            # Check for decreasing volatility (compression)
            recent_volatility = data['close'].pct_change().tail(10).std()
            historical_volatility = data['close'].pct_change().tail(30).std()
            
            volatility_compression = recent_volatility < historical_volatility * 0.7
            
            # Check for volume accumulation
            volume_increasing = False
            if 'volume' in data.columns:
                recent_volume = data['volume'].tail(5).mean()
                historical_volume = data['volume'].tail(20).mean()
                volume_increasing = recent_volume > historical_volume * 1.2
            
            # Check for price consolidation near key levels
            recent_range = (data['high'].tail(10).max() - data['low'].tail(10).min()) / data['close'].tail(10).mean()
            tight_range = recent_range < 0.05
            
            return volatility_compression and (volume_increasing or tight_range)
            
        except Exception as e:
            logger.error(f"Error detecting breakout pending: {e}")
            return False
    
    def analyze_market_regime(self, data: pd.DataFrame, symbol: str) -> RegimeAnalysis:
        """Comprehensive market regime analysis."""
        try:
            if len(data) < self.lookback_period:
                logger.warning(f"Insufficient data for regime analysis: {len(data)} < {self.lookback_period}")
                return RegimeAnalysis(
                    primary_regime=MarketRegime.SIDEWAYS_RANGING,
                    secondary_regime=None,
                    confidence=30.0,
                    trend_strength=0.0,
                    volatility_level=0.5,
                    volume_trend=0.0,
                    recommended_strategies=['mean_reversion'],
                    risk_adjustment=1.0
                )
            
            # Calculate key metrics
            trend_strength = self.calculate_trend_strength(data)
            volatility_level = self.calculate_volatility_level(data)
            volume_trend = self.calculate_volume_trend(data)
            
            # Detect specific conditions
            is_ranging = self.detect_ranging_market(data)
            is_breakout_pending = self.detect_breakout_pending(data)
            
            # Determine primary regime
            primary_regime = MarketRegime.SIDEWAYS_RANGING
            secondary_regime = None
            confidence = 50.0
            
            if abs(trend_strength) > 0.3:  # Strong trend
                if trend_strength > 0:
                    primary_regime = MarketRegime.BULL_TRENDING
                else:
                    primary_regime = MarketRegime.BEAR_TRENDING
                confidence = min(abs(trend_strength) * 200, 90)
                
            elif volatility_level > 0.6:  # High volatility
                primary_regime = MarketRegime.HIGH_VOLATILITY
                confidence = min(volatility_level * 100, 85)
                
            elif volatility_level < 0.2:  # Low volatility
                primary_regime = MarketRegime.LOW_VOLATILITY
                confidence = min((1 - volatility_level) * 80, 80)
                
            elif is_breakout_pending:
                primary_regime = MarketRegime.BREAKOUT_PENDING
                confidence = 75.0
                
            elif is_ranging:
                primary_regime = MarketRegime.SIDEWAYS_RANGING
                confidence = 70.0
            
            # Determine secondary regime
            if primary_regime in [MarketRegime.BULL_TRENDING, MarketRegime.BEAR_TRENDING]:
                if volatility_level > 0.5:
                    secondary_regime = MarketRegime.HIGH_VOLATILITY
                elif volatility_level < 0.3:
                    secondary_regime = MarketRegime.LOW_VOLATILITY
            
            # Recommend strategies based on regime
            recommended_strategies = self.get_recommended_strategies(primary_regime, secondary_regime)
            
            # Calculate risk adjustment
            risk_adjustment = self.calculate_risk_adjustment(primary_regime, volatility_level, trend_strength)
            
            return RegimeAnalysis(
                primary_regime=primary_regime,
                secondary_regime=secondary_regime,
                confidence=confidence,
                trend_strength=trend_strength,
                volatility_level=volatility_level,
                volume_trend=volume_trend,
                recommended_strategies=recommended_strategies,
                risk_adjustment=risk_adjustment
            )
            
        except Exception as e:
            logger.error(f"Error analyzing market regime for {symbol}: {e}")
            return RegimeAnalysis(
                primary_regime=MarketRegime.SIDEWAYS_RANGING,
                secondary_regime=None,
                confidence=30.0,
                trend_strength=0.0,
                volatility_level=0.5,
                volume_trend=0.0,
                recommended_strategies=['mean_reversion'],
                risk_adjustment=1.0
            )
    
    def get_recommended_strategies(self, primary: MarketRegime, secondary: Optional[MarketRegime]) -> List[str]:
        """Get recommended strategies for market regime."""
        strategy_map = {
            MarketRegime.BULL_TRENDING: ['moving_average_crossover', 'breakout'],
            MarketRegime.BEAR_TRENDING: ['moving_average_crossover', 'mean_reversion'],
            MarketRegime.SIDEWAYS_RANGING: ['bollinger_bands', 'mean_reversion'],
            MarketRegime.HIGH_VOLATILITY: ['breakout', 'bollinger_bands'],
            MarketRegime.LOW_VOLATILITY: ['mean_reversion', 'bollinger_bands'],
            MarketRegime.BREAKOUT_PENDING: ['breakout', 'moving_average_crossover']
        }
        
        strategies = strategy_map.get(primary, ['mean_reversion'])
        
        # Add secondary regime strategies
        if secondary and secondary in strategy_map:
            secondary_strategies = strategy_map[secondary]
            strategies.extend([s for s in secondary_strategies if s not in strategies])
        
        return strategies[:3]  # Limit to top 3 strategies
    
    def calculate_risk_adjustment(self, regime: MarketRegime, volatility: float, trend_strength: float) -> float:
        """Calculate risk adjustment multiplier for position sizing."""
        base_adjustment = 1.0
        
        # Regime-based adjustments
        regime_adjustments = {
            MarketRegime.BULL_TRENDING: 1.2,
            MarketRegime.BEAR_TRENDING: 0.8,
            MarketRegime.SIDEWAYS_RANGING: 1.0,
            MarketRegime.HIGH_VOLATILITY: 0.6,
            MarketRegime.LOW_VOLATILITY: 1.3,
            MarketRegime.BREAKOUT_PENDING: 0.9
        }
        
        adjustment = regime_adjustments.get(regime, 1.0)
        
        # Volatility adjustment
        if volatility > 0.6:
            adjustment *= 0.7  # Reduce size in high volatility
        elif volatility < 0.2:
            adjustment *= 1.2  # Increase size in low volatility
        
        # Trend strength adjustment
        if abs(trend_strength) > 0.5:
            adjustment *= 1.1  # Slightly increase size in strong trends
        
        # Ensure reasonable bounds
        return max(0.5, min(adjustment, 2.0))
    
    def get_regime_description(self, analysis: RegimeAnalysis) -> str:
        """Get human-readable regime description."""
        regime_descriptions = {
            MarketRegime.BULL_TRENDING: "Strong Uptrend",
            MarketRegime.BEAR_TRENDING: "Strong Downtrend", 
            MarketRegime.SIDEWAYS_RANGING: "Sideways/Ranging",
            MarketRegime.HIGH_VOLATILITY: "High Volatility",
            MarketRegime.LOW_VOLATILITY: "Low Volatility",
            MarketRegime.BREAKOUT_PENDING: "Breakout Pending"
        }
        
        primary_desc = regime_descriptions.get(analysis.primary_regime, "Unknown")
        
        if analysis.secondary_regime:
            secondary_desc = regime_descriptions.get(analysis.secondary_regime, "")
            return f"{primary_desc} + {secondary_desc}"
        
        return primary_desc
