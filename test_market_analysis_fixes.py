"""
Test script to verify market analysis fixes:
1. Sufficient historical data for 1d timeframe
2. Improved EMA proximity filter with ATR
"""

import os
import sys
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from multi_timeframe_analysis import MultiTimeframeAnalysis
from strategies.enhanced_futures_strategy import EnhancedFuturesStrategy
from utils import load_config

def test_historical_data_fix():
    """Test that we can fetch sufficient 1d historical data."""
    print("🔧 Testing Historical Data Fix")
    print("=" * 50)
    
    try:
        client = BinanceClient(testnet=True)
        
        # Test symbols
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        
        for symbol in test_symbols:
            print(f"\n📊 Testing {symbol}...")
            
            # Test 1d data with increased periods
            data_1d = client.get_historical_klines(symbol, '1d', 250)
            
            if data_1d.empty:
                print(f"❌ No 1d data for {symbol}")
                continue
            
            print(f"✅ 1d data: {len(data_1d)} candles (target: 250)")
            
            if len(data_1d) >= 200:
                print(f"✅ Sufficient data for long-term analysis")
            else:
                print(f"⚠️  Limited data: {len(data_1d)} candles")
            
            # Test other timeframes
            data_4h = client.get_historical_klines(symbol, '4h', 180)
            data_1h = client.get_historical_klines(symbol, '1h', 168)
            
            print(f"   4h data: {len(data_4h)} candles")
            print(f"   1h data: {len(data_1h)} candles")
        
        print("\n✅ Historical data test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing historical data: {e}")
        return False

def test_multi_timeframe_analysis():
    """Test multi-timeframe analysis with improved data fetching."""
    print("\n🔧 Testing Multi-Timeframe Analysis")
    print("=" * 50)
    
    try:
        client = BinanceClient(testnet=True)
        mta = MultiTimeframeAnalysis(client)
        
        # Test with a symbol
        symbol = 'BTCUSDT'
        print(f"📊 Testing multi-timeframe analysis for {symbol}...")
        
        # This should now work without "Insufficient data" warnings
        result = mta.analyze_symbol_all_timeframes(symbol)
        
        if result:
            print("✅ Multi-timeframe analysis completed successfully")
            print(f"   Overall signal: {result.get('overall_signal', 'N/A')}")
            print(f"   Confidence: {result.get('confidence', 0):.2f}")
            
            # Check timeframe results
            timeframe_results = result.get('timeframe_results', {})
            for tf, tf_result in timeframe_results.items():
                print(f"   {tf}: {tf_result.get('signal', 'N/A')} (conf: {tf_result.get('confidence', 0):.2f})")
            
            return True
        else:
            print("❌ Multi-timeframe analysis failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing multi-timeframe analysis: {e}")
        return False

def test_ema_proximity_filter():
    """Test improved EMA proximity filter with ATR."""
    print("\n🔧 Testing EMA Proximity Filter")
    print("=" * 50)
    
    try:
        client = BinanceClient(testnet=True)
        config = load_config('config/config.yaml')
        strategy_config = config.get('strategies', {}).get('enhanced_futures', {})
        
        strategy = EnhancedFuturesStrategy(client, strategy_config)
        
        # Test symbols with different market conditions
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT']
        
        for symbol in test_symbols:
            print(f"\n📊 Testing {symbol}...")
            
            # Get market data
            data = client.get_historical_klines(symbol, '5m', 100)
            if data.empty:
                print(f"❌ No data for {symbol}")
                continue
            
            # Calculate indicators
            data['EMA_fast'] = strategy.calculate_ema(data['close'], strategy.ema_fast_period)
            data['EMA_slow'] = strategy.calculate_ema(data['close'], strategy.ema_slow_period)
            
            # Test market trend context
            context = strategy.check_market_trend_context(data)
            
            print(f"   Market context valid: {context['valid']}")
            if not context['valid']:
                print(f"   Reason: {context['reason']}")
            else:
                print(f"   EMA spread: {context.get('ema_spread', 0):.4f}")
                print(f"   ATR %: {context.get('atr_pct', 0):.4f}")
                print(f"   Volatility: {context.get('volatility', 0):.4f}")
                print(f"   Trend strength: {context.get('trend_strength', 0):.2f}")
        
        print("\n✅ EMA proximity filter test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing EMA proximity filter: {e}")
        return False

def test_atr_calculation():
    """Test ATR calculation functionality."""
    print("\n🔧 Testing ATR Calculation")
    print("=" * 50)
    
    try:
        client = BinanceClient(testnet=True)
        config = load_config('config/config.yaml')
        strategy_config = config.get('strategies', {}).get('enhanced_futures', {})
        
        strategy = EnhancedFuturesStrategy(client, strategy_config)
        
        # Get test data
        symbol = 'BTCUSDT'
        data = client.get_historical_klines(symbol, '1h', 100)
        
        if data.empty:
            print(f"❌ No data for {symbol}")
            return False
        
        # Calculate ATR
        atr = strategy.calculate_atr(data, 14)
        current_atr = atr.iloc[-1]
        current_price = data['close'].iloc[-1]
        atr_pct = current_atr / current_price
        
        print(f"✅ ATR calculation successful for {symbol}")
        print(f"   Current price: {current_price:.2f}")
        print(f"   ATR (14): {current_atr:.4f}")
        print(f"   ATR %: {atr_pct:.4f} ({atr_pct*100:.2f}%)")
        
        # Check if ATR meets minimum threshold
        min_atr_pct = strategy_config.get('min_atr_pct', 0.008)
        if atr_pct >= min_atr_pct:
            print(f"✅ ATR meets minimum threshold ({min_atr_pct:.4f})")
        else:
            print(f"⚠️  ATR below minimum threshold ({min_atr_pct:.4f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing ATR calculation: {e}")
        return False

def test_config_updates():
    """Test that config updates are properly loaded."""
    print("\n🔧 Testing Config Updates")
    print("=" * 50)
    
    try:
        config = load_config('config/config.yaml')
        enhanced_config = config.get('strategies', {}).get('enhanced_futures', {})
        
        # Check updated parameters
        min_ema_spread = enhanced_config.get('min_ema_spread', 0)
        min_atr_pct = enhanced_config.get('min_atr_pct', 0)
        volatility_threshold = enhanced_config.get('volatility_threshold', 0)
        
        print(f"✅ Config loaded successfully")
        print(f"   min_ema_spread: {min_ema_spread} (should be 0.003)")
        print(f"   min_atr_pct: {min_atr_pct} (should be 0.008)")
        print(f"   volatility_threshold: {volatility_threshold} (should be 0.03)")
        
        # Verify values are updated
        if min_ema_spread == 0.003:
            print("✅ EMA spread threshold updated correctly")
        else:
            print(f"❌ EMA spread threshold not updated: {min_ema_spread}")
        
        if min_atr_pct == 0.008:
            print("✅ ATR threshold added correctly")
        else:
            print(f"❌ ATR threshold not found or incorrect: {min_atr_pct}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing config updates: {e}")
        return False

def main():
    """Run all market analysis fix tests."""
    print("🧪 Market Analysis Fixes Test Suite")
    print("=" * 60)
    
    tests = [
        ("Historical Data Fix", test_historical_data_fix),
        ("Multi-Timeframe Analysis", test_multi_timeframe_analysis),
        ("EMA Proximity Filter", test_ema_proximity_filter),
        ("ATR Calculation", test_atr_calculation),
        ("Config Updates", test_config_updates)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All market analysis fixes working correctly!")
        print("\n🚀 Improvements Applied:")
        print("✅ 1d timeframe now fetches 250 candles (was 100)")
        print("✅ EMA spread threshold increased to 0.3% (was 0.1%)")
        print("✅ Added ATR-based volatility filter (0.8% minimum)")
        print("✅ Volatility threshold reduced to 3% (was 5%)")
        print("✅ Enhanced logging for debugging market conditions")
        print("✅ Better filtering prevents false sideways market detection")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
