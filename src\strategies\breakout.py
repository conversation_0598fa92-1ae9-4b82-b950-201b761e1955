"""
Breakout Strategy Implementation
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Optional
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class BreakoutStrategy(BaseStrategy):
    """
    Breakout Strategy: Capitalizes on price breaking out of a defined range.
    
    - BUY: When price breaks above the rolling high (upward breakout)
    - SELL: When price breaks below the rolling low (downward breakout)
    """
    
    def __init__(self, client, config: Dict):
        super().__init__(client, config)
        self.name = "Breakout"
        
        # Strategy parameters
        self.window = config.get('window', 20)
        self.breakout_threshold = config.get('breakout_threshold', 0.001)  # 0.1% threshold for breakout
        self.min_data_points = self.window + 5
        
        logger.info(f"Breakout Strategy initialized: {self.window} period lookback")
    
    def breakout_strategy(self, data: pd.DataFrame, window: int) -> pd.DataFrame:
        """
        Compute breakout signals.
        
        Args:
            data: Price data with OHLCV
            window: Lookback window for high/low calculation
            
        Returns:
            DataFrame with signals
        """
        # Compute rolling highest high and lowest low
        data['rolling_high'] = data['high'].rolling(window=window).max()
        data['rolling_low'] = data['low'].rolling(window=window).min()
        
        # Generate buy/sell signals
        data['signal'] = 0
        data.loc[data['close'] > data['rolling_high'], 'signal'] = 1   # BUY (breakout above range)
        data.loc[data['close'] < data['rolling_low'], 'signal'] = -1   # SELL (breakout below range)
        
        return data
    
    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """
        Generate trading signal based on breakout.
        
        Args:
            data: Historical price data
            symbol: Trading symbol
            
        Returns:
            'BUY', 'SELL', or 'HOLD'
        """
        try:
            if len(data) < self.min_data_points:
                logger.warning(f"[{symbol}] Insufficient data: {len(data)} < {self.min_data_points}")
                return 'HOLD'
            
            # Apply breakout strategy
            strategy_data = self.breakout_strategy(data.copy(), self.window)
            
            # Get the latest data
            latest = strategy_data.iloc[-1]
            previous = strategy_data.iloc[-2]
            
            current_price = latest['close']
            current_high = latest['high']
            current_low = latest['low']
            rolling_high = latest['rolling_high']
            rolling_low = latest['rolling_low']
            
            # Calculate range metrics
            range_size = rolling_high - rolling_low
            range_midpoint = (rolling_high + rolling_low) / 2
            price_position = (current_price - rolling_low) / range_size if range_size > 0 else 0.5
            
            # Check for breakout with threshold
            high_breakout_level = rolling_high * (1 + self.breakout_threshold)
            low_breakout_level = rolling_low * (1 - self.breakout_threshold)
            
            # Detect fresh breakouts (not already in breakout state)
            current_signal = latest['signal']
            previous_signal = previous['signal']
            
            if current_price > high_breakout_level and previous_signal != 1:
                # Upward breakout - BUY signal
                breakout_strength = ((current_price - rolling_high) / rolling_high) * 100
                logger.info(f"[{symbol}] 🚀 BUY signal - Upward Breakout:")
                logger.info(f"[{symbol}]   Price: {current_price:.6f}")
                logger.info(f"[{symbol}]   Rolling High: {rolling_high:.6f}")
                logger.info(f"[{symbol}]   Rolling Low: {rolling_low:.6f}")
                logger.info(f"[{symbol}]   Range Size: {range_size:.6f}")
                logger.info(f"[{symbol}]   ✅ Breakout above range (+{breakout_strength:.2f}%)")
                return 'BUY'
            
            elif current_price < low_breakout_level and previous_signal != -1:
                # Downward breakout - SELL signal
                breakout_strength = ((rolling_low - current_price) / rolling_low) * 100
                logger.info(f"[{symbol}] 📉 SELL signal - Downward Breakout:")
                logger.info(f"[{symbol}]   Price: {current_price:.6f}")
                logger.info(f"[{symbol}]   Rolling High: {rolling_high:.6f}")
                logger.info(f"[{symbol}]   Rolling Low: {rolling_low:.6f}")
                logger.info(f"[{symbol}]   Range Size: {range_size:.6f}")
                logger.info(f"[{symbol}]   ✅ Breakout below range (-{breakout_strength:.2f}%)")
                return 'SELL'
            
            else:
                # No breakout - HOLD
                distance_to_high = ((rolling_high - current_price) / current_price) * 100
                distance_to_low = ((current_price - rolling_low) / current_price) * 100
                
                logger.info(f"[{symbol}] ➡️  No breakout signal:")
                logger.info(f"[{symbol}]   Price: {current_price:.6f}")
                logger.info(f"[{symbol}]   Range: [{rolling_low:.6f}, {rolling_high:.6f}]")
                logger.info(f"[{symbol}]   Position in range: {price_position:.1%}")
                logger.info(f"[{symbol}]   Distance to high: {distance_to_high:.2f}%")
                logger.info(f"[{symbol}]   Distance to low: {distance_to_low:.2f}%")
                return 'HOLD'
        
        except Exception as e:
            logger.error(f"[{symbol}] Error in Breakout strategy: {e}")
            return 'HOLD'
    
    def get_strategy_info(self) -> Dict:
        """Get strategy information."""
        return {
            'name': self.name,
            'type': 'Momentum',
            'window': self.window,
            'breakout_threshold': self.breakout_threshold,
            'min_data_points': self.min_data_points,
            'description': f'Breakout {self.window}p lookback'
        }
    
    def validate_signal(self, data: pd.DataFrame, signal: str) -> bool:
        """
        Validate the generated signal.
        
        Args:
            data: Price data
            signal: Generated signal
            
        Returns:
            True if signal is valid
        """
        try:
            if signal == 'HOLD':
                return True
            
            latest = data.iloc[-1]
            
            # Ensure we have valid range values
            required_fields = ['rolling_high', 'rolling_low']
            for field in required_fields:
                if pd.isna(latest.get(field)):
                    logger.warning(f"Invalid {field} value: {latest.get(field)}")
                    return False
            
            # Ensure range is valid
            if latest['rolling_low'] >= latest['rolling_high']:
                logger.warning(f"Invalid range: low={latest['rolling_low']}, high={latest['rolling_high']}")
                return False
            
            # Ensure range size is reasonable (not too small)
            range_size = latest['rolling_high'] - latest['rolling_low']
            if range_size <= 0:
                logger.warning(f"Invalid range size: {range_size}")
                return False
            
            # Ensure current price is reasonable relative to range
            current_price = latest['close']
            if current_price <= 0:
                logger.warning(f"Invalid current price: {current_price}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating signal: {e}")
            return False

    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate breakout indicators.

        Args:
            data: Price data

        Returns:
            DataFrame with calculated indicators
        """
        # Calculate rolling highest high and lowest low
        data['rolling_high'] = data['high'].rolling(window=self.window).max()
        data['rolling_low'] = data['low'].rolling(window=self.window).min()

        return data

    def should_buy(self, data: pd.DataFrame) -> bool:
        """
        Check if conditions are met for a BUY signal.

        Args:
            data: Price data with indicators

        Returns:
            True if should buy
        """
        if len(data) < self.min_data_points:
            return False

        latest = data.iloc[-1]

        # Check for upward breakout (price breaks above rolling high)
        breakout_level = latest['rolling_high'] * (1 + self.breakout_threshold)
        return latest['close'] > breakout_level

    def should_sell(self, data: pd.DataFrame) -> bool:
        """
        Check if conditions are met for a SELL signal.

        Args:
            data: Price data with indicators

        Returns:
            True if should sell
        """
        if len(data) < self.min_data_points:
            return False

        latest = data.iloc[-1]

        # Check for downward breakout (price breaks below rolling low)
        breakout_level = latest['rolling_low'] * (1 - self.breakout_threshold)
        return latest['close'] < breakout_level
