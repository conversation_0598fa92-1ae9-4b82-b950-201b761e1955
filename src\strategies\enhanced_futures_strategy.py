"""
Enhanced Futures Trading Strategy following context.txt guidelines.
Implements EMA + RSI with volume confirmation and market condition awareness.
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple
import logging
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

# --- CONFIGURABLE STRATEGY THRESHOLDS ---
# Adjust these values to make the strategy more/less restrictive
DEFAULT_MIN_ATR_PCT = 0.0010        # 0.1% minimum ATR (VERY PERMISSIVE)
DEFAULT_MIN_EMA_SPREAD = 0.0005     # 0.05% minimum EMA spread (VERY PERMISSIVE)
DEFAULT_MAX_VOLATILITY = 0.10       # 10% maximum volatility threshold (VERY PERMISSIVE)
# ----------------------------------------

class EnhancedFuturesStrategy(BaseStrategy):
    """
    Enhanced futures strategy implementing context.txt guidelines:
    
    Entry Conditions:
    - BUY (Long): EMA Fast > EMA Slow + RSI < 30 (oversold)
    - SELL (Short): EMA Fast < EMA Slow + RSI > 70 (overbought)
    
    Pro-Level Intelligence:
    - Volume spike confirmation
    - Market trend context
    - Time-based filters
    - Multi-indicator confirmation
    - Cooldown timer
    """
    
    def __init__(self, binance_client, config: Dict):
        """Initialize enhanced futures strategy."""
        super().__init__("Enhanced_Futures_Strategy", config)
        
        self.client = binance_client
        
        # EMA parameters
        self.ema_fast_period = config.get('ema_fast_period', 12)
        self.ema_slow_period = config.get('ema_slow_period', 26)
        
        # RSI parameters
        self.rsi_period = config.get('rsi_period', 14)
        self.rsi_oversold = config.get('rsi_oversold', 30)
        self.rsi_overbought = config.get('rsi_overbought', 70)
        
        # Volume confirmation
        self.volume_spike_threshold = config.get('volume_spike_threshold', 1.5)  # 50% above average
        self.volume_lookback = config.get('volume_lookback', 20)
        
        # Market condition filters - Using configurable defaults for more trading opportunities
        self.min_ema_spread = config.get('min_ema_spread', DEFAULT_MIN_EMA_SPREAD)
        self.volatility_threshold = config.get('volatility_threshold', DEFAULT_MAX_VOLATILITY)

        # ATR-based volatility filter - Using configurable defaults
        self.min_atr_pct = config.get('min_atr_pct', DEFAULT_MIN_ATR_PCT)
        self.atr_period = config.get('atr_period', 14)

        # Debug logging to verify config values are loaded
        logger.info(f"Enhanced Futures Strategy initialized with thresholds:")
        logger.info(f"  min_ema_spread: {self.min_ema_spread:.4f} (default: {DEFAULT_MIN_EMA_SPREAD:.4f})")
        logger.info(f"  min_atr_pct: {self.min_atr_pct:.4f} (default: {DEFAULT_MIN_ATR_PCT:.4f})")
        logger.info(f"  volatility_threshold: {self.volatility_threshold:.4f} (default: {DEFAULT_MAX_VOLATILITY:.4f})")
        
        # Time-based filters
        self.avoid_low_volume_hours = config.get('avoid_low_volume_hours', True)
        self.low_volume_hours = config.get('low_volume_hours', [22, 23, 0, 1, 2, 3, 4, 5])  # UTC
        
        # Cooldown timer
        self.cooldown_minutes = config.get('cooldown_minutes', 5)
        self.last_signal_time = {}
        
        # Multi-indicator confirmation
        self.require_macd_confirmation = config.get('require_macd_confirmation', True)
        self.macd_fast = config.get('macd_fast', 12)
        self.macd_slow = config.get('macd_slow', 26)
        self.macd_signal = config.get('macd_signal', 9)
        
        logger.info("Enhanced Futures Strategy initialized with pro-level intelligence")
    
    def calculate_ema(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average."""
        return prices.ewm(span=period, adjust=False).mean()
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI (Relative Strength Index)."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range (ATR) for volatility measurement."""
        high = data['high']
        low = data['low']
        close = data['close']

        # True Range calculation
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))

        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()

        return atr
    
    def calculate_macd(self, prices: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Calculate MACD indicator."""
        ema_fast = self.calculate_ema(prices, self.macd_fast)
        ema_slow = self.calculate_ema(prices, self.macd_slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = self.calculate_ema(macd_line, self.macd_signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    def check_volume_confirmation(self, data: pd.DataFrame) -> bool:
        """
        Check for volume spike confirmation.
        
        Args:
            data: OHLCV data
            
        Returns:
            True if volume confirms the signal
        """
        if len(data) < self.volume_lookback:
            return False
        
        # Calculate average volume
        avg_volume = data['volume'].tail(self.volume_lookback).mean()
        current_volume = data['volume'].iloc[-1]
        
        # Check for volume spike
        volume_spike = current_volume >= (avg_volume * self.volume_spike_threshold)

        if volume_spike:
            logger.info(f"[{data.index[-1] if hasattr(data, 'index') else 'UNKNOWN'}] ✅ Volume spike confirmed: {current_volume:.0f} vs avg {avg_volume:.0f} (threshold: {self.volume_spike_threshold}x)")
        else:
            logger.info(f"[{data.index[-1] if hasattr(data, 'index') else 'UNKNOWN'}] ❌ Volume too low: {current_volume:.0f} vs avg {avg_volume:.0f} (need: {self.volume_spike_threshold}x)")

        return volume_spike
    
    def check_market_trend_context(self, data: pd.DataFrame, symbol: str = "UNKNOWN") -> Dict:
        """
        Check market trend context to avoid sideways/noise zones.

        Args:
            data: OHLCV data with indicators
            symbol: Trading symbol for logging

        Returns:
            Dictionary with trend analysis
        """
        if len(data) < max(self.ema_fast_period, self.ema_slow_period):
            return {'valid': False, 'reason': 'Insufficient data'}
        
        latest = data.iloc[-1]
        ema_fast = latest['EMA_fast']
        ema_slow = latest['EMA_slow']
        
        # Calculate EMA spread
        ema_spread = abs(ema_fast - ema_slow) / latest['close']

        # Calculate ATR for volatility measurement
        atr = self.calculate_atr(data, self.atr_period)
        current_atr = atr.iloc[-1]
        atr_pct = current_atr / latest['close']

        # Enhanced filtering with better logging
        reasons = []

        # Check EMA spread (loosened threshold)
        if ema_spread < self.min_ema_spread:
            reasons.append(f"EMA spread too small: {ema_spread:.4f} < {self.min_ema_spread:.4f}")

        # Check ATR-based volatility
        if atr_pct < self.min_atr_pct:
            reasons.append(f"ATR too low: {atr_pct:.4f} < {self.min_atr_pct:.4f}")

        # Calculate recent volatility (price change based)
        returns = data['close'].pct_change().tail(20)
        volatility = returns.std()

        if volatility > self.volatility_threshold:
            reasons.append(f"High volatility: {volatility:.4f} > {self.volatility_threshold:.4f}")

        # Log detailed analysis for debugging with symbol name
        logger.info(f"[{symbol}] Context check — EMA spread: {ema_spread:.4f}, ATR: {atr_pct:.4f}, Volatility: {volatility:.4f}")

        if reasons:
            combined_reason = "; ".join(reasons)
            logger.info(f"[{symbol}] → Skipped: {combined_reason}")
            return {'valid': False, 'reason': combined_reason}

        logger.info(f"[{symbol}] ✅ Context valid - trend strength: {ema_spread / self.min_ema_spread:.2f}x")

        return {
            'valid': True,
            'ema_spread': ema_spread,
            'atr_pct': atr_pct,
            'volatility': volatility,
            'trend_strength': ema_spread / self.min_ema_spread
        }
    
    def check_time_filter(self) -> bool:
        """
        Check time-based filters to avoid low-volume periods.
        
        Returns:
            True if current time is suitable for trading
        """
        if not self.avoid_low_volume_hours:
            return True
        
        current_hour = pd.Timestamp.now().hour
        
        if current_hour in self.low_volume_hours:
            logger.info(f"Avoiding trade during low-volume hour: {current_hour}")
            return False
        
        return True
    
    def check_cooldown(self, symbol: str) -> bool:
        """
        Check cooldown timer to prevent overtrading.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            True if cooldown period has passed
        """
        if symbol not in self.last_signal_time:
            return True
        
        time_since_last = pd.Timestamp.now() - self.last_signal_time[symbol]
        cooldown_passed = time_since_last.total_seconds() >= (self.cooldown_minutes * 60)
        
        if not cooldown_passed:
            remaining = (self.cooldown_minutes * 60) - time_since_last.total_seconds()
            logger.info(f"Cooldown active for {symbol}: {remaining:.0f}s remaining")
        
        return cooldown_passed
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate all technical indicators."""
        df = data.copy()
        
        # Calculate EMAs
        df['EMA_fast'] = self.calculate_ema(df['close'], self.ema_fast_period)
        df['EMA_slow'] = self.calculate_ema(df['close'], self.ema_slow_period)
        
        # Calculate RSI
        df['RSI'] = self.calculate_rsi(df['close'], self.rsi_period)
        
        # Calculate MACD
        df['MACD'], df['MACD_signal'], df['MACD_histogram'] = self.calculate_macd(df['close'])
        
        # EMA trend signals
        df['EMA_bullish'] = df['EMA_fast'] > df['EMA_slow']
        df['EMA_bearish'] = df['EMA_fast'] < df['EMA_slow']
        
        # RSI signals
        df['RSI_oversold'] = df['RSI'] < self.rsi_oversold
        df['RSI_overbought'] = df['RSI'] > self.rsi_overbought
        
        # MACD signals
        df['MACD_bullish'] = df['MACD'] > df['MACD_signal']
        df['MACD_bearish'] = df['MACD'] < df['MACD_signal']
        
        return df

    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """
        Generate trading signal with enhanced intelligence.

        Args:
            data: OHLCV data
            symbol: Trading symbol

        Returns:
            Signal: 'BUY', 'SELL', or 'HOLD'
        """
        # Check minimum data requirements
        min_periods = max(self.ema_slow_period, self.rsi_period, self.volume_lookback)
        if len(data) < min_periods:
            return 'HOLD'

        # 🧪 TEST MODE: Force a trade for specific symbols (REMOVE AFTER TESTING)
        TEST_SYMBOLS = ['DOGEUSDT', 'ADAUSDT', 'SOLUSDT', 'INITUSDT']  # Add symbols you want to test
        ENABLE_TEST_MODE = True  # ENABLED for testing - will force BUY signals

        if ENABLE_TEST_MODE and symbol in TEST_SYMBOLS:
            logger.warning(f"[{symbol}] 🧪 TEST MODE: Forcing a BUY signal for testing!")
            logger.warning(f"[{symbol}] ⚠️  This is a test trade - remove TEST_MODE after verification")
            return 'BUY'

        # Calculate indicators
        df = self.calculate_indicators(data)
        latest = df.iloc[-1]

        # Check cooldown timer
        if not self.check_cooldown(symbol):
            return 'HOLD'

        # Check time-based filters
        if not self.check_time_filter():
            return 'HOLD'

        # Check market trend context
        trend_context = self.check_market_trend_context(df, symbol)
        if not trend_context['valid']:
            # Detailed logging is now handled in check_market_trend_context
            return 'HOLD'

        # Check volume confirmation (DISABLED FOR MORE TRADES)
        volume_ok = self.check_volume_confirmation(df)
        if not volume_ok:
            logger.info(f"[{symbol}] ⚠️  Volume confirmation failed but CONTINUING anyway (relaxed rules)")
        else:
            logger.info(f"[{symbol}] ✅ Volume confirmation passed")

        # Get latest values for signal generation
        latest = df.iloc[-1]

        # Log current market conditions for signal generation
        logger.info(f"[{symbol}] 📊 Signal conditions:")
        logger.info(f"[{symbol}]   EMA Fast: {latest['EMA_fast']:.6f}, EMA Slow: {latest['EMA_slow']:.6f}")
        logger.info(f"[{symbol}]   RSI: {latest['RSI']:.2f} (oversold<30, overbought>70)")
        logger.info(f"[{symbol}]   EMA Bullish: {latest.get('EMA_bullish', False)}, RSI Oversold: {latest.get('RSI_oversold', False)}")
        logger.info(f"[{symbol}]   EMA Bearish: {latest.get('EMA_bearish', False)}, RSI Overbought: {latest.get('RSI_overbought', False)}")

        # Generate base signals according to context.txt rules
        signal = 'HOLD'

        # CONTEXT.TXT COMPLIANT SIGNAL LOGIC

        # BUY (Long) Signal: EMA Fast > EMA Slow AND RSI < 30 (oversold)
        if latest['EMA_fast'] > latest['EMA_slow'] and latest['RSI'] < 30:
            signal = 'BUY'
            logger.info(f"[{symbol}] 🚀 BUY signal - CONTEXT.TXT rules met:")
            logger.info(f"[{symbol}]   ✅ EMA Fast > EMA Slow: {latest['EMA_fast']:.6f} > {latest['EMA_slow']:.6f}")
            logger.info(f"[{symbol}]   ✅ RSI < 30 (oversold): {latest['RSI']:.2f}")

        # SELL (Short) Signal: EMA Fast < EMA Slow AND RSI > 70 (overbought)
        elif latest['EMA_fast'] < latest['EMA_slow'] and latest['RSI'] > 70:
            signal = 'SELL'
            logger.info(f"[{symbol}] 📉 SELL signal - CONTEXT.TXT rules met:")
            logger.info(f"[{symbol}]   ✅ EMA Fast < EMA Slow: {latest['EMA_fast']:.6f} < {latest['EMA_slow']:.6f}")
            logger.info(f"[{symbol}]   ✅ RSI > 70 (overbought): {latest['RSI']:.2f}")

        # Log why signal conditions are not met
        else:
            logger.info(f"[{symbol}] ❌ No signal - CONTEXT.TXT conditions not met:")
            logger.info(f"[{symbol}]   EMA condition: Fast({latest['EMA_fast']:.6f}) vs Slow({latest['EMA_slow']:.6f})")
            logger.info(f"[{symbol}]   RSI condition: {latest['RSI']:.2f} (need <30 for BUY or >70 for SELL)")
            logger.info(f"[{symbol}]   BUY needs: EMA Fast > EMA Slow AND RSI < 30")
            logger.info(f"[{symbol}]   SELL needs: EMA Fast < EMA Slow AND RSI > 70")

        # Log when no signal conditions are met
        if signal == 'HOLD':
            logger.info(f"[{symbol}] ❌ No signal conditions met:")
            logger.info(f"[{symbol}]   BUY needs: EMA_bullish AND RSI_oversold")
            logger.info(f"[{symbol}]   SELL needs: EMA_bearish AND RSI_overbought")
            logger.info(f"[{symbol}]   Current: EMA_bullish={latest.get('EMA_bullish', False)}, RSI_oversold={latest.get('RSI_oversold', False)}")
            logger.info(f"[{symbol}]   Current: EMA_bearish={latest.get('EMA_bearish', False)}, RSI_overbought={latest.get('RSI_overbought', False)}")

        # Update last signal time if signal generated
        if signal != 'HOLD':
            self.last_signal_time[symbol] = pd.Timestamp.now()

            # Log detailed signal information
            indicators = {
                'EMA_fast': latest['EMA_fast'],
                'EMA_slow': latest['EMA_slow'],
                'RSI': latest['RSI'],
                'MACD': latest['MACD'],
                'MACD_signal': latest['MACD_signal'],
                'volume': latest['volume'],
                'trend_strength': trend_context.get('trend_strength', 0),
                'volatility': trend_context.get('volatility', 0)
            }

            self.log_signal(symbol, signal, latest['close'], indicators)

        return signal

    def get_strategy_info(self) -> Dict:
        """Get strategy information."""
        return {
            'name': self.name,
            'type': 'Enhanced Futures Strategy',
            'ema_fast_period': self.ema_fast_period,
            'ema_slow_period': self.ema_slow_period,
            'rsi_period': self.rsi_period,
            'rsi_oversold': self.rsi_oversold,
            'rsi_overbought': self.rsi_overbought,
            'volume_spike_threshold': self.volume_spike_threshold,
            'cooldown_minutes': self.cooldown_minutes,
            'features': [
                'EMA + RSI Entry Logic',
                'Volume Spike Confirmation',
                'Market Trend Context',
                'Time-based Filters',
                'MACD Confirmation',
                'Cooldown Timer',
                'Volatility Awareness'
            ],
            'description': 'Enhanced futures strategy following context.txt guidelines with pro-level intelligence'
        }
