#!/usr/bin/env python3
"""
Enhanced Trading Bot with Advanced Multi-Strategy System
Designed to achieve 70%+ win rate through sophisticated strategy combination.
"""

import sys
import os
import time
import logging
import yaml
from datetime import datetime
from typing import Dict, List, Optional

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import enhanced components
from advanced_strategy_manager import AdvancedStrategyManager, StrategySignal
from adaptive_risk_manager import AdaptiveRiskManager, RiskParameters
from performance_analytics import PerformanceAnalytics
from binance_client import BinanceClient
from portfolio import Portfolio

# Import existing components
from chart_analysis_engine import ChartAnalysisEngine
from single_position_manager import SinglePositionManager
from multi_timeframe_analysis import MultiTimeframeAnalysis

class EnhancedTradingBot:
    """
    Enhanced trading bot with advanced multi-strategy system,
    adaptive risk management, and performance analytics.
    """
    
    def __init__(self, config_path: str = 'config/config.yaml'):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Setup logging
        self._setup_logging()
        
        # Initialize core components
        self.client = BinanceClient(self.config['binance'])
        self.portfolio = Portfolio(self.config['portfolio'])
        
        # Initialize enhanced components
        self.strategy_manager = AdvancedStrategyManager(self.config['advanced_strategies'])
        self.risk_manager = AdaptiveRiskManager(self.config['adaptive_risk'])
        self.analytics = PerformanceAnalytics(self.config['performance_analytics'])
        
        # Initialize existing components
        self.chart_analyzer = ChartAnalysisEngine(self.config['chart_analysis'])
        self.position_manager = SinglePositionManager(self.config['single_position'])
        self.multi_timeframe = MultiTimeframeAnalysis(self.client)
        
        # Trading state
        self.is_running = False
        self.current_position = None
        self.last_trade_time = None
        
        # Performance tracking
        self.session_stats = {
            'trades_executed': 0,
            'wins': 0,
            'losses': 0,
            'total_pnl': 0.0,
            'start_time': datetime.now()
        }
        
        self.logger.info("🚀 Enhanced Trading Bot initialized with advanced strategies")
        self._display_startup_banner()
    
    def _setup_logging(self):
        """Setup enhanced logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/enhanced_trading.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _display_startup_banner(self):
        """Display enhanced startup banner."""
        print("=" * 80)
        print("🚀 ENHANCED TRADING BOT - MULTI-STRATEGY SYSTEM")
        print("🎯 Target: 70%+ Win Rate through Advanced Strategy Combination")
        print("=" * 80)
        print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 Initial Balance: ${self.portfolio.get_balance():.2f}")
        print(f"🔧 Strategies: 7 advanced strategies with adaptive weighting")
        print(f"🛡️  Risk Management: Dynamic stops and position sizing")
        print(f"📊 Analytics: Real-time performance tracking and optimization")
        print("=" * 80)
    
    def run(self):
        """Run the enhanced trading bot."""
        try:
            self.is_running = True
            self.logger.info("🚀 Enhanced trading bot started")
            
            while self.is_running:
                try:
                    # Main trading cycle
                    self._trading_cycle()
                    
                    # Performance monitoring
                    self._monitor_performance()
                    
                    # Wait before next cycle
                    time.sleep(self.config.get('trading_interval_seconds', 300))  # 5 minutes
                    
                except KeyboardInterrupt:
                    self.logger.info("🛑 Bot stopped by user")
                    break
                except Exception as e:
                    self.logger.error(f"❌ Error in trading cycle: {e}")
                    time.sleep(60)  # Wait 1 minute before retrying
                    
        except Exception as e:
            self.logger.error(f"❌ Critical error: {e}")
        finally:
            self._shutdown()
    
    def _trading_cycle(self):
        """Enhanced trading cycle with multi-strategy analysis."""
        try:
            self.logger.info("🔄 Starting enhanced trading cycle")
            
            # Check if we have an active position
            if self.current_position:
                self._manage_existing_position()
                return
            
            # Scan for new opportunities
            opportunity = self._scan_for_opportunities()
            
            if opportunity:
                self.logger.info(f"🎯 High-confidence opportunity found: {opportunity.symbol}")
                self._execute_enhanced_trade(opportunity)
            else:
                self.logger.info("📊 No high-confidence opportunities found")
                
        except Exception as e:
            self.logger.error(f"Error in trading cycle: {e}")
    
    def _scan_for_opportunities(self) -> Optional[Dict]:
        """Scan for trading opportunities using advanced strategies."""
        try:
            # Get altcoin symbols
            symbols = self._get_trading_symbols()
            best_opportunity = None
            highest_score = 0
            
            self.logger.info(f"🔍 Scanning {len(symbols)} symbols with advanced strategies")
            
            for i, symbol in enumerate(symbols, 1):
                try:
                    self.logger.info(f"📊 [{i}/{len(symbols)}] Analyzing {symbol}...")
                    
                    # Get multi-timeframe data
                    data_5m = self.client.get_klines(symbol, '5m', 100)
                    data_15m = self.client.get_klines(symbol, '15m', 100)
                    data_1h = self.client.get_klines(symbol, '1h', 100)
                    
                    if data_15m is None or data_15m.empty:
                        continue
                    
                    # Run all strategies
                    strategy_signals = self.strategy_manager.analyze_all_strategies(data_15m, symbol)
                    
                    if not strategy_signals:
                        continue
                    
                    # Combine signals
                    combined_signal = self.strategy_manager.combine_signals(strategy_signals)
                    
                    if combined_signal and combined_signal.confidence >= self.config['advanced_strategies']['min_overall_confidence']:
                        
                        # Calculate risk parameters
                        signal_data = {
                            'symbol': symbol,
                            'signal': combined_signal.signal,
                            'confidence': combined_signal.confidence,
                            'entry_price': combined_signal.entry_price,
                            'strategy': combined_signal.reason
                        }
                        
                        risk_params = self.risk_manager.calculate_risk_parameters(
                            signal_data, data_15m, self.portfolio.get_balance()
                        )
                        
                        # Check if risk/reward meets requirements
                        if risk_params.risk_reward_ratio >= self.config['adaptive_risk']['min_risk_reward_ratio']:
                            
                            # Calculate opportunity score
                            opportunity_score = self._calculate_opportunity_score(
                                combined_signal, risk_params, len(strategy_signals)
                            )
                            
                            self.logger.info(f"✅ VALID OPPORTUNITY: {symbol}")
                            self.logger.info(f"   Signal: {combined_signal.signal}")
                            self.logger.info(f"   Confidence: {combined_signal.confidence:.1f}%")
                            self.logger.info(f"   Strategies: {len(strategy_signals)} confirming")
                            self.logger.info(f"   Risk/Reward: {risk_params.risk_reward_ratio:.2f}")
                            self.logger.info(f"   Opportunity Score: {opportunity_score:.1f}")
                            
                            if opportunity_score > highest_score:
                                highest_score = opportunity_score
                                best_opportunity = {
                                    'symbol': symbol,
                                    'signal': combined_signal,
                                    'risk_params': risk_params,
                                    'strategy_signals': strategy_signals,
                                    'opportunity_score': opportunity_score,
                                    'market_data': data_15m
                                }
                        else:
                            self.logger.info(f"🚫 REJECTED {symbol}: Poor risk/reward {risk_params.risk_reward_ratio:.2f}")
                    else:
                        if combined_signal:
                            self.logger.info(f"🚫 REJECTED {symbol}: Low confidence {combined_signal.confidence:.1f}%")
                        else:
                            self.logger.info(f"🚫 REJECTED {symbol}: No strategy consensus")
                            
                except Exception as e:
                    self.logger.error(f"❌ Error analyzing {symbol}: {e}")
                    continue
            
            if best_opportunity:
                self.logger.info(f"🎯 BEST OPPORTUNITY: {best_opportunity['symbol']} (Score: {best_opportunity['opportunity_score']:.1f})")
            
            return best_opportunity
            
        except Exception as e:
            self.logger.error(f"Error scanning for opportunities: {e}")
            return None
    
    def _calculate_opportunity_score(self, signal: StrategySignal, risk_params: RiskParameters, 
                                   strategy_count: int) -> float:
        """Calculate composite opportunity score."""
        try:
            # Base score from confidence
            confidence_score = signal.confidence
            
            # Strategy consensus bonus
            consensus_bonus = min(strategy_count * 5, 25)  # Up to 25% bonus
            
            # Risk/reward bonus
            rr_bonus = min((risk_params.risk_reward_ratio - 1) * 10, 20)  # Up to 20% bonus
            
            # Volatility adjustment
            volatility_adjustment = risk_params.volatility_adjustment * 5
            
            # Calculate final score
            total_score = confidence_score + consensus_bonus + rr_bonus + volatility_adjustment
            
            return min(total_score, 100)  # Cap at 100
            
        except Exception as e:
            self.logger.error(f"Error calculating opportunity score: {e}")
            return 0
    
    def _execute_enhanced_trade(self, opportunity: Dict):
        """Execute trade with enhanced risk management."""
        try:
            symbol = opportunity['symbol']
            signal = opportunity['signal']
            risk_params = opportunity['risk_params']
            
            self.logger.info(f"🚀 EXECUTING ENHANCED TRADE: {symbol}")
            self.logger.info(f"   Signal: {signal.signal}")
            self.logger.info(f"   Entry: ${signal.entry_price:.6f}")
            self.logger.info(f"   Stop Loss: ${risk_params.stop_loss:.6f}")
            self.logger.info(f"   Take Profit: ${risk_params.take_profit:.6f}")
            self.logger.info(f"   Position Size: ${risk_params.position_size:.2f}")
            self.logger.info(f"   Max Hold Time: {risk_params.max_hold_time} minutes")
            
            # Calculate quantity based on position size
            quantity = risk_params.position_size / signal.entry_price
            
            # Place order
            order_result = self.client.place_futures_order(
                symbol=symbol,
                side=signal.signal,
                quantity=quantity,
                order_type='MARKET'
            )
            
            if order_result:
                # Record position
                self.current_position = {
                    'symbol': symbol,
                    'side': signal.signal,
                    'entry_price': signal.entry_price,
                    'quantity': quantity,
                    'stop_loss': risk_params.stop_loss,
                    'take_profit': risk_params.take_profit,
                    'max_hold_time': risk_params.max_hold_time,
                    'entry_time': datetime.now(),
                    'strategy': signal.reason,
                    'confidence': signal.confidence,
                    'risk_reward_ratio': risk_params.risk_reward_ratio
                }
                
                self.logger.info(f"✅ ENHANCED TRADE EXECUTED SUCCESSFULLY")
                self.session_stats['trades_executed'] += 1
                
            else:
                self.logger.error(f"❌ Failed to execute trade for {symbol}")
                
        except Exception as e:
            self.logger.error(f"Error executing enhanced trade: {e}")
    
    def _manage_existing_position(self):
        """Manage existing position with enhanced exit logic."""
        try:
            if not self.current_position:
                return
            
            symbol = self.current_position['symbol']
            current_price = self.client.get_current_price(symbol)
            
            if not current_price:
                return
            
            # Calculate current P&L
            entry_price = self.current_position['entry_price']
            quantity = self.current_position['quantity']
            
            if self.current_position['side'] == 'BUY':
                pnl = (current_price - entry_price) * quantity
                pnl_percentage = (current_price - entry_price) / entry_price * 100
            else:  # SELL
                pnl = (entry_price - current_price) * quantity
                pnl_percentage = (entry_price - current_price) / entry_price * 100
            
            # Calculate hold time
            hold_time = (datetime.now() - self.current_position['entry_time']).total_seconds() / 60
            
            self.logger.info(f"📊 POSITION STATUS: {symbol}")
            self.logger.info(f"   P&L: ${pnl:.2f} ({pnl_percentage:.2f}%)")
            self.logger.info(f"   Hold Time: {hold_time:.1f} minutes")
            
            # Check exit conditions
            exit_reason = None
            
            # Stop loss hit
            if ((self.current_position['side'] == 'BUY' and current_price <= self.current_position['stop_loss']) or
                (self.current_position['side'] == 'SELL' and current_price >= self.current_position['stop_loss'])):
                exit_reason = "Stop loss hit"
            
            # Take profit hit
            elif ((self.current_position['side'] == 'BUY' and current_price >= self.current_position['take_profit']) or
                  (self.current_position['side'] == 'SELL' and current_price <= self.current_position['take_profit'])):
                exit_reason = "Take profit hit"
            
            # Max hold time exceeded
            elif hold_time >= self.current_position['max_hold_time']:
                exit_reason = f"Max hold time ({self.current_position['max_hold_time']}min) exceeded"
            
            # Exit if condition met
            if exit_reason:
                self._close_position(exit_reason, pnl, pnl_percentage, hold_time)
                
        except Exception as e:
            self.logger.error(f"Error managing position: {e}")
    
    def _close_position(self, exit_reason: str, pnl: float, pnl_percentage: float, hold_time: float):
        """Close current position and record results."""
        try:
            if not self.current_position:
                return
            
            symbol = self.current_position['symbol']
            side = 'SELL' if self.current_position['side'] == 'BUY' else 'BUY'
            quantity = self.current_position['quantity']
            
            self.logger.info(f"🔄 CLOSING POSITION: {exit_reason}")
            
            # Place closing order
            order_result = self.client.place_futures_order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type='MARKET'
            )
            
            if order_result:
                # Record trade result
                trade_data = {
                    'symbol': symbol,
                    'strategy': self.current_position['strategy'],
                    'signal': self.current_position['side'],
                    'entry_price': self.current_position['entry_price'],
                    'exit_price': self.client.get_current_price(symbol),
                    'quantity': quantity,
                    'pnl': pnl,
                    'pnl_percentage': pnl_percentage,
                    'hold_time_minutes': int(hold_time),
                    'exit_reason': exit_reason,
                    'confidence': self.current_position['confidence'],
                    'risk_reward_ratio': self.current_position['risk_reward_ratio']
                }
                
                # Update analytics
                self.analytics.record_trade(trade_data)
                
                # Update risk manager
                self.risk_manager.update_trade_result(trade_data)
                
                # Update session stats
                if pnl > 0:
                    self.session_stats['wins'] += 1
                else:
                    self.session_stats['losses'] += 1
                
                self.session_stats['total_pnl'] += pnl
                
                # Update portfolio
                self.portfolio.update_balance(pnl)
                
                self.logger.info(f"✅ POSITION CLOSED SUCCESSFULLY")
                self.logger.info(f"   Final P&L: ${pnl:.2f} ({pnl_percentage:.2f}%)")
                self.logger.info(f"   Exit Reason: {exit_reason}")
                
                # Clear position
                self.current_position = None
                self.last_trade_time = datetime.now()
                
            else:
                self.logger.error(f"❌ Failed to close position for {symbol}")
                
        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
    
    def _monitor_performance(self):
        """Monitor and display performance metrics."""
        try:
            # Display session stats every 10 cycles
            if self.session_stats['trades_executed'] > 0 and self.session_stats['trades_executed'] % 5 == 0:
                self._display_performance_summary()
                
        except Exception as e:
            self.logger.error(f"Error monitoring performance: {e}")
    
    def _display_performance_summary(self):
        """Display current performance summary."""
        try:
            overall_perf = self.analytics.get_overall_performance()
            
            print("\n" + "=" * 60)
            print("📊 ENHANCED TRADING BOT PERFORMANCE SUMMARY")
            print("=" * 60)
            print(f"🎯 Current Win Rate: {overall_perf.get('win_rate', 0):.1f}%")
            print(f"💰 Total P&L: ${overall_perf.get('total_pnl', 0):.2f}")
            print(f"📈 Total Trades: {overall_perf.get('total_trades', 0)}")
            print(f"🏆 Recent Win Rate: {overall_perf.get('recent_win_rate', 0):.1f}%")
            print(f"📊 Max Drawdown: {overall_perf.get('max_drawdown', 0):.1f}%")
            print("=" * 60)
            
            # Show best strategies
            best_strategies = self.analytics.identify_best_strategies()
            if best_strategies:
                print("🏆 TOP PERFORMING STRATEGIES:")
                for i, strategy in enumerate(best_strategies[:3], 1):
                    print(f"   {i}. {strategy['strategy']}: {strategy['win_rate']:.1f}% win rate")
            
            print("=" * 60 + "\n")
            
        except Exception as e:
            self.logger.error(f"Error displaying performance summary: {e}")
    
    def _get_trading_symbols(self) -> List[str]:
        """Get list of symbols to trade."""
        try:
            # Get altcoin symbols (excluding top 10 by market cap)
            all_symbols = self.client.get_futures_symbols()
            
            # Filter for USDT pairs and exclude major coins
            excluded_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT', 
                              'SOLUSDT', 'DOTUSDT', 'MATICUSDT', 'AVAXUSDT', 'LINKUSDT']
            
            altcoin_symbols = [s for s in all_symbols 
                             if s.endswith('USDT') and s not in excluded_symbols]
            
            # Limit to reasonable number for testing
            return altcoin_symbols[:15]  # Top 15 altcoins
            
        except Exception as e:
            self.logger.error(f"Error getting trading symbols: {e}")
            return ['ADAUSDT', 'SOLUSDT', 'LINKUSDT']  # Fallback
    
    def _shutdown(self):
        """Shutdown the bot gracefully."""
        try:
            self.is_running = False
            
            # Close any open positions
            if self.current_position:
                self.logger.info("🔄 Closing open position before shutdown...")
                current_price = self.client.get_current_price(self.current_position['symbol'])
                if current_price:
                    entry_price = self.current_position['entry_price']
                    quantity = self.current_position['quantity']
                    
                    if self.current_position['side'] == 'BUY':
                        pnl = (current_price - entry_price) * quantity
                        pnl_percentage = (current_price - entry_price) / entry_price * 100
                    else:
                        pnl = (entry_price - current_price) * quantity
                        pnl_percentage = (entry_price - current_price) / entry_price * 100
                    
                    hold_time = (datetime.now() - self.current_position['entry_time']).total_seconds() / 60
                    self._close_position("Bot shutdown", pnl, pnl_percentage, hold_time)
            
            # Generate final performance report
            print("\n" + self.analytics.generate_performance_report())
            
            self.logger.info("🛑 Enhanced Trading Bot shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

def main():
    """Main entry point for enhanced trading bot."""
    try:
        # Create and run enhanced bot
        bot = EnhancedTradingBot()
        bot.run()
        
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Critical error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
