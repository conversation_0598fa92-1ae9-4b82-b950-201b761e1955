"""
Quick test to verify DataFrame validation fixes
"""

import sys
import os
import pandas as pd
import numpy as np

sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_test_dataframe():
    """Create a test DataFrame for validation."""
    np.random.seed(42)
    
    dates = pd.date_range('2024-01-01', periods=50, freq='5min')
    
    data = pd.DataFrame({
        'timestamp': dates,
        'open': np.random.uniform(100, 105, 50),
        'high': np.random.uniform(105, 110, 50),
        'low': np.random.uniform(95, 100, 50),
        'close': np.random.uniform(100, 105, 50),
        'volume': np.random.uniform(1000, 5000, 50)
    })
    
    return data

def test_dataframe_validation():
    """Test DataFrame validation logic."""
    print("🧪 Testing DataFrame Validation Fixes")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        ("Valid DataFrame", create_test_dataframe()),
        ("None DataFrame", None),
        ("Empty DataFrame", pd.DataFrame()),
        ("Small DataFrame", create_test_dataframe().head(5))  # Less than 20 rows
    ]
    
    for case_name, data in test_cases:
        print(f"\n📊 Testing {case_name}:")
        
        # Test the validation logic we implemented
        if data is not None and not data.empty and len(data) >= 20:
            print(f"   ✅ Valid: {len(data)} rows")
            valid = True
        else:
            if data is None:
                print(f"   ❌ Invalid: None")
            elif data.empty:
                print(f"   ❌ Invalid: Empty DataFrame")
            else:
                print(f"   ❌ Invalid: Only {len(data)} rows (need 20+)")
            valid = False
        
        # Test that we don't get the ambiguous truth value error
        try:
            # This should not raise an error anymore
            result = data is not None and not data.empty and len(data) >= 20
            print(f"   ✅ No boolean evaluation error")
        except ValueError as e:
            if "ambiguous" in str(e):
                print(f"   ❌ Still has boolean evaluation error: {e}")
            else:
                print(f"   ❌ Other error: {e}")

def test_chart_analysis_with_mock_data():
    """Test chart analysis with mock data."""
    print("\n📊 Testing Chart Analysis with Mock Data")
    print("=" * 50)
    
    try:
        from chart_analysis_engine import ChartAnalysisEngine
        
        # Initialize engine
        config = {
            'support_resistance_periods': 20,
            'breakout_threshold': 0.005,
            'volume_surge_threshold': 1.5,
            'min_confidence_threshold': 70,
            'min_risk_reward_ratio': 1.5
        }
        
        engine = ChartAnalysisEngine(config)
        
        # Create test data
        data_5m = create_test_dataframe()
        data_15m = create_test_dataframe()
        data_1h = create_test_dataframe()
        
        # Add required columns
        for data in [data_5m, data_15m, data_1h]:
            data['RSI'] = 50 + 10 * np.sin(np.arange(len(data)) * 0.1)
        
        print(f"📊 Test Data Created:")
        print(f"   5m: {len(data_5m)} rows")
        print(f"   15m: {len(data_15m)} rows") 
        print(f"   1h: {len(data_1h)} rows")
        
        # Test comprehensive analysis
        analysis = engine.comprehensive_chart_analysis(data_5m, data_15m, data_1h, 'TESTUSDT')
        
        print(f"\n📊 Analysis Results:")
        print(f"   Signal: {analysis.signal}")
        print(f"   Confidence: {analysis.confidence:.1f}%")
        print(f"   Pattern: {analysis.pattern.value}")
        print(f"   Entry Reason: {analysis.entry_reason}")
        
        print(f"   ✅ Chart analysis completed without DataFrame errors")
        return True
        
    except Exception as e:
        print(f"   ❌ Chart analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run DataFrame validation tests."""
    print("🧪 DataFrame Validation Fix Test Suite")
    print("=" * 60)
    
    print("🎯 This test verifies that DataFrame boolean evaluation errors are fixed")
    print()
    
    # Run tests
    test_dataframe_validation()
    chart_success = test_chart_analysis_with_mock_data()
    
    print("\n" + "=" * 60)
    if chart_success:
        print("🎉 DATAFRAME VALIDATION FIXES SUCCESSFUL!")
        print("\n✅ Fixed Issues:")
        print("   ✅ DataFrame boolean evaluation errors")
        print("   ✅ Proper None/empty/length validation")
        print("   ✅ Chart analysis engine working")
        print("   ✅ Multi-timeframe data handling")
        
        print("\n🚀 Bot should now run without DataFrame errors!")
        print("   Run: python main.py")
    else:
        print("❌ Some DataFrame issues remain. Check the errors above.")

if __name__ == "__main__":
    main()
