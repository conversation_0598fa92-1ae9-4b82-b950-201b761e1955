"""
Scalping Fallback System for Enhanced Trading Algorithm
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ScalpingSignal:
    """Scalping signal result."""
    signal: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float  # 0-100
    entry_reason: str
    target_profit: float  # Expected profit in %
    stop_loss: float  # Stop loss in %
    hold_time_minutes: int  # Expected hold time

class ScalpingFallback:
    """
    Scalping fallback system for when main strategies don't find good signals.
    Uses simple, quick scalping techniques to capture small profits.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Scalping parameters
        self.min_price_movement = config.get('min_price_movement', 0.002)  # 0.2% minimum movement
        self.quick_profit_target = config.get('quick_profit_target', 0.005)  # 0.5% profit target
        self.scalp_stop_loss = config.get('scalp_stop_loss', 0.003)  # 0.3% stop loss
        self.max_hold_minutes = config.get('max_hold_minutes', 10)  # 10 minutes max hold
        self.volume_spike_threshold = config.get('volume_spike_threshold', 1.3)  # 1.3x volume spike
        
        # RSI scalping levels
        self.rsi_oversold_scalp = config.get('rsi_oversold_scalp', 35)  # Less strict than main
        self.rsi_overbought_scalp = config.get('rsi_overbought_scalp', 65)  # Less strict than main
        
        logger.info(f"Scalping Fallback initialized: {self.quick_profit_target*100:.1f}% profit target, {self.max_hold_minutes}min max hold")
    
    def detect_price_momentum(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Detect short-term price momentum for scalping."""
        try:
            if len(data) < 5:
                return 'HOLD', 0.0
            
            # Check recent price movement (last 3 periods)
            recent_prices = data['close'].tail(3).values
            price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            
            # Check if movement is significant enough for scalping
            if abs(price_change) < self.min_price_movement:
                return 'HOLD', 0.0
            
            # Determine direction and strength
            if price_change > 0:
                return 'BUY', min(price_change * 100, 2.0)  # Cap at 2% strength
            else:
                return 'SELL', min(abs(price_change) * 100, 2.0)
                
        except Exception as e:
            logger.error(f"Error detecting price momentum: {e}")
            return 'HOLD', 0.0
    
    def detect_volume_spike(self, data: pd.DataFrame) -> bool:
        """Detect volume spikes that might indicate scalping opportunities."""
        try:
            if 'volume' not in data.columns or len(data) < 10:
                return False
            
            # Compare recent volume to average
            recent_volume = data['volume'].tail(2).mean()
            avg_volume = data['volume'].tail(10).mean()
            
            return recent_volume > avg_volume * self.volume_spike_threshold
            
        except Exception as e:
            logger.error(f"Error detecting volume spike: {e}")
            return False
    
    def check_rsi_scalping_levels(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Check RSI for scalping opportunities with relaxed thresholds."""
        try:
            if 'RSI' not in data.columns or len(data) < 1:
                return 'HOLD', 0.0
            
            latest_rsi = data['RSI'].iloc[-1]
            
            # Relaxed RSI levels for scalping
            if latest_rsi < self.rsi_oversold_scalp:
                strength = (self.rsi_oversold_scalp - latest_rsi) / self.rsi_oversold_scalp * 100
                return 'BUY', min(strength, 80.0)
            elif latest_rsi > self.rsi_overbought_scalp:
                strength = (latest_rsi - self.rsi_overbought_scalp) / (100 - self.rsi_overbought_scalp) * 100
                return 'SELL', min(strength, 80.0)
            
            return 'HOLD', 0.0
            
        except Exception as e:
            logger.error(f"Error checking RSI scalping levels: {e}")
            return 'HOLD', 0.0
    
    def detect_support_resistance_bounce(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Detect bounces off support/resistance for scalping."""
        try:
            if len(data) < 20:
                return 'HOLD', 0.0
            
            # Calculate recent support and resistance
            recent_data = data.tail(20)
            support = recent_data['low'].min()
            resistance = recent_data['high'].max()
            current_price = data['close'].iloc[-1]
            
            # Calculate distance from support/resistance
            range_size = resistance - support
            if range_size == 0:
                return 'HOLD', 0.0
            
            support_distance = (current_price - support) / range_size
            resistance_distance = (resistance - current_price) / range_size
            
            # Look for bounces (price near support/resistance)
            bounce_threshold = 0.1  # Within 10% of support/resistance
            
            if support_distance < bounce_threshold:
                # Near support - potential bounce up
                strength = (bounce_threshold - support_distance) / bounce_threshold * 60
                return 'BUY', strength
            elif resistance_distance < bounce_threshold:
                # Near resistance - potential bounce down
                strength = (bounce_threshold - resistance_distance) / bounce_threshold * 60
                return 'SELL', strength
            
            return 'HOLD', 0.0
            
        except Exception as e:
            logger.error(f"Error detecting support/resistance bounce: {e}")
            return 'HOLD', 0.0
    
    def generate_scalping_signal(self, data: pd.DataFrame, symbol: str) -> ScalpingSignal:
        """Generate scalping signal using multiple quick techniques."""
        try:
            if len(data) < 10:
                return ScalpingSignal('HOLD', 0.0, 'Insufficient data', 0.0, 0.0, 0)
            
            signals = []
            reasons = []
            
            # 1. Price momentum scalping
            momentum_signal, momentum_strength = self.detect_price_momentum(data)
            if momentum_signal != 'HOLD':
                signals.append((momentum_signal, momentum_strength, 'Price momentum'))
            
            # 2. RSI scalping
            rsi_signal, rsi_strength = self.check_rsi_scalping_levels(data)
            if rsi_signal != 'HOLD':
                signals.append((rsi_signal, rsi_strength, 'RSI scalping'))
            
            # 3. Support/Resistance bounce
            bounce_signal, bounce_strength = self.detect_support_resistance_bounce(data)
            if bounce_signal != 'HOLD':
                signals.append((bounce_signal, bounce_strength, 'Support/Resistance bounce'))
            
            # 4. Volume spike confirmation
            volume_spike = self.detect_volume_spike(data)
            
            if not signals:
                return ScalpingSignal('HOLD', 0.0, 'No scalping opportunities', 0.0, 0.0, 0)
            
            # Find strongest signal
            best_signal = max(signals, key=lambda x: x[1])
            signal_type, strength, reason = best_signal
            
            # Boost confidence if volume spike present
            confidence = strength
            if volume_spike:
                confidence = min(confidence * 1.2, 90.0)
                reason += ' + Volume spike'
            
            # Set scalping parameters
            target_profit = self.quick_profit_target * 100  # Convert to percentage
            stop_loss = self.scalp_stop_loss * 100
            hold_time = self.max_hold_minutes
            
            return ScalpingSignal(
                signal=signal_type,
                confidence=confidence,
                entry_reason=reason,
                target_profit=target_profit,
                stop_loss=stop_loss,
                hold_time_minutes=hold_time
            )
            
        except Exception as e:
            logger.error(f"Error generating scalping signal for {symbol}: {e}")
            return ScalpingSignal('HOLD', 0.0, f'Error: {e}', 0.0, 0.0, 0)
    
    def should_activate_scalping(self, main_signal_rejected: bool, market_regime: str, 
                               time_since_last_trade: int) -> bool:
        """Determine if scalping mode should be activated."""
        try:
            # Activate scalping if:
            # 1. Main signal was rejected due to quality
            # 2. Market is in sideways/ranging mode
            # 3. Haven't traded in a while
            
            activate_conditions = [
                main_signal_rejected,
                market_regime in ['sideways_ranging', 'low_volatility'],
                time_since_last_trade > 30  # 30 minutes since last trade
            ]
            
            # Activate if any condition is met
            should_activate = any(activate_conditions)
            
            if should_activate:
                reasons = []
                if main_signal_rejected:
                    reasons.append("Main signal rejected")
                if market_regime in ['sideways_ranging', 'low_volatility']:
                    reasons.append(f"Market regime: {market_regime}")
                if time_since_last_trade > 30:
                    reasons.append(f"No trades for {time_since_last_trade}min")
                
                logger.info(f"🎯 SCALPING MODE ACTIVATED: {', '.join(reasons)}")
            
            return should_activate
            
        except Exception as e:
            logger.error(f"Error checking scalping activation: {e}")
            return False
    
    def get_scalping_position_size_multiplier(self) -> float:
        """Get position size multiplier for scalping (smaller positions)."""
        # Use smaller positions for scalping due to higher frequency
        return 0.7  # 70% of normal position size
    
    def log_scalping_decision(self, signal: ScalpingSignal, symbol: str):
        """Log scalping decision details."""
        if signal.signal != 'HOLD':
            logger.info(f"🎯 SCALPING SIGNAL for {symbol}:")
            logger.info(f"   Signal: {signal.signal}")
            logger.info(f"   Confidence: {signal.confidence:.1f}%")
            logger.info(f"   Reason: {signal.entry_reason}")
            logger.info(f"   Target: +{signal.target_profit:.2f}%")
            logger.info(f"   Stop Loss: -{signal.stop_loss:.2f}%")
            logger.info(f"   Max Hold: {signal.hold_time_minutes} minutes")
        else:
            logger.info(f"🎯 SCALPING: No opportunity for {symbol} - {signal.entry_reason}")
    
    def is_scalping_time_valid(self) -> bool:
        """Check if current time is good for scalping (avoid low liquidity periods)."""
        try:
            import datetime
            
            # Get current UTC time
            now = datetime.datetime.utcnow()
            hour = now.hour
            
            # Avoid scalping during low liquidity hours (typically 22:00-02:00 UTC)
            low_liquidity_hours = list(range(22, 24)) + list(range(0, 3))
            
            return hour not in low_liquidity_hours
            
        except Exception as e:
            logger.error(f"Error checking scalping time: {e}")
            return True  # Default to allow scalping
