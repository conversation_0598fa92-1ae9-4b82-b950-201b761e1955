"""
Display all available altcoins on Binance Futures.
Shows comprehensive information about available trading pairs.
"""

import os
import sys
import pandas as pd
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from altcoin_scanner import AltcoinScanner
from binance_client import BinanceClient
from utils import setup_logging

def display_altcoin_info():
    """Display comprehensive altcoin information."""
    print("🔍 Binance Futures Altcoin Scanner")
    print("=" * 80)
    
    # Load environment variables
    load_dotenv()
    
    # Setup logging
    setup_logging(log_level="INFO", log_file="logs/altcoin_scan.log")
    
    # Initialize Binance client
    client = BinanceClient()
    
    # Initialize altcoin scanner
    scanner_config = {
        'min_volume_24h': 100000,   # $100K minimum volume (lower for more coins)
        'min_price': 0.0001,        # Minimum price
        'max_price': 10000,         # Maximum price
        'quote_asset': 'USDT',      # Only USDT pairs
        'scan_interval_hours': 24   # Rescan daily
    }
    
    scanner = AltcoinScanner(client, scanner_config)
    
    print("Scanning Binance Futures for all available altcoins...")
    print("This may take a moment...\n")
    
    # Get all altcoins
    altcoins = scanner.scan_altcoins(force_rescan=True)
    
    if not altcoins:
        print("❌ No altcoins found")
        return
    
    # Get detailed information
    ticker_stats = scanner.get_24h_ticker_stats()
    
    print(f"✅ Found {len(altcoins)} qualifying altcoins")
    print("=" * 80)
    
    # Display summary
    summary = scanner.get_altcoin_summary()
    print("📊 SUMMARY")
    print("-" * 40)
    print(f"Total Altcoins: {summary['total_altcoins']}")
    print(f"Total 24h Volume: ${summary['total_volume_24h']:,.2f}")
    print(f"Average Price Change: {summary['avg_price_change_24h']:.2f}%")
    print(f"Excluded Assets: {', '.join(summary['excluded_assets'])}")
    print(f"Min Volume Filter: ${summary['min_volume_filter']:,}")
    print(f"Price Range: {summary['price_range']}")
    print()
    
    # Display top altcoins
    print("🏆 TOP 20 ALTCOINS BY VOLUME")
    print("-" * 80)
    print(f"{'Rank':<4} {'Symbol':<12} {'Price':<12} {'24h Volume':<15} {'24h Change':<12} {'Trades':<8}")
    print("-" * 80)
    
    for i, symbol in enumerate(altcoins[:20], 1):
        info = scanner.get_altcoin_info(symbol, ticker_stats)
        
        price = f"${info['price']:.6f}"
        volume = f"${info['volume_24h']:,.0f}"
        change = f"{info['price_change_24h']:+.2f}%"
        trades = f"{info['trades_24h']:,}"
        
        print(f"{i:<4} {symbol:<12} {price:<12} {volume:<15} {change:<12} {trades:<8}")
    
    print()
    
    # Display volume categories
    print("📈 VOLUME CATEGORIES")
    print("-" * 40)
    
    volume_categories = {
        'Ultra High (>$100M)': [],
        'High ($10M-$100M)': [],
        'Medium ($1M-$10M)': [],
        'Low ($100K-$1M)': []
    }
    
    for symbol in altcoins:
        info = scanner.get_altcoin_info(symbol, ticker_stats)
        volume = info['volume_24h']
        
        if volume > 100_000_000:
            volume_categories['Ultra High (>$100M)'].append(symbol)
        elif volume > 10_000_000:
            volume_categories['High ($10M-$100M)'].append(symbol)
        elif volume > 1_000_000:
            volume_categories['Medium ($1M-$10M)'].append(symbol)
        else:
            volume_categories['Low ($100K-$1M)'].append(symbol)
    
    for category, symbols in volume_categories.items():
        print(f"{category}: {len(symbols)} coins")
        if symbols:
            print(f"  Examples: {', '.join(symbols[:5])}")
        print()
    
    # Display price change distribution
    print("📊 PRICE CHANGE DISTRIBUTION (24h)")
    print("-" * 40)
    
    price_changes = []
    for symbol in altcoins:
        info = scanner.get_altcoin_info(symbol, ticker_stats)
        price_changes.append(info['price_change_24h'])
    
    gainers = [p for p in price_changes if p > 5]
    moderate_gainers = [p for p in price_changes if 0 < p <= 5]
    moderate_losers = [p for p in price_changes if -5 <= p < 0]
    losers = [p for p in price_changes if p < -5]
    
    print(f"Strong Gainers (>+5%): {len(gainers)} coins")
    print(f"Moderate Gainers (0% to +5%): {len(moderate_gainers)} coins")
    print(f"Moderate Losers (-5% to 0%): {len(moderate_losers)} coins")
    print(f"Strong Losers (<-5%): {len(losers)} coins")
    print()
    
    # Show biggest movers
    altcoins_with_change = [(symbol, scanner.get_altcoin_info(symbol, ticker_stats)['price_change_24h']) 
                           for symbol in altcoins]
    
    biggest_gainers = sorted(altcoins_with_change, key=lambda x: x[1], reverse=True)[:5]
    biggest_losers = sorted(altcoins_with_change, key=lambda x: x[1])[:5]
    
    print("🚀 BIGGEST GAINERS (24h)")
    print("-" * 30)
    for symbol, change in biggest_gainers:
        print(f"{symbol}: +{change:.2f}%")
    
    print("\n📉 BIGGEST LOSERS (24h)")
    print("-" * 30)
    for symbol, change in biggest_losers:
        print(f"{symbol}: {change:.2f}%")
    
    print()
    
    # Export options
    print("💾 EXPORT OPTIONS")
    print("-" * 40)
    
    export_choice = input("Export altcoin list to file? (y/N): ").lower()
    if export_choice == 'y':
        # Export to text file
        scanner.export_altcoin_list('all_altcoins.txt')
        
        # Export to CSV with details
        altcoin_data = []
        for symbol in altcoins:
            info = scanner.get_altcoin_info(symbol, ticker_stats)
            altcoin_data.append(info)
        
        df = pd.DataFrame(altcoin_data)
        df.to_csv('altcoins_detailed.csv', index=False)
        
        print("✅ Exported to:")
        print("   - all_altcoins.txt (symbol list)")
        print("   - altcoins_detailed.csv (detailed data)")
    
    print("\n🔧 CONFIGURATION TIPS")
    print("-" * 40)
    print("To use all these altcoins in your trading bot:")
    print("1. Set 'use_dynamic_scanning: true' in config/config.yaml")
    print("2. Adjust 'max_symbols' to control how many to trade")
    print("3. Run 'python update_altcoins.py' to update config")
    print("4. Start the bot with 'python main.py'")
    
    print(f"\n📝 Total altcoins available: {len(altcoins)}")
    print("The bot will automatically select the most liquid ones for trading.")

def main():
    """Main function."""
    try:
        display_altcoin_info()
    except KeyboardInterrupt:
        print("\nScan cancelled by user.")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
