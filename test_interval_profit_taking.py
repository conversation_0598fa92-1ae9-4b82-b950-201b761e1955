"""
Test script to verify interval-based profit taking system.
"""

import os
import sys
from dotenv import load_dotenv
import pandas as pd
from datetime import timedelta

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import load_config

def test_profit_intervals():
    """Test the interval-based profit taking logic."""
    print("⏰ Testing Interval-Based Profit Taking")
    print("=" * 60)
    
    try:
        # Load config
        config = load_config('config/config.yaml')
        risk_config = config.get('risk_management', {})
        
        # Get interval settings
        min_profit_hold = risk_config.get('min_profit_hold_minutes', 5)
        breakeven_hold = risk_config.get('breakeven_hold_minutes', 15)
        max_hold = risk_config.get('max_hold_minutes', 30)
        max_loss = risk_config.get('max_acceptable_loss', 2.0)
        
        print(f"📊 Interval Configuration:")
        print(f"   Stage 1: Take profit after {min_profit_hold} minutes (if profit > 0)")
        print(f"   Stage 2: Breakeven exit after {breakeven_hold} minutes (if loss < ${max_loss})")
        print(f"   Stage 3: Force close after {max_hold} minutes (regardless of P&L)")
        
        # Test scenarios
        test_scenarios = [
            {
                'name': 'Quick profit - should close at 5min',
                'time_held': 6,
                'profit': 1.5,
                'expected_action': 'CLOSE',
                'expected_reason': 'profit-taking'
            },
            {
                'name': 'Small loss early - should hold',
                'time_held': 3,
                'profit': -0.5,
                'expected_action': 'HOLD',
                'expected_reason': 'too early'
            },
            {
                'name': 'Acceptable loss at 15min - should close',
                'time_held': 16,
                'profit': -1.0,
                'expected_action': 'CLOSE',
                'expected_reason': 'breakeven exit'
            },
            {
                'name': 'Large loss at 15min - should hold',
                'time_held': 16,
                'profit': -3.0,
                'expected_action': 'HOLD',
                'expected_reason': 'loss too large'
            },
            {
                'name': 'Force close at 30min - any P&L',
                'time_held': 31,
                'profit': -5.0,
                'expected_action': 'CLOSE',
                'expected_reason': 'force close'
            }
        ]
        
        all_passed = True
        
        for scenario in test_scenarios:
            print(f"\n📊 {scenario['name']}:")
            
            time_held = scenario['time_held']
            profit = scenario['profit']
            
            # Apply the same logic as the bot
            should_close = False
            close_reason = ""
            
            # Stage 1: After min_profit_hold_minutes, take any profit
            if time_held >= min_profit_hold:
                if profit > 0:
                    should_close = True
                    close_reason = "profit-taking"
            
            # Stage 2: After breakeven_hold_minutes, close if loss is acceptable
            if time_held >= breakeven_hold:
                if profit > -max_loss:
                    should_close = True
                    close_reason = "breakeven exit"
            
            # Stage 3: After max_hold_minutes, force close
            if time_held >= max_hold:
                should_close = True
                close_reason = "force close"
            
            action = "CLOSE" if should_close else "HOLD"
            
            print(f"   Time held: {time_held} minutes")
            print(f"   Profit: {profit:+.2f} USDT")
            print(f"   Action: {action}")
            print(f"   Reason: {close_reason if close_reason else 'waiting'}")
            
            # Check if result matches expectation
            if action == scenario['expected_action']:
                print(f"   ✅ CORRECT - Action matches expected")
            else:
                print(f"   ❌ INCORRECT - Expected {scenario['expected_action']}, got {action}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing profit intervals: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_division_by_zero_fix():
    """Test that division by zero errors are handled."""
    print("\n🛡️ Testing Division by Zero Protection")
    print("=" * 60)
    
    print("📋 Testing edge cases that could cause division by zero:")
    
    test_cases = [
        {
            'name': 'Zero entry price',
            'entry_price': 0.0,
            'current_price': 1.0,
            'quantity': 100.0,
            'should_skip': True
        },
        {
            'name': 'Zero current price',
            'entry_price': 1.0,
            'current_price': 0.0,
            'quantity': 100.0,
            'should_skip': True
        },
        {
            'name': 'Zero quantity',
            'entry_price': 1.0,
            'current_price': 1.1,
            'quantity': 0.0,
            'should_skip': True
        },
        {
            'name': 'Negative entry price',
            'entry_price': -1.0,
            'current_price': 1.0,
            'quantity': 100.0,
            'should_skip': True
        },
        {
            'name': 'Valid values',
            'entry_price': 1.0,
            'current_price': 1.05,
            'quantity': 100.0,
            'should_skip': False
        }
    ]
    
    all_passed = True
    
    for test_case in test_cases:
        print(f"\n📊 {test_case['name']}:")
        
        entry_price = test_case['entry_price']
        current_price = test_case['current_price']
        quantity = test_case['quantity']
        
        print(f"   Entry: {entry_price}, Current: {current_price}, Qty: {quantity}")
        
        # Apply the same safety checks as the bot
        should_skip = (entry_price <= 0 or current_price <= 0 or quantity <= 0)
        
        if should_skip:
            print(f"   🛡️  SKIPPED - Invalid data detected")
        else:
            try:
                # Calculate profit (same logic as bot)
                price_diff = current_price - entry_price  # Assuming BUY
                profit_usdt = (price_diff / entry_price) * (quantity * entry_price) * 3  # 3x leverage
                print(f"   💰 Profit calculated: {profit_usdt:+.2f} USDT")
            except ZeroDivisionError:
                print(f"   ❌ Division by zero error occurred")
                should_skip = True
        
        # Check if result matches expectation
        if should_skip == test_case['should_skip']:
            print(f"   ✅ CORRECT - Handling matches expected")
        else:
            print(f"   ❌ INCORRECT - Expected skip={test_case['should_skip']}, got skip={should_skip}")
            all_passed = False
    
    return all_passed

def show_profit_taking_strategy():
    """Show the profit taking strategy."""
    print("\n📈 Interval-Based Profit Taking Strategy")
    print("=" * 60)
    
    print("🎯 STRATEGY OVERVIEW:")
    print("   Instead of immediate profit-taking, use time-based intervals")
    print("   This allows positions to develop and capture larger moves")
    print()
    
    print("⏰ TIME-BASED STAGES:")
    print("   Stage 1 (0-5 min):  Hold position, let it develop")
    print("   Stage 2 (5+ min):   Take any profit > $0")
    print("   Stage 3 (15+ min):  Close if loss < $2 (damage control)")
    print("   Stage 4 (30+ min):  Force close regardless of P&L")
    print()
    
    print("💡 BENEFITS:")
    print("   • Prevents premature exits on small profits")
    print("   • Allows trends to develop fully")
    print("   • Limits maximum loss exposure")
    print("   • Prevents positions from running indefinitely")
    print()
    
    print("🛡️ RISK MANAGEMENT:")
    print("   • Maximum hold time: 30 minutes")
    print("   • Maximum acceptable loss: $2")
    print("   • Automatic position cleanup")
    print("   • No division by zero errors")

def main():
    """Run interval-based profit taking tests."""
    print("🧪 Interval-Based Profit Taking Test Suite")
    print("=" * 80)
    
    show_profit_taking_strategy()
    
    tests = [
        ("Profit Intervals", test_profit_intervals),
        ("Division by Zero Fix", test_division_by_zero_fix)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 INTERVAL-BASED PROFIT TAKING WORKING!")
        print("\n✅ Fixes Applied:")
        print("   ✅ Division by zero errors fixed")
        print("   ✅ Interval-based profit taking implemented")
        print("   ✅ Configurable time intervals")
        print("   ✅ Progressive exit strategy")
        print("   ✅ Force close after 30 minutes")
        print("   ✅ Better error handling")
        
        print("\n🎯 Expected Bot Behavior:")
        print("   • Holds positions for 5+ minutes before profit-taking")
        print("   • Takes any profit after 5 minutes")
        print("   • Closes small losses after 15 minutes")
        print("   • Force closes all positions after 30 minutes")
        print("   • No more division by zero errors")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
