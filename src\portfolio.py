"""
Portfolio management module for tracking positions and performance.
"""

import logging
from typing import Dict, List, Optional
import pandas as pd
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class Portfolio:
    """Portfolio manager for tracking positions and performance."""
    
    def __init__(self, initial_balance: float = 10000.0, base_currency: str = 'USDT'):
        """
        Initialize portfolio.
        
        Args:
            initial_balance: Initial portfolio balance
            base_currency: Base currency for calculations
        """
        self.initial_balance = initial_balance
        self.base_currency = base_currency
        self.positions = {}  # Active positions
        self.trade_history = []  # Completed trades
        self.balance_history = []  # Balance over time
        
        # Current balances
        self.balances = {base_currency: initial_balance}
        
        # Performance metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        
        logger.info(f"Portfolio initialized with {initial_balance:.2f} {base_currency}")

    def sync_balance(self, actual_balance: float):
        """
        Sync portfolio balance with actual account balance.

        Args:
            actual_balance: Actual balance from exchange account
        """
        old_balance = self.balances.get(self.base_currency, 0.0)
        self.balances[self.base_currency] = actual_balance

        logger.info(f"Portfolio balance synced: {old_balance:.2f} -> {actual_balance:.2f} {self.base_currency}")

    def add_position(self, symbol: str, side: str, quantity: float,
                    entry_price: float, timestamp: datetime = None):
        """
        Add a new position to the portfolio.
        
        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL'
            quantity: Position quantity
            entry_price: Entry price
            timestamp: Position timestamp
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        position = {
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            'entry_price': entry_price,
            'timestamp': timestamp,
            'unrealized_pnl': 0.0
        }
        
        self.positions[symbol] = position
        logger.info(f"Position added: {side} {quantity} {symbol} at {entry_price}")
    
    def close_position(self, symbol: str, exit_price: float, timestamp: datetime = None):
        """
        Close a position and record the trade.
        
        Args:
            symbol: Trading symbol
            exit_price: Exit price
            timestamp: Exit timestamp
        """
        if symbol not in self.positions:
            logger.warning(f"No position found for {symbol}")
            return
        
        if timestamp is None:
            timestamp = datetime.now()
        
        position = self.positions[symbol]
        
        # Calculate realized P&L
        realized_pnl = self.calculate_realized_pnl(position, exit_price)
        
        # Create trade record
        trade = {
            'symbol': symbol,
            'side': position['side'],
            'quantity': position['quantity'],
            'entry_price': position['entry_price'],
            'exit_price': exit_price,
            'entry_time': position['timestamp'],
            'exit_time': timestamp,
            'realized_pnl': realized_pnl,
            'duration': timestamp - position['timestamp']
        }
        
        # Update trade history
        self.trade_history.append(trade)
        
        # Update performance metrics
        self.total_trades += 1
        self.total_pnl += realized_pnl
        
        if realized_pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # Update balance
        self.balances[self.base_currency] += realized_pnl
        
        # Remove position
        del self.positions[symbol]
        
        logger.info(f"Position closed: {symbol} P&L: {realized_pnl:.4f}")
    
    def update_position_pnl(self, symbol: str, current_price: float):
        """
        Update unrealized P&L for a position.
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
        """
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        unrealized_pnl = self.calculate_unrealized_pnl(position, current_price)
        position['unrealized_pnl'] = unrealized_pnl
    
    def calculate_unrealized_pnl(self, position: Dict, current_price: float) -> float:
        """Calculate unrealized P&L for a position."""
        entry_price = position['entry_price']
        quantity = position['quantity']
        side = position['side']
        
        if side == 'BUY':
            return (current_price - entry_price) * quantity
        else:
            return (entry_price - current_price) * quantity
    
    def calculate_realized_pnl(self, position: Dict, exit_price: float) -> float:
        """Calculate realized P&L for a closed position."""
        entry_price = position['entry_price']
        quantity = position['quantity']
        side = position['side']
        
        if side == 'BUY':
            return (exit_price - entry_price) * quantity
        else:
            return (entry_price - exit_price) * quantity
    
    def get_total_value(self, current_prices: Dict[str, float] = None) -> float:
        """
        Calculate total portfolio value.
        
        Args:
            current_prices: Current prices for assets
            
        Returns:
            Total portfolio value in base currency
        """
        total_value = self.balances.get(self.base_currency, 0.0)
        
        # Add unrealized P&L from open positions
        if current_prices:
            for symbol, position in self.positions.items():
                if symbol in current_prices:
                    unrealized_pnl = self.calculate_unrealized_pnl(
                        position, current_prices[symbol]
                    )
                    total_value += unrealized_pnl
        
        return total_value
    
    def get_performance_metrics(self) -> Dict:
        """Get portfolio performance metrics."""
        if self.total_trades == 0:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'total_return': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0
            }
        
        # Calculate metrics
        win_rate = self.winning_trades / self.total_trades
        total_return = (self.total_pnl / self.initial_balance) * 100
        
        # Calculate average win/loss
        winning_trades = [t for t in self.trade_history if t['realized_pnl'] > 0]
        losing_trades = [t for t in self.trade_history if t['realized_pnl'] < 0]
        
        avg_win = sum(t['realized_pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(t['realized_pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0
        
        # Calculate profit factor
        gross_profit = sum(t['realized_pnl'] for t in winning_trades)
        gross_loss = abs(sum(t['realized_pnl'] for t in losing_trades))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        
        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': win_rate,
            'total_pnl': self.total_pnl,
            'total_return': total_return,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'current_balance': self.balances.get(self.base_currency, 0.0)
        }
    
    def get_positions_summary(self) -> List[Dict]:
        """Get summary of current positions."""
        return [
            {
                'symbol': symbol,
                'side': pos['side'],
                'quantity': pos['quantity'],
                'entry_price': pos['entry_price'],
                'unrealized_pnl': pos['unrealized_pnl'],
                'timestamp': pos['timestamp']
            }
            for symbol, pos in self.positions.items()
        ]
    
    def save_to_file(self, filename: str):
        """Save portfolio state to file."""
        data = {
            'initial_balance': self.initial_balance,
            'base_currency': self.base_currency,
            'balances': self.balances,
            'positions': {k: {**v, 'timestamp': v['timestamp'].isoformat()} 
                         for k, v in self.positions.items()},
            'trade_history': [
                {**trade, 
                 'entry_time': trade['entry_time'].isoformat(),
                 'exit_time': trade['exit_time'].isoformat(),
                 'duration': str(trade['duration'])}
                for trade in self.trade_history
            ],
            'performance': self.get_performance_metrics()
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"Portfolio saved to {filename}")
    
    def load_from_file(self, filename: str):
        """Load portfolio state from file."""
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
            
            self.initial_balance = data['initial_balance']
            self.base_currency = data['base_currency']
            self.balances = data['balances']
            
            # Restore positions
            self.positions = {}
            for symbol, pos in data['positions'].items():
                pos['timestamp'] = datetime.fromisoformat(pos['timestamp'])
                self.positions[symbol] = pos
            
            # Restore trade history
            self.trade_history = []
            for trade in data['trade_history']:
                trade['entry_time'] = datetime.fromisoformat(trade['entry_time'])
                trade['exit_time'] = datetime.fromisoformat(trade['exit_time'])
                trade['duration'] = pd.Timedelta(trade['duration'])
                self.trade_history.append(trade)
            
            # Recalculate metrics
            self.total_trades = len(self.trade_history)
            self.winning_trades = len([t for t in self.trade_history if t['realized_pnl'] > 0])
            self.losing_trades = len([t for t in self.trade_history if t['realized_pnl'] < 0])
            self.total_pnl = sum(t['realized_pnl'] for t in self.trade_history)
            
            logger.info(f"Portfolio loaded from {filename}")
            
        except Exception as e:
            logger.error(f"Error loading portfolio from {filename}: {e}")
