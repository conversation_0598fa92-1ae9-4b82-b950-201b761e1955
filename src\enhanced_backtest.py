"""
Enhanced Backtesting Framework for Futures Trading Bot.
Tests strategies with real historical altcoin data following context.txt guidelines.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# Import our enhanced components
from binance_client import BinanceClient
from strategies.enhanced_futures_strategy import EnhancedFuturesStrategy
from risk_management import RiskManager
from profit_manager import ProfitManager
from market_monitor import MarketMonitor

logger = logging.getLogger(__name__)

class EnhancedBacktester:
    """
    Enhanced backtesting framework that:
    - Tests strategies on real historical altcoin data
    - Simulates market conditions and risk management
    - Validates context.txt rules implementation
    - Provides comprehensive performance analysis
    """
    
    def __init__(self, config: Dict):
        """
        Initialize enhanced backtester.
        
        Args:
            config: Backtesting configuration
        """
        self.config = config
        
        # Backtesting parameters
        self.initial_balance = config.get('initial_balance', 10000)
        self.start_date = config.get('start_date', '2024-01-01')
        self.end_date = config.get('end_date', '2024-12-01')
        self.timeframe = config.get('timeframe', '5m')
        
        # Test symbols (altcoins as per context.txt)
        self.test_symbols = config.get('test_symbols', [
            'ADAUSDT', 'DOTUSDT', 'LINKUSDT', 'LTCUSDT', 'XLMUSDT',
            'XRPUSDT', 'SOLUSDT', 'AVAXUSDT', 'MATICUSDT', 'ALGOUSDT'
        ])
        
        # Initialize components
        self.client = BinanceClient(testnet=False)  # Use real data
        
        # Strategy configuration following context.txt
        strategy_config = {
            'ema_fast_period': 12,
            'ema_slow_period': 26,
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'volume_spike_threshold': 1.5,
            'cooldown_minutes': 15,  # 15-30 min cooldown from context.txt
            'require_macd_confirmation': True,
            'avoid_low_volume_hours': True
        }
        
        self.strategy = EnhancedFuturesStrategy(self.client, strategy_config)
        
        # Risk management following context.txt
        risk_config = {
            'max_position_size': 0.05,  # 5% max position size
            'min_position_size': 0.02,  # 2% min position size
            'stop_loss_percentage': 0.015,  # 1.5% stop loss
            'max_leverage': 5,  # Max 5x leverage
            'max_daily_loss': 0.05,  # 5% daily loss limit
            'trade_cooldown_minutes': 15,
            'market_pause_duration': 30
        }
        
        self.risk_manager = RiskManager(risk_config)
        
        # Profit management
        profit_config = {
            'trailing_stop_enabled': True,
            'trailing_stop_percentage': 0.005,  # 0.5%
            'break_even_enabled': True,
            'break_even_trigger_profit': 0.015,  # 1.5%
            'partial_profit_enabled': False  # Disabled for context.txt rule compliance
        }
        
        self.profit_manager = ProfitManager(profit_config)
        
        # Backtesting state
        self.current_balance = self.initial_balance
        self.positions = {}
        self.trades = []
        self.equity_curve = []
        self.daily_returns = []
        
        logger.info("Enhanced Backtester initialized")
    
    def get_historical_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Get historical data for backtesting.
        
        Args:
            symbol: Trading symbol
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            Historical OHLCV data
        """
        try:
            # Convert dates to timestamps
            start_ts = int(pd.Timestamp(start_date).timestamp() * 1000)
            end_ts = int(pd.Timestamp(end_date).timestamp() * 1000)
            
            # Get data in chunks (Binance API limit)
            all_data = []
            current_start = start_ts
            
            while current_start < end_ts:
                # Get 1000 candles at a time
                klines = self.client.client.get_historical_klines(
                    symbol=symbol,
                    interval=self.timeframe,
                    start_str=current_start,
                    limit=1000
                )
                
                if not klines:
                    break
                
                # Convert to DataFrame
                df = pd.DataFrame(klines, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_asset_volume', 'number_of_trades',
                    'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
                ])
                
                # Convert data types
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    df[col] = df[col].astype(float)
                
                all_data.append(df[['timestamp', 'open', 'high', 'low', 'close', 'volume']])
                
                # Update start for next chunk
                current_start = int(df['timestamp'].iloc[-1].timestamp() * 1000) + 1
                
                # Break if we've reached the end date
                if current_start >= end_ts:
                    break
            
            if all_data:
                combined_data = pd.concat(all_data, ignore_index=True)
                combined_data.set_index('timestamp', inplace=True)
                
                # Filter by date range
                combined_data = combined_data[
                    (combined_data.index >= start_date) & 
                    (combined_data.index <= end_date)
                ]
                
                logger.info(f"Retrieved {len(combined_data)} candles for {symbol}")
                return combined_data
            
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    def simulate_trade(self, symbol: str, side: str, price: float, quantity: float, timestamp: pd.Timestamp) -> Dict:
        """
        Simulate a trade execution.
        
        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL'
            price: Execution price
            quantity: Trade quantity
            timestamp: Trade timestamp
            
        Returns:
            Trade result dictionary
        """
        # Calculate position value
        position_value = quantity * price
        
        # Apply leverage (max 5x as per context.txt)
        leverage = min(5, self.risk_manager.max_leverage)
        
        # Calculate fees (approximate Binance futures fees)
        fee_rate = 0.0004  # 0.04% maker/taker fee
        fees = position_value * fee_rate
        
        # Update balance
        if side == 'BUY':
            required_margin = position_value / leverage
            if required_margin + fees > self.current_balance:
                return {'success': False, 'reason': 'Insufficient balance'}
            
            self.current_balance -= (required_margin + fees)
            
            # Add position
            self.positions[symbol] = {
                'side': side,
                'quantity': quantity,
                'entry_price': price,
                'entry_time': timestamp,
                'leverage': leverage,
                'margin_used': required_margin
            }
            
            # Initialize profit manager tracking
            self.profit_manager.initialize_position_tracking(symbol, price, side, quantity)
            
        else:  # SELL (close position)
            if symbol not in self.positions:
                return {'success': False, 'reason': 'No position to close'}
            
            position = self.positions[symbol]
            
            # Calculate P&L
            pnl = (price - position['entry_price']) * position['quantity']
            pnl_with_leverage = pnl * position['leverage']
            
            # Update balance
            self.current_balance += (position['margin_used'] + pnl_with_leverage - fees)
            
            # Record trade
            trade = {
                'symbol': symbol,
                'entry_time': position['entry_time'],
                'exit_time': timestamp,
                'side': position['side'],
                'quantity': position['quantity'],
                'entry_price': position['entry_price'],
                'exit_price': price,
                'leverage': position['leverage'],
                'pnl': pnl_with_leverage,
                'pnl_percentage': (pnl_with_leverage / position['margin_used']) * 100,
                'fees': fees * 2,  # Entry + exit fees
                'duration_minutes': (timestamp - position['entry_time']).total_seconds() / 60
            }
            
            self.trades.append(trade)
            
            # Update risk manager
            self.risk_manager.update_daily_pnl(pnl_with_leverage)
            
            # Remove position
            del self.positions[symbol]
            self.profit_manager.remove_position_tracking(symbol)
        
        return {'success': True, 'fees': fees}
    
    def run_backtest(self) -> Dict:
        """
        Run comprehensive backtest on all symbols.
        
        Returns:
            Backtest results dictionary
        """
        logger.info(f"Starting enhanced backtest from {self.start_date} to {self.end_date}")
        
        # Get historical data for all symbols
        historical_data = {}
        for symbol in self.test_symbols:
            data = self.get_historical_data(symbol, self.start_date, self.end_date)
            if not data.empty:
                historical_data[symbol] = data
                logger.info(f"Loaded {len(data)} candles for {symbol}")
            else:
                logger.warning(f"No data available for {symbol}")
        
        if not historical_data:
            logger.error("No historical data available for backtesting")
            return {}
        
        # Find common time range
        start_times = [data.index[0] for data in historical_data.values()]
        end_times = [data.index[-1] for data in historical_data.values()]
        
        backtest_start = max(start_times)
        backtest_end = min(end_times)
        
        logger.info(f"Backtesting period: {backtest_start} to {backtest_end}")
        
        # Run simulation
        current_time = backtest_start
        step = pd.Timedelta(minutes=5)  # 5-minute intervals
        
        while current_time <= backtest_end:
            # Process each symbol
            for symbol, data in historical_data.items():
                try:
                    # Get data up to current time
                    current_data = data[data.index <= current_time].tail(100)  # Last 100 candles
                    
                    if len(current_data) < 50:  # Need enough data for indicators
                        continue
                    
                    current_price = current_data['close'].iloc[-1]
                    
                    # Check existing position
                    if symbol in self.positions:
                        # Check exit conditions (context.txt rule: exit as soon as profitable)
                        position = self.positions[symbol]
                        pnl = (current_price - position['entry_price']) * position['quantity']
                        
                        if pnl > 0:  # Exit immediately when profitable
                            result = self.simulate_trade(symbol, 'SELL', current_price, position['quantity'], current_time)
                            if result['success']:
                                logger.info(f"Position closed (profit): {symbol} at {current_price:.6f}")
                        
                        # Check stop loss (-1.5% as per context.txt)
                        elif pnl / (position['entry_price'] * position['quantity']) <= -0.015:
                            result = self.simulate_trade(symbol, 'SELL', current_price, position['quantity'], current_time)
                            if result['success']:
                                logger.info(f"Position closed (stop loss): {symbol} at {current_price:.6f}")
                    
                    else:
                        # Check for entry signals
                        if not self.risk_manager.should_stop_trading():
                            signal = self.strategy.generate_signal(current_data, symbol)
                            
                            if signal == 'BUY':
                                # Calculate position size (2-5% of balance as per context.txt)
                                position_size_pct = 0.05  # 5% position size
                                position_value = self.current_balance * position_size_pct
                                quantity = position_value / current_price
                                
                                result = self.simulate_trade(symbol, 'BUY', current_price, quantity, current_time)
                                if result['success']:
                                    logger.info(f"Position opened: {symbol} at {current_price:.6f}")
                
                except Exception as e:
                    logger.error(f"Error processing {symbol} at {current_time}: {e}")
            
            # Record equity curve
            total_equity = self.current_balance
            for symbol, position in self.positions.items():
                if symbol in historical_data:
                    current_data = historical_data[symbol][historical_data[symbol].index <= current_time]
                    if not current_data.empty:
                        current_price = current_data['close'].iloc[-1]
                        unrealized_pnl = (current_price - position['entry_price']) * position['quantity'] * position['leverage']
                        total_equity += unrealized_pnl
            
            self.equity_curve.append({
                'timestamp': current_time,
                'equity': total_equity,
                'balance': self.current_balance,
                'open_positions': len(self.positions)
            })
            
            # Move to next time step
            current_time += step
        
        # Close any remaining positions
        for symbol in list(self.positions.keys()):
            if symbol in historical_data:
                final_data = historical_data[symbol]
                if not final_data.empty:
                    final_price = final_data['close'].iloc[-1]
                    position = self.positions[symbol]
                    self.simulate_trade(symbol, 'SELL', final_price, position['quantity'], backtest_end)
        
        # Calculate results
        return self.calculate_backtest_results()
    
    def calculate_backtest_results(self) -> Dict:
        """Calculate comprehensive backtest results."""
        if not self.trades:
            return {'error': 'No trades executed during backtest'}
        
        trades_df = pd.DataFrame(self.trades)
        equity_df = pd.DataFrame(self.equity_curve)
        
        # Basic metrics
        total_trades = len(self.trades)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] <= 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # P&L metrics
        total_pnl = trades_df['pnl'].sum()
        total_fees = trades_df['fees'].sum()
        net_pnl = total_pnl - total_fees
        
        # Return metrics
        initial_equity = self.initial_balance
        final_equity = self.current_balance + sum(trade['pnl'] for trade in self.trades)
        total_return = (final_equity - initial_equity) / initial_equity * 100
        
        # Risk metrics
        if len(equity_df) > 1:
            equity_df['returns'] = equity_df['equity'].pct_change()
            daily_returns = equity_df['returns'].dropna()
            
            volatility = daily_returns.std() * np.sqrt(365 * 24 * 12)  # Annualized (5-min data)
            sharpe_ratio = (daily_returns.mean() * 365 * 24 * 12) / volatility if volatility > 0 else 0
            
            # Drawdown
            equity_df['peak'] = equity_df['equity'].cummax()
            equity_df['drawdown'] = (equity_df['equity'] - equity_df['peak']) / equity_df['peak']
            max_drawdown = equity_df['drawdown'].min() * 100
        else:
            volatility = 0
            sharpe_ratio = 0
            max_drawdown = 0
        
        # Trade analysis
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] <= 0]['pnl'].mean() if losing_trades > 0 else 0
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 and losing_trades > 0 else 0
        
        best_trade = trades_df['pnl'].max()
        worst_trade = trades_df['pnl'].min()
        avg_trade_duration = trades_df['duration_minutes'].mean()
        
        results = {
            'backtest_period': f"{self.start_date} to {self.end_date}",
            'initial_balance': initial_equity,
            'final_balance': final_equity,
            'total_return_pct': total_return,
            'total_pnl': total_pnl,
            'total_fees': total_fees,
            'net_pnl': net_pnl,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate_pct': win_rate * 100,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'best_trade': best_trade,
            'worst_trade': worst_trade,
            'max_drawdown_pct': max_drawdown,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'avg_trade_duration_minutes': avg_trade_duration,
            'trades_per_day': total_trades / ((pd.Timestamp(self.end_date) - pd.Timestamp(self.start_date)).days),
            'symbols_tested': self.test_symbols,
            'strategy_config': 'Enhanced Futures Strategy (Context.txt compliant)',
            'context_txt_rules_validated': [
                'EMA + RSI entry conditions',
                'Immediate profit exit',
                '1.5% stop loss',
                'Max 5x leverage',
                '2-5% position sizing',
                'Altcoin focus (BTC/ETH excluded)',
                'Volume confirmation',
                'Market condition awareness',
                '15-30 minute cooldown'
            ]
        }
        
        logger.info("Backtest completed successfully")
        logger.info(f"Total Return: {total_return:.2f}%")
        logger.info(f"Win Rate: {win_rate*100:.1f}%")
        logger.info(f"Total Trades: {total_trades}")
        logger.info(f"Profit Factor: {profit_factor:.2f}")
        logger.info(f"Max Drawdown: {max_drawdown:.2f}%")
        
        return results
