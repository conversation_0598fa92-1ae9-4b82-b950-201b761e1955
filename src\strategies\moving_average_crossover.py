"""
Moving Average Crossover Strategy Implementation
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Optional
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class MovingAverageCrossoverStrategy(BaseStrategy):
    """
    Moving Average Crossover Strategy: Uses two moving averages of different time periods
    to generate buy and sell signals.
    
    - BUY: When short-term MA crosses above long-term MA
    - SELL: When short-term MA crosses below long-term MA
    """
    
    def __init__(self, client, config: Dict):
        super().__init__(client, config)
        self.name = "MA_Crossover"
        
        # Strategy parameters
        self.short_window = config.get('short_window', 50)
        self.long_window = config.get('long_window', 200)
        self.min_data_points = max(self.short_window, self.long_window) + 5
        
        logger.info(f"MA Crossover Strategy initialized: {self.short_window}/{self.long_window} periods")
    
    def moving_average_crossover_strategy(self, data: pd.DataFrame, short_window: int, long_window: int) -> pd.DataFrame:
        """
        Compute moving average crossover signals.
        
        Args:
            data: Price data with OHLCV
            short_window: Short-term moving average period
            long_window: Long-term moving average period
            
        Returns:
            DataFrame with signals
        """
        # Compute short-term moving average
        data['short_ma'] = data['close'].rolling(window=short_window).mean()
        
        # Compute long-term moving average
        data['long_ma'] = data['close'].rolling(window=long_window).mean()
        
        # Generate buy/sell signals
        data['signal'] = 0
        data.loc[data['short_ma'] > data['long_ma'], 'signal'] = 1   # BUY signal
        data.loc[data['short_ma'] < data['long_ma'], 'signal'] = -1  # SELL signal
        
        return data
    
    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """
        Generate trading signal based on moving average crossover.
        
        Args:
            data: Historical price data
            symbol: Trading symbol
            
        Returns:
            'BUY', 'SELL', or 'HOLD'
        """
        try:
            if len(data) < self.min_data_points:
                logger.warning(f"[{symbol}] Insufficient data: {len(data)} < {self.min_data_points}")
                return 'HOLD'
            
            # Apply moving average crossover strategy
            strategy_data = self.moving_average_crossover_strategy(
                data.copy(), self.short_window, self.long_window
            )
            
            # Get the latest signals
            latest = strategy_data.iloc[-1]
            previous = strategy_data.iloc[-2]
            
            # Check for crossover signals
            current_signal = latest['signal']
            previous_signal = previous['signal']
            
            # Detect crossover events
            if current_signal == 1 and previous_signal != 1:
                # Short MA crossed above Long MA - BUY signal
                logger.info(f"[{symbol}] 🚀 BUY signal - MA Crossover:")
                logger.info(f"[{symbol}]   Short MA ({self.short_window}): {latest['short_ma']:.6f}")
                logger.info(f"[{symbol}]   Long MA ({self.long_window}): {latest['long_ma']:.6f}")
                logger.info(f"[{symbol}]   ✅ Short MA > Long MA (Golden Cross)")
                return 'BUY'
            
            elif current_signal == -1 and previous_signal != -1:
                # Short MA crossed below Long MA - SELL signal
                logger.info(f"[{symbol}] 📉 SELL signal - MA Crossover:")
                logger.info(f"[{symbol}]   Short MA ({self.short_window}): {latest['short_ma']:.6f}")
                logger.info(f"[{symbol}]   Long MA ({self.long_window}): {latest['long_ma']:.6f}")
                logger.info(f"[{symbol}]   ✅ Short MA < Long MA (Death Cross)")
                return 'SELL'
            
            else:
                # No crossover detected
                ma_diff = latest['short_ma'] - latest['long_ma']
                ma_diff_pct = (ma_diff / latest['long_ma']) * 100
                
                logger.info(f"[{symbol}] ➡️  No crossover signal:")
                logger.info(f"[{symbol}]   Short MA: {latest['short_ma']:.6f}")
                logger.info(f"[{symbol}]   Long MA: {latest['long_ma']:.6f}")
                logger.info(f"[{symbol}]   Difference: {ma_diff_pct:+.2f}%")
                return 'HOLD'
        
        except Exception as e:
            logger.error(f"[{symbol}] Error in MA Crossover strategy: {e}")
            return 'HOLD'
    
    def get_strategy_info(self) -> Dict:
        """Get strategy information."""
        return {
            'name': self.name,
            'type': 'Trend Following',
            'short_window': self.short_window,
            'long_window': self.long_window,
            'min_data_points': self.min_data_points,
            'description': f'MA Crossover {self.short_window}/{self.long_window}'
        }
    
    def validate_signal(self, data: pd.DataFrame, signal: str) -> bool:
        """
        Validate the generated signal.
        
        Args:
            data: Price data
            signal: Generated signal
            
        Returns:
            True if signal is valid
        """
        try:
            if signal == 'HOLD':
                return True
            
            latest = data.iloc[-1]
            
            # Ensure we have valid MA values
            if pd.isna(latest.get('short_ma')) or pd.isna(latest.get('long_ma')):
                logger.warning(f"Invalid MA values: short={latest.get('short_ma')}, long={latest.get('long_ma')}")
                return False
            
            # Ensure MAs are reasonable relative to price
            price = latest['close']
            short_ma = latest['short_ma']
            long_ma = latest['long_ma']
            
            if abs(short_ma - price) / price > 0.5:  # MA more than 50% away from price
                logger.warning(f"Short MA too far from price: MA={short_ma}, Price={price}")
                return False
            
            if abs(long_ma - price) / price > 0.5:  # MA more than 50% away from price
                logger.warning(f"Long MA too far from price: MA={long_ma}, Price={price}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating signal: {e}")
            return False

    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate moving averages for the strategy.

        Args:
            data: Price data

        Returns:
            DataFrame with calculated indicators
        """
        # Calculate moving averages
        data['short_ma'] = data['close'].rolling(window=self.short_window).mean()
        data['long_ma'] = data['close'].rolling(window=self.long_window).mean()

        return data

    def should_buy(self, data: pd.DataFrame) -> bool:
        """
        Check if conditions are met for a BUY signal.

        Args:
            data: Price data with indicators

        Returns:
            True if should buy
        """
        if len(data) < 2:
            return False

        latest = data.iloc[-1]
        previous = data.iloc[-2]

        # Check for golden cross (short MA crosses above long MA)
        return (latest['short_ma'] > latest['long_ma'] and
                previous['short_ma'] <= previous['long_ma'])

    def should_sell(self, data: pd.DataFrame) -> bool:
        """
        Check if conditions are met for a SELL signal.

        Args:
            data: Price data with indicators

        Returns:
            True if should sell
        """
        if len(data) < 2:
            return False

        latest = data.iloc[-1]
        previous = data.iloc[-2]

        # Check for death cross (short MA crosses below long MA)
        return (latest['short_ma'] < latest['long_ma'] and
                previous['short_ma'] >= previous['long_ma'])
