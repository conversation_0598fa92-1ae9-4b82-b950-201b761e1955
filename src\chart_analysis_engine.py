"""
Chart-Based Analysis Engine for Trading Bot
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ChartPattern(Enum):
    """Enhanced chart pattern types."""
    # Breakout Patterns
    BREAKOUT_BULLISH = "breakout_bullish"
    BREAKOUT_BEARISH = "breakout_bearish"
    VOLUME_BREAKOUT_BULL = "volume_breakout_bull"
    VOLUME_BREAKOUT_BEAR = "volume_breakout_bear"

    # Reversal Patterns
    REVERSAL_BULLISH = "reversal_bullish"
    REVERSAL_BEARISH = "reversal_bearish"
    DOUBLE_BOTTOM = "double_bottom"
    DOUBLE_TOP = "double_top"
    HAMMER_REVERSAL = "hammer_reversal"
    SHOOTING_STAR = "shooting_star"

    # Continuation Patterns
    TREND_CONTINUATION = "trend_continuation"
    BULL_FLAG = "bull_flag"
    BEAR_FLAG = "bear_flag"
    ASCENDING_TRIANGLE = "ascending_triangle"
    DESCENDING_TRIANGLE = "descending_triangle"

    # Support/Resistance Patterns
    SUPPORT_BOUNCE = "support_bounce"
    RESISTANCE_REJECTION = "resistance_rejection"
    SUPPORT_BREAK = "support_break"
    RESISTANCE_BREAK = "resistance_break"

    # Consolidation Patterns
    CONSOLIDATION = "consolidation"
    SIDEWAYS_RANGE = "sideways_range"
    SQUEEZE_PATTERN = "squeeze_pattern"

@dataclass
class ChartAnalysis:
    """Chart analysis result."""
    signal: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float  # 0-100
    pattern: ChartPattern
    entry_reason: str
    support_level: float
    resistance_level: float
    target_price: float
    stop_loss: float
    timeframe_alignment: Dict[str, str]  # {'5m': 'BUY', '15m': 'BUY', '1h': 'HOLD'}
    volume_confirmation: bool
    risk_reward_ratio: float

class ChartAnalysisEngine:
    """
    Advanced chart analysis engine for technical trading decisions.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Chart analysis parameters
        self.support_resistance_periods = config.get('support_resistance_periods', 20)
        self.breakout_threshold = config.get('breakout_threshold', 0.005)  # 0.5%
        self.volume_surge_threshold = config.get('volume_surge_threshold', 1.5)
        self.min_confidence_threshold = config.get('min_confidence_threshold', 70)
        self.min_risk_reward_ratio = config.get('min_risk_reward_ratio', 1.5)
        
        # Moving average periods for trend analysis
        self.ma_short = config.get('ma_short', 10)
        self.ma_medium = config.get('ma_medium', 20)
        self.ma_long = config.get('ma_long', 50)
        
        logger.info(f"Chart Analysis Engine initialized with {self.min_confidence_threshold}% min confidence")
    
    def calculate_support_resistance(self, data: pd.DataFrame) -> Tuple[float, float]:
        """Calculate dynamic support and resistance levels."""
        try:
            if len(data) < self.support_resistance_periods:
                return 0.0, 0.0
            
            # Use recent data for S/R calculation
            recent_data = data.tail(self.support_resistance_periods)
            
            # Support: Recent low levels
            support_candidates = recent_data['low'].nsmallest(3)
            support = support_candidates.mean()
            
            # Resistance: Recent high levels
            resistance_candidates = recent_data['high'].nlargest(3)
            resistance = resistance_candidates.mean()
            
            return float(support), float(resistance)
            
        except Exception as e:
            logger.error(f"Error calculating support/resistance: {e}")
            return 0.0, 0.0
    
    def detect_price_action_pattern(self, data: pd.DataFrame) -> Tuple[ChartPattern, float]:
        """Enhanced price action pattern detection with advanced algorithms."""
        try:
            if len(data) < 20:
                return ChartPattern.CONSOLIDATION, 0.0

            current_price = data['close'].iloc[-1]
            support, resistance = self.calculate_support_resistance(data)

            # Calculate price position within S/R range
            if resistance > support > 0:
                price_position = (current_price - support) / (resistance - support)
            else:
                price_position = 0.5

            # Enhanced price movement analysis
            price_change_3 = (current_price - data['close'].iloc[-3]) / data['close'].iloc[-3]
            price_change_5 = (current_price - data['close'].iloc[-5]) / data['close'].iloc[-5]
            price_change_10 = (current_price - data['close'].iloc[-10]) / data['close'].iloc[-10]
            price_change_20 = (current_price - data['close'].iloc[-20]) / data['close'].iloc[-20]

            # Enhanced volume analysis
            recent_volume = data['volume'].tail(3).mean()
            avg_volume = data['volume'].tail(20).mean()
            volume_surge = recent_volume > avg_volume * self.volume_surge_threshold
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0

            # Volatility analysis
            price_volatility = data['close'].tail(10).std() / data['close'].tail(10).mean()

            # Pattern detection with enhanced logic
            pattern, confidence = self._detect_advanced_patterns(
                data, current_price, support, resistance, price_position,
                price_change_3, price_change_5, price_change_10, price_change_20,
                volume_surge, volume_ratio, price_volatility
            )

            return pattern, confidence

        except Exception as e:
            logger.error(f"Error detecting price action pattern: {e}")
            return ChartPattern.CONSOLIDATION, 0.0

    def _detect_advanced_patterns(self, data: pd.DataFrame, current_price: float,
                                support: float, resistance: float, price_position: float,
                                price_change_3: float, price_change_5: float,
                                price_change_10: float, price_change_20: float,
                                volume_surge: bool, volume_ratio: float,
                                price_volatility: float) -> Tuple[ChartPattern, float]:
        """Detect advanced chart patterns with sophisticated logic."""

        # 1. VOLUME BREAKOUT PATTERNS (High Priority)
        if volume_ratio > 2.0:  # Strong volume surge
            if current_price > resistance * (1 + self.breakout_threshold):
                confidence = min(90.0, 75.0 + (volume_ratio - 2.0) * 5)
                return ChartPattern.VOLUME_BREAKOUT_BULL, confidence
            elif current_price < support * (1 - self.breakout_threshold):
                confidence = min(90.0, 75.0 + (volume_ratio - 2.0) * 5)
                return ChartPattern.VOLUME_BREAKOUT_BEAR, confidence

        # 2. DOUBLE BOTTOM/TOP PATTERNS
        if len(data) >= 30:
            double_pattern, double_confidence = self._detect_double_patterns(data, current_price)
            if double_confidence > 70:
                return double_pattern, double_confidence

        # 3. FLAG PATTERNS
        flag_pattern, flag_confidence = self._detect_flag_patterns(
            data, price_change_5, price_change_10, price_change_20, volume_surge
        )
        if flag_confidence > 65:
            return flag_pattern, flag_confidence

        # 4. TRIANGLE PATTERNS
        triangle_pattern, triangle_confidence = self._detect_triangle_patterns(
            data, current_price, support, resistance, price_volatility
        )
        if triangle_confidence > 60:
            return triangle_pattern, triangle_confidence

        # 5. ENHANCED BREAKOUT PATTERNS
        if current_price > resistance * (1 + self.breakout_threshold):
            confidence = 70.0 + (volume_ratio - 1.0) * 10
            confidence = min(confidence, 85.0)
            return ChartPattern.BREAKOUT_BULLISH, confidence
        elif current_price < support * (1 - self.breakout_threshold):
            confidence = 70.0 + (volume_ratio - 1.0) * 10
            confidence = min(confidence, 85.0)
            return ChartPattern.BREAKOUT_BEARISH, confidence

        # 6. SUPPORT/RESISTANCE PATTERNS
        if price_position < 0.15:  # Very close to support
            if price_change_3 > 0.005:  # Bouncing up
                confidence = 70.0 if volume_surge else 55.0
                return ChartPattern.SUPPORT_BOUNCE, confidence
            elif price_change_3 < -0.01:  # Breaking down
                confidence = 75.0 if volume_surge else 60.0
                return ChartPattern.SUPPORT_BREAK, confidence

        elif price_position > 0.85:  # Very close to resistance
            if price_change_3 < -0.005:  # Rejecting down
                confidence = 70.0 if volume_surge else 55.0
                return ChartPattern.RESISTANCE_REJECTION, confidence
            elif price_change_3 > 0.01:  # Breaking up
                confidence = 75.0 if volume_surge else 60.0
                return ChartPattern.RESISTANCE_BREAK, confidence

        # 7. REVERSAL PATTERNS
        if abs(price_change_10) > 0.05:  # Significant prior move
            if price_change_10 < -0.05 and price_change_3 > 0.015:  # Bullish reversal
                confidence = 65.0 + (volume_ratio - 1.0) * 5
                return ChartPattern.REVERSAL_BULLISH, min(confidence, 80.0)
            elif price_change_10 > 0.05 and price_change_3 < -0.015:  # Bearish reversal
                confidence = 65.0 + (volume_ratio - 1.0) * 5
                return ChartPattern.REVERSAL_BEARISH, min(confidence, 80.0)

        # 8. TREND CONTINUATION
        if (price_change_5 > 0.02 and price_change_10 > 0.03 and price_change_20 > 0.05):
            confidence = 60.0 + (volume_ratio - 1.0) * 5
            return ChartPattern.TREND_CONTINUATION, min(confidence, 75.0)

        # 9. SQUEEZE PATTERN (Low volatility before breakout)
        if price_volatility < 0.02 and volume_ratio > 1.3:
            confidence = 55.0
            return ChartPattern.SQUEEZE_PATTERN, confidence

        # 10. DEFAULT PATTERNS
        if 0.3 <= price_position <= 0.7 and price_volatility < 0.015:
            return ChartPattern.SIDEWAYS_RANGE, 45.0

        return ChartPattern.CONSOLIDATION, 40.0

    def _detect_double_patterns(self, data: pd.DataFrame, current_price: float) -> Tuple[ChartPattern, float]:
        """Detect double bottom and double top patterns."""
        try:
            if len(data) < 30:
                return ChartPattern.CONSOLIDATION, 0.0

            # Look for double bottom pattern
            lows = data['low'].tail(30)
            low_indices = []

            # Find local minima
            for i in range(2, len(lows) - 2):
                if (lows.iloc[i] < lows.iloc[i-1] and lows.iloc[i] < lows.iloc[i-2] and
                    lows.iloc[i] < lows.iloc[i+1] and lows.iloc[i] < lows.iloc[i+2]):
                    low_indices.append(i)

            # Check for double bottom
            if len(low_indices) >= 2:
                last_two_lows = [lows.iloc[low_indices[-2]], lows.iloc[low_indices[-1]]]
                if abs(last_two_lows[0] - last_two_lows[1]) / last_two_lows[0] < 0.02:  # Within 2%
                    if current_price > min(last_two_lows) * 1.015:  # Price above lows
                        return ChartPattern.DOUBLE_BOTTOM, 75.0

            # Look for double top pattern
            highs = data['high'].tail(30)
            high_indices = []

            # Find local maxima
            for i in range(2, len(highs) - 2):
                if (highs.iloc[i] > highs.iloc[i-1] and highs.iloc[i] > highs.iloc[i-2] and
                    highs.iloc[i] > highs.iloc[i+1] and highs.iloc[i] > highs.iloc[i+2]):
                    high_indices.append(i)

            # Check for double top
            if len(high_indices) >= 2:
                last_two_highs = [highs.iloc[high_indices[-2]], highs.iloc[high_indices[-1]]]
                if abs(last_two_highs[0] - last_two_highs[1]) / last_two_highs[0] < 0.02:  # Within 2%
                    if current_price < max(last_two_highs) * 0.985:  # Price below highs
                        return ChartPattern.DOUBLE_TOP, 75.0

            return ChartPattern.CONSOLIDATION, 0.0

        except Exception as e:
            logger.error(f"Error detecting double patterns: {e}")
            return ChartPattern.CONSOLIDATION, 0.0

    def _detect_flag_patterns(self, data: pd.DataFrame, price_change_5: float,
                            price_change_10: float, price_change_20: float,
                            volume_surge: bool) -> Tuple[ChartPattern, float]:
        """Detect bull and bear flag patterns."""
        try:
            # Bull flag: Strong uptrend followed by slight pullback/consolidation
            if (price_change_20 > 0.08 and  # Strong prior uptrend
                price_change_10 > 0.03 and  # Continued uptrend
                -0.02 <= price_change_5 <= 0.01):  # Slight pullback or consolidation
                confidence = 70.0 if volume_surge else 60.0
                return ChartPattern.BULL_FLAG, confidence

            # Bear flag: Strong downtrend followed by slight bounce/consolidation
            if (price_change_20 < -0.08 and  # Strong prior downtrend
                price_change_10 < -0.03 and  # Continued downtrend
                -0.01 <= price_change_5 <= 0.02):  # Slight bounce or consolidation
                confidence = 70.0 if volume_surge else 60.0
                return ChartPattern.BEAR_FLAG, confidence

            return ChartPattern.CONSOLIDATION, 0.0

        except Exception as e:
            logger.error(f"Error detecting flag patterns: {e}")
            return ChartPattern.CONSOLIDATION, 0.0

    def _detect_triangle_patterns(self, data: pd.DataFrame, current_price: float,
                                support: float, resistance: float,
                                price_volatility: float) -> Tuple[ChartPattern, float]:
        """Detect ascending and descending triangle patterns."""
        try:
            if len(data) < 20 or resistance <= support:
                return ChartPattern.CONSOLIDATION, 0.0

            # Get recent highs and lows
            recent_data = data.tail(15)
            highs = recent_data['high']
            lows = recent_data['low']

            # Calculate trend lines
            high_trend = np.polyfit(range(len(highs)), highs, 1)[0]  # Slope of highs
            low_trend = np.polyfit(range(len(lows)), lows, 1)[0]    # Slope of lows

            # Ascending triangle: Flat resistance, rising support
            if (abs(high_trend) < 0.001 and  # Flat resistance
                low_trend > 0.001 and       # Rising support
                price_volatility < 0.025):   # Decreasing volatility
                confidence = 65.0
                return ChartPattern.ASCENDING_TRIANGLE, confidence

            # Descending triangle: Flat support, falling resistance
            if (abs(low_trend) < 0.001 and   # Flat support
                high_trend < -0.001 and      # Falling resistance
                price_volatility < 0.025):   # Decreasing volatility
                confidence = 65.0
                return ChartPattern.DESCENDING_TRIANGLE, confidence

            return ChartPattern.CONSOLIDATION, 0.0

        except Exception as e:
            logger.error(f"Error detecting triangle patterns: {e}")
            return ChartPattern.CONSOLIDATION, 0.0

    def analyze_moving_averages(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Analyze moving average alignment and trend strength."""
        try:
            if len(data) < self.ma_long:
                return 'HOLD', 0.0
            
            # Calculate moving averages
            ma_short = data['close'].rolling(self.ma_short).mean().iloc[-1]
            ma_medium = data['close'].rolling(self.ma_medium).mean().iloc[-1]
            ma_long = data['close'].rolling(self.ma_long).mean().iloc[-1]
            current_price = data['close'].iloc[-1]
            
            # Check alignment
            bullish_alignment = current_price > ma_short > ma_medium > ma_long
            bearish_alignment = current_price < ma_short < ma_medium < ma_long
            
            # Calculate trend strength
            if bullish_alignment:
                trend_strength = min(((current_price - ma_long) / ma_long) * 100, 20)
                return 'BUY', 70.0 + trend_strength
            elif bearish_alignment:
                trend_strength = min(((ma_long - current_price) / ma_long) * 100, 20)
                return 'SELL', 70.0 + trend_strength
            else:
                # Mixed signals
                return 'HOLD', 30.0
                
        except Exception as e:
            logger.error(f"Error analyzing moving averages: {e}")
            return 'HOLD', 0.0
    
    def calculate_rsi_divergence(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Enhanced RSI analysis with divergence detection."""
        try:
            if 'RSI' not in data.columns or len(data) < 14:
                return 'HOLD', 0.0

            current_rsi = data['RSI'].iloc[-1]

            # Enhanced RSI analysis with divergence
            rsi_signal, rsi_confidence = self._analyze_rsi_levels(current_rsi)

            # Check for RSI divergence
            if len(data) >= 20:
                divergence_signal, divergence_confidence = self._detect_rsi_divergence(data)
                if divergence_confidence > rsi_confidence:
                    return divergence_signal, divergence_confidence

            return rsi_signal, rsi_confidence

        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return 'HOLD', 0.0

    def _analyze_rsi_levels(self, current_rsi: float) -> Tuple[str, float]:
        """Analyze RSI levels with enhanced thresholds."""
        if current_rsi < 20:
            return 'BUY', 90.0  # Extremely oversold
        elif current_rsi < 30:
            return 'BUY', 80.0  # Oversold
        elif current_rsi < 40:
            return 'BUY', 65.0  # Approaching oversold
        elif current_rsi > 80:
            return 'SELL', 90.0  # Extremely overbought
        elif current_rsi > 70:
            return 'SELL', 80.0  # Overbought
        elif current_rsi > 60:
            return 'SELL', 65.0  # Approaching overbought
        elif 45 <= current_rsi <= 55:
            return 'HOLD', 20.0  # Neutral zone
        else:
            return 'HOLD', 35.0  # Weak signal zone

    def _detect_rsi_divergence(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Detect RSI divergences for enhanced signals."""
        try:
            if len(data) < 20:
                return 'HOLD', 0.0

            # Get recent price and RSI data
            recent_data = data.tail(15)
            prices = recent_data['close']
            rsi_values = recent_data['RSI']

            # Find recent highs and lows
            price_highs = []
            price_lows = []
            rsi_highs = []
            rsi_lows = []

            for i in range(2, len(prices) - 2):
                # Price highs
                if (prices.iloc[i] > prices.iloc[i-1] and prices.iloc[i] > prices.iloc[i-2] and
                    prices.iloc[i] > prices.iloc[i+1] and prices.iloc[i] > prices.iloc[i+2]):
                    price_highs.append(prices.iloc[i])
                    rsi_highs.append(rsi_values.iloc[i])

                # Price lows
                if (prices.iloc[i] < prices.iloc[i-1] and prices.iloc[i] < prices.iloc[i-2] and
                    prices.iloc[i] < prices.iloc[i+1] and prices.iloc[i] < prices.iloc[i+2]):
                    price_lows.append(prices.iloc[i])
                    rsi_lows.append(rsi_values.iloc[i])

            # Bullish divergence: Lower price lows, higher RSI lows
            if len(price_lows) >= 2 and len(rsi_lows) >= 2:
                if (price_lows[-1] < price_lows[-2] and rsi_lows[-1] > rsi_lows[-2]):
                    return 'BUY', 85.0

            # Bearish divergence: Higher price highs, lower RSI highs
            if len(price_highs) >= 2 and len(rsi_highs) >= 2:
                if (price_highs[-1] > price_highs[-2] and rsi_highs[-1] < rsi_highs[-2]):
                    return 'SELL', 85.0

            return 'HOLD', 0.0

        except Exception as e:
            logger.error(f"Error detecting RSI divergence: {e}")
            return 'HOLD', 0.0

    def calculate_macd_signals(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Calculate MACD signals for trend confirmation."""
        try:
            if len(data) < 26:
                return 'HOLD', 0.0

            # Calculate MACD
            exp1 = data['close'].ewm(span=12).mean()
            exp2 = data['close'].ewm(span=26).mean()
            macd_line = exp1 - exp2
            signal_line = macd_line.ewm(span=9).mean()
            histogram = macd_line - signal_line

            current_macd = macd_line.iloc[-1]
            current_signal = signal_line.iloc[-1]
            current_histogram = histogram.iloc[-1]
            prev_histogram = histogram.iloc[-2] if len(histogram) > 1 else 0

            # MACD signals
            confidence = 50.0

            # MACD line above signal line (bullish)
            if current_macd > current_signal:
                confidence += 15
                signal = 'BUY'
            else:
                confidence += 15
                signal = 'SELL'

            # Histogram increasing (momentum strengthening)
            if current_histogram > prev_histogram:
                confidence += 10

            # MACD crossover
            prev_macd = macd_line.iloc[-2] if len(macd_line) > 1 else current_macd
            prev_signal_line = signal_line.iloc[-2] if len(signal_line) > 1 else current_signal

            # Bullish crossover
            if prev_macd <= prev_signal_line and current_macd > current_signal:
                return 'BUY', 80.0

            # Bearish crossover
            if prev_macd >= prev_signal_line and current_macd < current_signal:
                return 'SELL', 80.0

            return signal, min(confidence, 75.0)

        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return 'HOLD', 0.0

    def calculate_stochastic_signals(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Calculate Stochastic oscillator signals."""
        try:
            if len(data) < 14:
                return 'HOLD', 0.0

            # Calculate Stochastic %K and %D
            period = 14
            recent_data = data.tail(period)

            lowest_low = recent_data['low'].min()
            highest_high = recent_data['high'].max()
            current_close = data['close'].iloc[-1]

            if highest_high == lowest_low:
                return 'HOLD', 0.0

            k_percent = 100 * (current_close - lowest_low) / (highest_high - lowest_low)

            # Calculate %D (3-period SMA of %K)
            if len(data) >= 16:
                k_values = []
                for i in range(3):
                    period_data = data.iloc[-(16-i):-(2-i)]
                    period_low = period_data['low'].min()
                    period_high = period_data['high'].max()
                    period_close = period_data['close'].iloc[-1]

                    if period_high != period_low:
                        k_val = 100 * (period_close - period_low) / (period_high - period_low)
                        k_values.append(k_val)

                d_percent = np.mean(k_values) if k_values else k_percent
            else:
                d_percent = k_percent

            # Stochastic signals
            if k_percent < 20 and d_percent < 20:
                return 'BUY', 75.0  # Oversold
            elif k_percent > 80 and d_percent > 80:
                return 'SELL', 75.0  # Overbought
            elif k_percent < 30:
                return 'BUY', 60.0  # Approaching oversold
            elif k_percent > 70:
                return 'SELL', 60.0  # Approaching overbought
            else:
                return 'HOLD', 30.0

        except Exception as e:
            logger.error(f"Error calculating Stochastic: {e}")
            return 'HOLD', 0.0
    
    def analyze_volume_patterns(self, data: pd.DataFrame) -> Tuple[bool, float]:
        """Analyze volume patterns for confirmation."""
        try:
            if 'volume' not in data.columns or len(data) < 10:
                return False, 0.0
            
            recent_volume = data['volume'].tail(3).mean()
            avg_volume = data['volume'].tail(20).mean()
            
            # Volume surge detection
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0
            
            if volume_ratio > self.volume_surge_threshold:
                confidence = min((volume_ratio - 1) * 50, 90)
                return True, confidence
            else:
                return False, max(volume_ratio * 30, 10)
                
        except Exception as e:
            logger.error(f"Error analyzing volume: {e}")
            return False, 0.0
    
    def multi_timeframe_analysis(self, data_5m: pd.DataFrame, data_15m: pd.DataFrame, 
                                data_1h: pd.DataFrame) -> Dict[str, str]:
        """Perform multi-timeframe analysis."""
        try:
            timeframe_signals = {}
            
            # Analyze each timeframe
            timeframes = {
                '5m': data_5m,
                '15m': data_15m,
                '1h': data_1h
            }
            
            for tf, data in timeframes.items():
                if data is not None and not data.empty and len(data) >= 20:
                    ma_signal, ma_confidence = self.analyze_moving_averages(data)
                    rsi_signal, rsi_confidence = self.calculate_rsi_divergence(data)
                    
                    # Combine signals
                    if ma_confidence > 60 and rsi_confidence > 60:
                        if ma_signal == rsi_signal:
                            timeframe_signals[tf] = ma_signal
                        else:
                            timeframe_signals[tf] = 'HOLD'
                    elif ma_confidence > rsi_confidence:
                        timeframe_signals[tf] = ma_signal if ma_confidence > 50 else 'HOLD'
                    else:
                        timeframe_signals[tf] = rsi_signal if rsi_confidence > 50 else 'HOLD'
                else:
                    timeframe_signals[tf] = 'HOLD'
            
            return timeframe_signals
            
        except Exception as e:
            logger.error(f"Error in multi-timeframe analysis: {e}")
            return {'5m': 'HOLD', '15m': 'HOLD', '1h': 'HOLD'}
    
    def calculate_risk_reward(self, entry_price: float, target_price: float, 
                            stop_loss: float) -> float:
        """Calculate risk-reward ratio."""
        try:
            if entry_price <= 0 or target_price <= 0 or stop_loss <= 0:
                return 0.0
            
            reward = abs(target_price - entry_price)
            risk = abs(entry_price - stop_loss)
            
            if risk > 0:
                return reward / risk
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating risk-reward: {e}")
            return 0.0
    
    def comprehensive_chart_analysis(self, data_5m: pd.DataFrame, data_15m: pd.DataFrame,
                                   data_1h: pd.DataFrame, symbol: str) -> ChartAnalysis:
        """Perform comprehensive chart analysis across multiple timeframes."""
        try:
            # Use 15m as primary timeframe for analysis
            primary_data = None
            if data_15m is not None and not data_15m.empty and len(data_15m) >= 20:
                primary_data = data_15m
            elif data_5m is not None and not data_5m.empty and len(data_5m) >= 20:
                primary_data = data_5m
            elif data_1h is not None and not data_1h.empty and len(data_1h) >= 20:
                primary_data = data_1h
            
            if primary_data is None or primary_data.empty or len(primary_data) < 20:
                return ChartAnalysis(
                    signal='HOLD',
                    confidence=0.0,
                    pattern=ChartPattern.CONSOLIDATION,
                    entry_reason='Insufficient data',
                    support_level=0.0,
                    resistance_level=0.0,
                    target_price=0.0,
                    stop_loss=0.0,
                    timeframe_alignment={'5m': 'HOLD', '15m': 'HOLD', '1h': 'HOLD'},
                    volume_confirmation=False,
                    risk_reward_ratio=0.0
                )
            
            current_price = primary_data['close'].iloc[-1]
            
            # 1. Pattern detection
            pattern, pattern_confidence = self.detect_price_action_pattern(primary_data)
            
            # 2. Moving average analysis
            ma_signal, ma_confidence = self.analyze_moving_averages(primary_data)

            # 3. RSI analysis with divergence
            rsi_signal, rsi_confidence = self.calculate_rsi_divergence(primary_data)

            # 4. MACD analysis
            macd_signal, macd_confidence = self.calculate_macd_signals(primary_data)

            # 5. Stochastic analysis
            stoch_signal, stoch_confidence = self.calculate_stochastic_signals(primary_data)

            # 6. Volume confirmation
            volume_confirmed, volume_confidence = self.analyze_volume_patterns(primary_data)

            # 7. Multi-timeframe alignment
            timeframe_signals = self.multi_timeframe_analysis(data_5m, data_15m, data_1h)
            
            # 8. Support and resistance levels
            support, resistance = self.calculate_support_resistance(primary_data)

            # Enhanced signal combination with weighted voting
            signals_data = [
                {'signal': ma_signal, 'confidence': ma_confidence, 'weight': 0.25},
                {'signal': rsi_signal, 'confidence': rsi_confidence, 'weight': 0.20},
                {'signal': macd_signal, 'confidence': macd_confidence, 'weight': 0.20},
                {'signal': stoch_signal, 'confidence': stoch_confidence, 'weight': 0.15},
                {'signal': 'BUY' if pattern in [ChartPattern.BREAKOUT_BULLISH, ChartPattern.VOLUME_BREAKOUT_BULL,
                                               ChartPattern.REVERSAL_BULLISH, ChartPattern.SUPPORT_BOUNCE,
                                               ChartPattern.DOUBLE_BOTTOM, ChartPattern.BULL_FLAG,
                                               ChartPattern.ASCENDING_TRIANGLE] else
                          'SELL' if pattern in [ChartPattern.BREAKOUT_BEARISH, ChartPattern.VOLUME_BREAKOUT_BEAR,
                                               ChartPattern.REVERSAL_BEARISH, ChartPattern.RESISTANCE_REJECTION,
                                               ChartPattern.DOUBLE_TOP, ChartPattern.BEAR_FLAG,
                                               ChartPattern.DESCENDING_TRIANGLE] else 'HOLD',
                 'confidence': pattern_confidence, 'weight': 0.20}
            ]

            # Calculate weighted signal scores
            buy_score = sum(s['confidence'] * s['weight'] for s in signals_data if s['signal'] == 'BUY')
            sell_score = sum(s['confidence'] * s['weight'] for s in signals_data if s['signal'] == 'SELL')
            hold_score = sum(s['confidence'] * s['weight'] for s in signals_data if s['signal'] == 'HOLD')

            # Determine overall signal based on weighted scores
            if buy_score > sell_score and buy_score > hold_score:
                overall_signal = 'BUY'
                base_confidence = buy_score / sum(s['weight'] for s in signals_data if s['signal'] == 'BUY')
            elif sell_score > buy_score and sell_score > hold_score:
                overall_signal = 'SELL'
                base_confidence = sell_score / sum(s['weight'] for s in signals_data if s['signal'] == 'SELL')
            else:
                overall_signal = 'HOLD'
                base_confidence = max(buy_score, sell_score, hold_score) / 0.2  # Normalize
            
            # Enhanced confidence boosting with adaptive factors

            # 1. Timeframe alignment boost (weighted by timeframe importance)
            timeframe_weights = {'5m': 0.2, '15m': 0.35, '1h': 0.45}
            alignment_boost = 0

            for tf, tf_signal in timeframe_signals.items():
                if tf_signal == overall_signal:
                    alignment_boost += 5 * timeframe_weights.get(tf, 0.33)

            # 2. Volume confirmation boost (scaled by volume ratio)
            volume_boost = 0
            if volume_confirmed:
                volume_ratio = primary_data['volume'].tail(3).mean() / primary_data['volume'].tail(20).mean() if 'volume' in primary_data.columns else 1.0
                volume_boost = min(10 * volume_ratio, 15)  # Cap at 15%

            # 3. Pattern strength boost
            pattern_boost = 0
            strong_patterns = [ChartPattern.VOLUME_BREAKOUT_BULL, ChartPattern.VOLUME_BREAKOUT_BEAR,
                              ChartPattern.DOUBLE_BOTTOM, ChartPattern.DOUBLE_TOP]

            if pattern in strong_patterns:
                pattern_boost = 5

            # 4. Signal agreement boost (when multiple indicators agree)
            signal_counts = {'BUY': 0, 'SELL': 0, 'HOLD': 0}
            for s in [ma_signal, rsi_signal, macd_signal, stoch_signal]:
                signal_counts[s] += 1

            agreement_boost = 0
            if signal_counts[overall_signal] >= 3:  # At least 3 indicators agree
                agreement_boost = 7
            elif signal_counts[overall_signal] == 2:  # 2 indicators agree
                agreement_boost = 3

            # 5. Risk/reward boost
            risk_reward_boost = 0
            if overall_signal != 'HOLD':
                # Calculate potential risk/reward
                if overall_signal == 'BUY':
                    potential_reward = (resistance - current_price) / current_price
                    potential_risk = (current_price - support) / current_price
                else:  # SELL
                    potential_reward = (current_price - support) / current_price
                    potential_risk = (resistance - current_price) / current_price

                if potential_risk > 0:
                    risk_reward_ratio = potential_reward / potential_risk
                    if risk_reward_ratio > 2.0:
                        risk_reward_boost = 5
                    elif risk_reward_ratio > 1.5:
                        risk_reward_boost = 3

            # Calculate final confidence with all boosts
            total_boost = alignment_boost + volume_boost + pattern_boost + agreement_boost + risk_reward_boost
            overall_confidence = min(base_confidence + total_boost, 95.0)
            
            # Calculate target and stop loss
            if overall_signal == 'BUY':
                target_price = resistance if resistance > current_price else current_price * 1.02
                stop_loss = support if support < current_price else current_price * 0.98
            elif overall_signal == 'SELL':
                target_price = support if support < current_price else current_price * 0.98
                stop_loss = resistance if resistance > current_price else current_price * 1.02
            else:
                target_price = current_price
                stop_loss = current_price
            
            # Calculate risk-reward ratio
            risk_reward = self.calculate_risk_reward(current_price, target_price, stop_loss)
            
            # Create entry reason
            reasons = []
            if pattern_confidence > 60:
                reasons.append(f"{pattern.value}")
            if ma_confidence > 60:
                reasons.append(f"MA trend")
            if rsi_confidence > 60:
                reasons.append(f"RSI signal")
            if volume_confirmed:
                reasons.append("Volume surge")
            
            entry_reason = " + ".join(reasons) if reasons else "Weak signals"
            
            return ChartAnalysis(
                signal=overall_signal,
                confidence=overall_confidence,
                pattern=pattern,
                entry_reason=entry_reason,
                support_level=support,
                resistance_level=resistance,
                target_price=target_price,
                stop_loss=stop_loss,
                timeframe_alignment=timeframe_signals,
                volume_confirmation=volume_confirmed,
                risk_reward_ratio=risk_reward
            )
            
        except Exception as e:
            logger.error(f"Error in comprehensive chart analysis for {symbol}: {e}")
            return ChartAnalysis(
                signal='HOLD',
                confidence=0.0,
                pattern=ChartPattern.CONSOLIDATION,
                entry_reason=f'Analysis error: {e}',
                support_level=0.0,
                resistance_level=0.0,
                target_price=0.0,
                stop_loss=0.0,
                timeframe_alignment={'5m': 'HOLD', '15m': 'HOLD', '1h': 'HOLD'},
                volume_confirmation=False,
                risk_reward_ratio=0.0
            )
