"""
Chart-Based Analysis Engine for Trading Bot
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class ChartPattern(Enum):
    """Chart pattern types."""
    BREAKOUT_BULLISH = "breakout_bullish"
    BREAKOUT_BEARISH = "breakout_bearish"
    REVERSAL_BULLISH = "reversal_bullish"
    REVERSAL_BEARISH = "reversal_bearish"
    CONSOLIDATION = "consolidation"
    TREND_CONTINUATION = "trend_continuation"
    SUPPORT_BOUNCE = "support_bounce"
    RESISTANCE_REJECTION = "resistance_rejection"

@dataclass
class ChartAnalysis:
    """Chart analysis result."""
    signal: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float  # 0-100
    pattern: ChartPattern
    entry_reason: str
    support_level: float
    resistance_level: float
    target_price: float
    stop_loss: float
    timeframe_alignment: Dict[str, str]  # {'5m': 'BUY', '15m': 'BUY', '1h': 'HOLD'}
    volume_confirmation: bool
    risk_reward_ratio: float

class ChartAnalysisEngine:
    """
    Advanced chart analysis engine for technical trading decisions.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Chart analysis parameters
        self.support_resistance_periods = config.get('support_resistance_periods', 20)
        self.breakout_threshold = config.get('breakout_threshold', 0.005)  # 0.5%
        self.volume_surge_threshold = config.get('volume_surge_threshold', 1.5)
        self.min_confidence_threshold = config.get('min_confidence_threshold', 70)
        self.min_risk_reward_ratio = config.get('min_risk_reward_ratio', 1.5)
        
        # Moving average periods for trend analysis
        self.ma_short = config.get('ma_short', 10)
        self.ma_medium = config.get('ma_medium', 20)
        self.ma_long = config.get('ma_long', 50)
        
        logger.info(f"Chart Analysis Engine initialized with {self.min_confidence_threshold}% min confidence")
    
    def calculate_support_resistance(self, data: pd.DataFrame) -> Tuple[float, float]:
        """Calculate dynamic support and resistance levels."""
        try:
            if len(data) < self.support_resistance_periods:
                return 0.0, 0.0
            
            # Use recent data for S/R calculation
            recent_data = data.tail(self.support_resistance_periods)
            
            # Support: Recent low levels
            support_candidates = recent_data['low'].nsmallest(3)
            support = support_candidates.mean()
            
            # Resistance: Recent high levels
            resistance_candidates = recent_data['high'].nlargest(3)
            resistance = resistance_candidates.mean()
            
            return float(support), float(resistance)
            
        except Exception as e:
            logger.error(f"Error calculating support/resistance: {e}")
            return 0.0, 0.0
    
    def detect_price_action_pattern(self, data: pd.DataFrame) -> Tuple[ChartPattern, float]:
        """Detect price action patterns and return confidence."""
        try:
            if len(data) < 20:
                return ChartPattern.CONSOLIDATION, 0.0
            
            current_price = data['close'].iloc[-1]
            support, resistance = self.calculate_support_resistance(data)
            
            # Calculate price position within S/R range
            if resistance > support > 0:
                price_position = (current_price - support) / (resistance - support)
            else:
                price_position = 0.5
            
            # Recent price movement
            price_change_5 = (current_price - data['close'].iloc[-5]) / data['close'].iloc[-5]
            price_change_10 = (current_price - data['close'].iloc[-10]) / data['close'].iloc[-10]
            
            # Volume analysis
            recent_volume = data['volume'].tail(3).mean()
            avg_volume = data['volume'].tail(20).mean()
            volume_surge = recent_volume > avg_volume * self.volume_surge_threshold
            
            # Pattern detection logic
            confidence = 50.0
            
            # Breakout patterns
            if current_price > resistance * (1 + self.breakout_threshold):
                if volume_surge:
                    confidence = 85.0
                else:
                    confidence = 70.0
                return ChartPattern.BREAKOUT_BULLISH, confidence
            
            elif current_price < support * (1 - self.breakout_threshold):
                if volume_surge:
                    confidence = 85.0
                else:
                    confidence = 70.0
                return ChartPattern.BREAKOUT_BEARISH, confidence
            
            # Support bounce pattern
            elif price_position < 0.2 and price_change_5 > 0.01:  # Near support, bouncing up
                confidence = 75.0 if volume_surge else 60.0
                return ChartPattern.SUPPORT_BOUNCE, confidence
            
            # Resistance rejection pattern
            elif price_position > 0.8 and price_change_5 < -0.01:  # Near resistance, rejecting down
                confidence = 75.0 if volume_surge else 60.0
                return ChartPattern.RESISTANCE_REJECTION, confidence
            
            # Reversal patterns
            elif price_change_10 < -0.05 and price_change_5 > 0.02:  # Strong reversal up
                confidence = 70.0 if volume_surge else 55.0
                return ChartPattern.REVERSAL_BULLISH, confidence
            
            elif price_change_10 > 0.05 and price_change_5 < -0.02:  # Strong reversal down
                confidence = 70.0 if volume_surge else 55.0
                return ChartPattern.REVERSAL_BEARISH, confidence
            
            # Trend continuation
            elif abs(price_change_5) > 0.015 and abs(price_change_10) > 0.03:
                if price_change_5 > 0 and price_change_10 > 0:
                    confidence = 65.0 if volume_surge else 50.0
                    return ChartPattern.TREND_CONTINUATION, confidence
            
            # Default to consolidation
            return ChartPattern.CONSOLIDATION, 40.0
            
        except Exception as e:
            logger.error(f"Error detecting price action pattern: {e}")
            return ChartPattern.CONSOLIDATION, 0.0
    
    def analyze_moving_averages(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Analyze moving average alignment and trend strength."""
        try:
            if len(data) < self.ma_long:
                return 'HOLD', 0.0
            
            # Calculate moving averages
            ma_short = data['close'].rolling(self.ma_short).mean().iloc[-1]
            ma_medium = data['close'].rolling(self.ma_medium).mean().iloc[-1]
            ma_long = data['close'].rolling(self.ma_long).mean().iloc[-1]
            current_price = data['close'].iloc[-1]
            
            # Check alignment
            bullish_alignment = current_price > ma_short > ma_medium > ma_long
            bearish_alignment = current_price < ma_short < ma_medium < ma_long
            
            # Calculate trend strength
            if bullish_alignment:
                trend_strength = min(((current_price - ma_long) / ma_long) * 100, 20)
                return 'BUY', 70.0 + trend_strength
            elif bearish_alignment:
                trend_strength = min(((ma_long - current_price) / ma_long) * 100, 20)
                return 'SELL', 70.0 + trend_strength
            else:
                # Mixed signals
                return 'HOLD', 30.0
                
        except Exception as e:
            logger.error(f"Error analyzing moving averages: {e}")
            return 'HOLD', 0.0
    
    def calculate_rsi_divergence(self, data: pd.DataFrame) -> Tuple[str, float]:
        """Calculate RSI and detect divergences."""
        try:
            if 'RSI' not in data.columns or len(data) < 14:
                return 'HOLD', 0.0
            
            current_rsi = data['RSI'].iloc[-1]
            
            # RSI levels
            if current_rsi < 30:
                return 'BUY', 80.0  # Oversold
            elif current_rsi > 70:
                return 'SELL', 80.0  # Overbought
            elif 30 <= current_rsi <= 40:
                return 'BUY', 60.0  # Approaching oversold
            elif 60 <= current_rsi <= 70:
                return 'SELL', 60.0  # Approaching overbought
            else:
                return 'HOLD', 30.0  # Neutral zone
                
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return 'HOLD', 0.0
    
    def analyze_volume_patterns(self, data: pd.DataFrame) -> Tuple[bool, float]:
        """Analyze volume patterns for confirmation."""
        try:
            if 'volume' not in data.columns or len(data) < 10:
                return False, 0.0
            
            recent_volume = data['volume'].tail(3).mean()
            avg_volume = data['volume'].tail(20).mean()
            
            # Volume surge detection
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0
            
            if volume_ratio > self.volume_surge_threshold:
                confidence = min((volume_ratio - 1) * 50, 90)
                return True, confidence
            else:
                return False, max(volume_ratio * 30, 10)
                
        except Exception as e:
            logger.error(f"Error analyzing volume: {e}")
            return False, 0.0
    
    def multi_timeframe_analysis(self, data_5m: pd.DataFrame, data_15m: pd.DataFrame, 
                                data_1h: pd.DataFrame) -> Dict[str, str]:
        """Perform multi-timeframe analysis."""
        try:
            timeframe_signals = {}
            
            # Analyze each timeframe
            timeframes = {
                '5m': data_5m,
                '15m': data_15m,
                '1h': data_1h
            }
            
            for tf, data in timeframes.items():
                if data is not None and len(data) >= 20:
                    ma_signal, ma_confidence = self.analyze_moving_averages(data)
                    rsi_signal, rsi_confidence = self.calculate_rsi_divergence(data)
                    
                    # Combine signals
                    if ma_confidence > 60 and rsi_confidence > 60:
                        if ma_signal == rsi_signal:
                            timeframe_signals[tf] = ma_signal
                        else:
                            timeframe_signals[tf] = 'HOLD'
                    elif ma_confidence > rsi_confidence:
                        timeframe_signals[tf] = ma_signal if ma_confidence > 50 else 'HOLD'
                    else:
                        timeframe_signals[tf] = rsi_signal if rsi_confidence > 50 else 'HOLD'
                else:
                    timeframe_signals[tf] = 'HOLD'
            
            return timeframe_signals
            
        except Exception as e:
            logger.error(f"Error in multi-timeframe analysis: {e}")
            return {'5m': 'HOLD', '15m': 'HOLD', '1h': 'HOLD'}
    
    def calculate_risk_reward(self, entry_price: float, target_price: float, 
                            stop_loss: float) -> float:
        """Calculate risk-reward ratio."""
        try:
            if entry_price <= 0 or target_price <= 0 or stop_loss <= 0:
                return 0.0
            
            reward = abs(target_price - entry_price)
            risk = abs(entry_price - stop_loss)
            
            if risk > 0:
                return reward / risk
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating risk-reward: {e}")
            return 0.0
    
    def comprehensive_chart_analysis(self, data_5m: pd.DataFrame, data_15m: pd.DataFrame,
                                   data_1h: pd.DataFrame, symbol: str) -> ChartAnalysis:
        """Perform comprehensive chart analysis across multiple timeframes."""
        try:
            # Use 15m as primary timeframe for analysis
            primary_data = data_15m if data_15m is not None and len(data_15m) >= 20 else data_5m
            
            if primary_data is None or len(primary_data) < 20:
                return ChartAnalysis(
                    signal='HOLD',
                    confidence=0.0,
                    pattern=ChartPattern.CONSOLIDATION,
                    entry_reason='Insufficient data',
                    support_level=0.0,
                    resistance_level=0.0,
                    target_price=0.0,
                    stop_loss=0.0,
                    timeframe_alignment={'5m': 'HOLD', '15m': 'HOLD', '1h': 'HOLD'},
                    volume_confirmation=False,
                    risk_reward_ratio=0.0
                )
            
            current_price = primary_data['close'].iloc[-1]
            
            # 1. Pattern detection
            pattern, pattern_confidence = self.detect_price_action_pattern(primary_data)
            
            # 2. Moving average analysis
            ma_signal, ma_confidence = self.analyze_moving_averages(primary_data)
            
            # 3. RSI analysis
            rsi_signal, rsi_confidence = self.calculate_rsi_divergence(primary_data)
            
            # 4. Volume confirmation
            volume_confirmed, volume_confidence = self.analyze_volume_patterns(primary_data)
            
            # 5. Multi-timeframe alignment
            timeframe_signals = self.multi_timeframe_analysis(data_5m, data_15m, data_1h)
            
            # 6. Support and resistance levels
            support, resistance = self.calculate_support_resistance(primary_data)
            
            # Determine overall signal
            signals = [ma_signal, rsi_signal]
            confidences = [ma_confidence, rsi_confidence, pattern_confidence]
            
            # Count signal alignment
            buy_votes = signals.count('BUY')
            sell_votes = signals.count('SELL')
            
            if buy_votes > sell_votes:
                overall_signal = 'BUY'
            elif sell_votes > buy_votes:
                overall_signal = 'SELL'
            else:
                overall_signal = 'HOLD'
            
            # Calculate overall confidence
            base_confidence = np.mean(confidences)
            
            # Boost confidence for timeframe alignment
            aligned_timeframes = sum(1 for tf_signal in timeframe_signals.values() 
                                   if tf_signal == overall_signal)
            alignment_boost = aligned_timeframes * 5  # 5% per aligned timeframe
            
            # Volume confirmation boost
            volume_boost = 10 if volume_confirmed else 0
            
            overall_confidence = min(base_confidence + alignment_boost + volume_boost, 95.0)
            
            # Calculate target and stop loss
            if overall_signal == 'BUY':
                target_price = resistance if resistance > current_price else current_price * 1.02
                stop_loss = support if support < current_price else current_price * 0.98
            elif overall_signal == 'SELL':
                target_price = support if support < current_price else current_price * 0.98
                stop_loss = resistance if resistance > current_price else current_price * 1.02
            else:
                target_price = current_price
                stop_loss = current_price
            
            # Calculate risk-reward ratio
            risk_reward = self.calculate_risk_reward(current_price, target_price, stop_loss)
            
            # Create entry reason
            reasons = []
            if pattern_confidence > 60:
                reasons.append(f"{pattern.value}")
            if ma_confidence > 60:
                reasons.append(f"MA trend")
            if rsi_confidence > 60:
                reasons.append(f"RSI signal")
            if volume_confirmed:
                reasons.append("Volume surge")
            
            entry_reason = " + ".join(reasons) if reasons else "Weak signals"
            
            return ChartAnalysis(
                signal=overall_signal,
                confidence=overall_confidence,
                pattern=pattern,
                entry_reason=entry_reason,
                support_level=support,
                resistance_level=resistance,
                target_price=target_price,
                stop_loss=stop_loss,
                timeframe_alignment=timeframe_signals,
                volume_confirmation=volume_confirmed,
                risk_reward_ratio=risk_reward
            )
            
        except Exception as e:
            logger.error(f"Error in comprehensive chart analysis for {symbol}: {e}")
            return ChartAnalysis(
                signal='HOLD',
                confidence=0.0,
                pattern=ChartPattern.CONSOLIDATION,
                entry_reason=f'Analysis error: {e}',
                support_level=0.0,
                resistance_level=0.0,
                target_price=0.0,
                stop_loss=0.0,
                timeframe_alignment={'5m': 'HOLD', '15m': 'HOLD', '1h': 'HOLD'},
                volume_confirmation=False,
                risk_reward_ratio=0.0
            )
