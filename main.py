"""
Main trading bot application.
"""

import os
import sys
import time
import logging
from typing import Dict, List
import pandas as pd
from dotenv import load_dotenv

# Load environment variables first
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from strategies.sma_crossover import SMACrossoverStrategy
from strategies.rsi_strategy import RSIStrategy
from strategies.advanced_ai_strategy import AdvancedAIStrategy
from strategies.enhanced_futures_strategy import EnhancedFuturesStrategy
from strategies.scalping_strategy import ScalpingStrategy
from risk_management import RiskManager
from portfolio import Portfolio
from market_monitor import MarketMonitor
from profit_manager import ProfitManager
from trade_logger import TradeLogger
from altcoin_scanner import AltcoinScanner
from utils import setup_logging, load_config, get_timestamp

def debug_list_types(lst, name="list"):
    """Debug function to check types in a list."""
    if not isinstance(lst, list):
        logging.warning(f"{name} is not a list: {type(lst)}")
        return False

    for i, item in enumerate(lst):
        if not isinstance(item, str):
            logging.error(f"{name}[{i}] is not a string: {type(item)} = {item}")
            return False
    return True

class TradingBot:
    """Main trading bot class."""
    
    def __init__(self, config_path: str = 'config/config.yaml'):
        """
        Initialize trading bot.
        
        Args:
            config_path: Path to configuration file
        """
        # Load environment variables
        load_dotenv()
        
        # Load configuration
        self.config = load_config(config_path)
        if not self.config:
            raise ValueError(f"Failed to load configuration from {config_path}")
        
        # Setup logging
        log_config = self.config.get('logging', {})
        setup_logging(
            log_level=log_config.get('level', 'INFO'),
            log_file=log_config.get('file', 'logs/trading_bot.log')
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Trading Bot initializing...")
        
        # Initialize components
        self.trading_mode = self.config['trading']['mode']
        self.base_currency = self.config['trading']['base_currency']
        self.timeframe = self.config['timeframes']['primary']

        # Initialize dynamic altcoin scanning
        self.use_dynamic_scanning = self.config['trading'].get('use_dynamic_scanning', True)
        self.max_symbols = self.config['trading'].get('max_symbols', 50)  # None = ALL altcoins

        if self.use_dynamic_scanning:
            self.logger.info("Dynamic altcoin scanning enabled")
            # Will be initialized after Binance client
            self.altcoin_scanner = None
            self.symbols = []  # Will be populated by scanner
        else:
            self.symbols = self.config['trading']['symbols']
            self.altcoin_scanner = None
        
        # Initialize Binance client - TESTNET ONLY
        self.client = BinanceClient(testnet=True)  # Always use testnet for safety

        if self.trading_mode == 'testnet':
            self.logger.info("TESTNET MODE ENABLED - Using Binance Futures Testnet")
            self.logger.info("Testnet URL: https://testnet.binancefuture.com")

        # Initialize dynamic altcoin scanner if enabled
        if self.use_dynamic_scanning:
            scanner_config = {
                'min_volume_24h': 500000,  # $500K minimum volume
                'min_price': 0.0001,       # Minimum price
                'max_price': 10000,        # Maximum price
                'quote_asset': 'USDT',     # Only USDT pairs
                'scan_interval_hours': 24  # Rescan daily
            }
            self.altcoin_scanner = AltcoinScanner(self.client, scanner_config)

            # Scan for altcoins
            if self.max_symbols is None:
                self.logger.info("🪙 Scanning for ALL available altcoins...")
            else:
                self.logger.info(f"🪙 Scanning for top {self.max_symbols} altcoins...")

            self.symbols = self.altcoin_scanner.get_top_altcoins(self.max_symbols)

            if not self.symbols:
                self.logger.error("No altcoins found! Falling back to configured symbols")
                self.symbols = self.config['trading'].get('symbols', [])
            else:
                # Ensure all symbols are strings (safety check)
                self.symbols = [str(symbol) for symbol in self.symbols if isinstance(symbol, str)]

                if self.max_symbols is None:
                    self.logger.info(f"🚀 Trading ALL {len(self.symbols)} available altcoins!")
                else:
                    self.logger.info(f"🚀 Trading top {len(self.symbols)} altcoins")

                self.logger.info(f"📊 Sample altcoins: {', '.join(self.symbols[:10])}")
                if len(self.symbols) > 10:
                    self.logger.info(f"📊 ... and {len(self.symbols) - 10} more altcoins")

        # Initialize portfolio with actual testnet balance
        try:
            # Get actual balance from testnet account
            account_info = self.client.futures_get_account_info()
            if account_info:
                assets = account_info.get('assets', [])
                usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)

                if usdt_asset:
                    actual_balance = float(usdt_asset.get('availableBalance', 0))
                    self.logger.info(f"Syncing portfolio with actual testnet balance: {actual_balance:.2f} USDT")
                    initial_balance = actual_balance
                else:
                    self.logger.warning("No USDT asset found in account, using default balance")
                    initial_balance = float(os.getenv('INITIAL_BALANCE', 10000))
            else:
                self.logger.warning("Could not fetch account info, using default balance")
                initial_balance = float(os.getenv('INITIAL_BALANCE', 10000))
        except Exception as e:
            self.logger.error(f"Error fetching testnet balance: {e}")
            self.logger.info("Using default balance from environment")
            initial_balance = float(os.getenv('INITIAL_BALANCE', 10000))

        self.portfolio = Portfolio(initial_balance, self.base_currency)

        # Initialize enhanced risk manager
        self.risk_manager = RiskManager(self.config['risk_management'])

        # STRICT POSITION MANAGEMENT ($100 MAX + 3 POSITION LIMIT)
        self.active_positions = {}      # Track active positions {symbol: position_info}
        self.max_positions = self.risk_manager.max_concurrent_positions  # Get from risk manager
        self.max_trade_value = self.risk_manager.max_position_value_usd  # Get from risk manager
        self.position_profits = {}      # Track profits per position {symbol: profit_usdt}

        # CONTEXT.TXT COOLDOWN SYSTEM
        self.last_trade_time = {}       # Track last trade time per symbol
        self.cooldown_minutes = 15      # 15-30 minute cooldown as per context.txt
        self.global_cooldown = None     # Global cooldown for market conditions

        # FORCE ENTRY MODE (for when positions are stuck)
        self.force_entry_mode = False   # Set to True to ignore position limits temporarily

        # CHART-BASED TRADING ALGORITHM COMPONENTS
        from src.chart_analysis_engine import ChartAnalysisEngine
        from src.single_position_manager import SinglePositionManager
        from src.multi_timeframe_analysis import MultiTimeframeAnalysis
        from src.advanced_signal_filter import AdvancedSignalFilter
        from src.market_regime_detector import MarketRegimeDetector
        from src.scalping_fallback import ScalpingFallback

        # Initialize chart-based trading components
        chart_analysis_config = self.config.get('chart_analysis', {
            'support_resistance_periods': 20,
            'breakout_threshold': 0.005,
            'volume_surge_threshold': 1.5,
            'min_confidence_threshold': 70,
            'min_risk_reward_ratio': 1.5,
            'ma_short': 10,
            'ma_medium': 20,
            'ma_long': 50
        })

        single_position_config = self.config.get('single_position', {
            'min_time_between_trades': 2
        })

        # Initialize chart-based components
        self.chart_analyzer = ChartAnalysisEngine(chart_analysis_config)
        self.position_manager = SinglePositionManager(single_position_config)
        self.multi_timeframe = MultiTimeframeAnalysis(self.client)

        # Keep advanced components for fallback (but disable scalping when position active)
        signal_filter_config = self.config.get('advanced_signal_filter', {
            'min_signal_strength': 60,
            'min_confidence': 65,
            'max_risk_score': 40,
            'min_overall_score': 70
        })

        scalping_config = self.config.get('scalping_fallback', {
            'min_price_movement': 0.002,
            'quick_profit_target': 0.005,
            'scalp_stop_loss': 0.003,
            'max_hold_minutes': 10,
            'rsi_oversold_scalp': 35,
            'rsi_overbought_scalp': 65
        })

        self.signal_filter = AdvancedSignalFilter(signal_filter_config)
        self.scalping_fallback = ScalpingFallback(scalping_config)

        # Override position limits for single position trading
        self.max_positions = 1  # SINGLE POSITION ONLY

        self.logger.info("📊 Chart-Based Single Position Trading Algorithm initialized")
        self.logger.info("🎯 Maximum 1 concurrent position across all altcoins")
        self.logger.info("📈 Multi-timeframe chart analysis enabled (5m, 15m, 1h)")

        # INTERVAL-BASED PROFIT TAKING CONFIGURATION
        risk_config = self.config.get('risk_management', {})
        self.min_profit_hold_minutes = risk_config.get('min_profit_hold_minutes', 5)
        self.breakeven_hold_minutes = risk_config.get('breakeven_hold_minutes', 15)
        self.max_hold_minutes = risk_config.get('max_hold_minutes', 30)
        self.max_acceptable_loss = risk_config.get('max_acceptable_loss', 2.0)

        self.logger.info(f"STRICT LIMITS: MAX ${self.max_trade_value} per trade, MAX {self.max_positions} positions")
        self.logger.info(f"PROFIT INTERVALS: {self.min_profit_hold_minutes}min profit, {self.breakeven_hold_minutes}min breakeven, {self.max_hold_minutes}min max hold")

        # Initialize market monitor
        market_monitor_config = self.config.get('market_monitor', {})
        market_monitor_config['major_symbols'] = ['BTCUSDT', 'ETHUSDT']
        market_monitor_config['altcoin_sample'] = [s for s in self.symbols if s not in {'BTCUSDT', 'ETHUSDT', 'BNBUSDT'}][:10]
        self.market_monitor = MarketMonitor(self.client, market_monitor_config)

        # Initialize profit manager
        profit_config = self.config.get('profit_management', {})
        self.profit_manager = ProfitManager(profit_config)

        # Initialize trade logger
        logger_config = self.config.get('trade_logging', {})
        logger_config['telegram_token'] = os.getenv('TELEGRAM_TOKEN', '')
        logger_config['telegram_chat_id'] = os.getenv('TELEGRAM_CHAT_ID', '')
        self.trade_logger = TradeLogger(logger_config)

        # Initialize strategies
        self.strategies = self._initialize_strategies()

        # Bot state
        self.running = False
        self.last_update = None
        self.position_entry_times = {}  # Track when positions were opened
        
        # Final safety check - ensure all symbols are strings before logging
        self.symbols = [str(symbol) for symbol in self.symbols if isinstance(symbol, str)]

        self.logger.info(f"Trading Bot initialized in {self.trading_mode} mode")
        self.logger.info(f"Monitoring symbols: {', '.join(self.symbols)}")
        self.logger.info(f"Active strategies: {', '.join(s.name for s in self.strategies)}")

        # Trading mode warnings
        if self.trading_mode == 'live':
            self.logger.warning("=" * 60)
            self.logger.warning("⚠️  LIVE TRADING MODE ENABLED ⚠️")
            self.logger.warning("This bot will place REAL trades with REAL money!")
            self.logger.warning("Make sure you understand the risks involved.")
            self.logger.warning("Start with small position sizes and monitor closely.")
            self.logger.warning("=" * 60)
        elif self.trading_mode == 'testnet':
            self.logger.info("=" * 60)
            self.logger.info("TESTNET MODE ENABLED")
            self.logger.info("Using Binance Futures Testnet for demo trading")
            self.logger.info("No real money at risk - perfect for testing!")
            self.logger.info("Testnet URL: https://testnet.binancefuture.com")
            self.logger.info("=" * 60)
    
    def _initialize_strategies(self) -> List:
        """Initialize trading strategies."""
        strategies = []
        strategy_configs = self.config.get('strategies', {})

        # Enhanced Futures Strategy - ALTCOIN FOCUSED!
        if strategy_configs.get('enhanced_futures', {}).get('enabled', True):  # Enabled by default
            futures_config = strategy_configs.get('enhanced_futures', {})

            # Filter symbols to exclude major coins (as per context.txt) - with safety check
            excluded = {'BTCUSDT', 'ETHUSDT', 'BNBUSDT'}
            altcoin_symbols = [str(s) for s in self.symbols if isinstance(s, str) and s not in excluded]

            # Set up enhanced futures strategy with context.txt guidelines
            futures_config.update({
                'ema_fast_period': futures_config.get('ema_fast_period', 12),
                'ema_slow_period': futures_config.get('ema_slow_period', 26),
                'rsi_period': futures_config.get('rsi_period', 14),
                'rsi_oversold': futures_config.get('rsi_oversold', 30),
                'rsi_overbought': futures_config.get('rsi_overbought', 70),
                'volume_spike_threshold': futures_config.get('volume_spike_threshold', 1.5),
                'cooldown_minutes': futures_config.get('cooldown_minutes', 15),  # 15 min cooldown from context.txt
                'require_macd_confirmation': futures_config.get('require_macd_confirmation', True)
            })

            strategies.append(EnhancedFuturesStrategy(self.client, futures_config))
            self.logger.info("Enhanced Futures Strategy enabled - FOLLOWING CONTEXT.TXT GUIDELINES!")

            # Debug check before join
            debug_list_types(altcoin_symbols, "altcoin_symbols")
            self.logger.info(f"Monitoring {len(altcoin_symbols)} altcoins: {', '.join(altcoin_symbols[:3])}...")
            self.logger.info("Using EMA + RSI combo with volume confirmation and market awareness")

        # NEW ADVANCED STRATEGIES (KEEP INTERVAL-BASED PROFIT TAKING)

        # Moving Average Crossover Strategy
        if strategy_configs.get('moving_average_crossover', {}).get('enabled', False):
            from src.strategies.moving_average_crossover import MovingAverageCrossoverStrategy
            ma_config = strategy_configs.get('moving_average_crossover', {})
            strategies.append(MovingAverageCrossoverStrategy(self.client, ma_config))
            self.logger.info(f"Moving Average Crossover Strategy enabled: {ma_config.get('short_window', 50)}/{ma_config.get('long_window', 200)}")

        # Bollinger Bands Strategy
        if strategy_configs.get('bollinger_bands', {}).get('enabled', False):
            from src.strategies.bollinger_bands import BollingerBandsStrategy
            bb_config = strategy_configs.get('bollinger_bands', {})
            strategies.append(BollingerBandsStrategy(self.client, bb_config))
            self.logger.info(f"Bollinger Bands Strategy enabled: {bb_config.get('window', 20)}p, {bb_config.get('num_std', 2)}σ")

        # Mean Reversion Strategy
        if strategy_configs.get('mean_reversion', {}).get('enabled', False):
            from src.strategies.mean_reversion import MeanReversionStrategy
            mr_config = strategy_configs.get('mean_reversion', {})
            strategies.append(MeanReversionStrategy(self.client, mr_config))
            self.logger.info(f"Mean Reversion Strategy enabled: {mr_config.get('window', 20)}p, {mr_config.get('num_std', 1.5)}σ")

        # Breakout Strategy
        if strategy_configs.get('breakout', {}).get('enabled', False):
            from src.strategies.breakout import BreakoutStrategy
            breakout_config = strategy_configs.get('breakout', {})
            strategies.append(BreakoutStrategy(self.client, breakout_config))
            self.logger.info(f"Breakout Strategy enabled: {breakout_config.get('window', 20)}p lookback")

        # Advanced AI Strategy - ALTCOIN FOCUSED!
        if strategy_configs.get('advanced_ai', {}).get('enabled', False):  # Disabled by default now
            ai_config = strategy_configs.get('advanced_ai', {})
            # Add altcoin-specific scanner configuration
            ai_config['scanner'] = {
                'base_currency': self.base_currency,
                'min_volume_24h': ai_config.get('min_volume_24h', 500000),  # Lower for altcoins
                'min_confidence': ai_config.get('min_confidence', 65),      # Lower threshold
                'top_n_tokens': ai_config.get('top_n_tokens', 30),          # More altcoins
                'altcoin_focus': ai_config.get('altcoin_focus', True),      # Altcoin mode
                'min_price': ai_config.get('min_price', 0.001),
                'max_price': ai_config.get('max_price', 500)
            }
            # Filter symbols to exclude major coins - with safety check
            excluded = {'BTCUSDT', 'ETHUSDT', 'BNBUSDT'}
            altcoin_symbols = [str(s) for s in self.symbols if isinstance(s, str) and s not in excluded]
            ai_config['initial_symbols'] = altcoin_symbols
            strategies.append(AdvancedAIStrategy(self.client, ai_config))
            self.logger.info("Advanced AI Strategy enabled - ALTCOIN FOCUS MODE!")

            # Debug check before join
            debug_list_types(altcoin_symbols, "ai_altcoin_symbols")
            self.logger.info(f"Monitoring {len(altcoin_symbols)} altcoins: {', '.join(altcoin_symbols[:3])}...")

        # SMA Crossover Strategy
        if strategy_configs.get('sma_crossover', {}).get('enabled', False):
            sma_config = strategy_configs['sma_crossover']
            strategies.append(SMACrossoverStrategy(sma_config))
            self.logger.info("SMA Crossover strategy enabled")

        # RSI Strategy
        if strategy_configs.get('rsi_strategy', {}).get('enabled', False):
            rsi_config = strategy_configs['rsi_strategy']
            strategies.append(RSIStrategy(rsi_config))
            self.logger.info("RSI strategy enabled")

        # Scalping Strategy for unclear market conditions
        if strategy_configs.get('scalping', {}).get('enabled', True):  # Enabled by default
            scalp_config = strategy_configs.get('scalping', {})
            scalp_config.update({
                'scalp_timeframe': scalp_config.get('scalp_timeframe', '1m'),
                'min_profit_pct': scalp_config.get('min_profit_pct', 0.3),
                'max_loss_pct': scalp_config.get('max_loss_pct', 0.15),
                'scalp_position_size': scalp_config.get('scalp_position_size', 0.02),
                'max_scalp_positions': scalp_config.get('max_scalp_positions', 3)
            })

            strategies.append(ScalpingStrategy(self.client, scalp_config))
            self.logger.info("Scalping Strategy enabled - ACTIVE DURING UNCLEAR MARKET CONDITIONS!")
            self.logger.info("Will scalp on 1m timeframe with 0.3% profit targets")

        if not strategies:
            self.logger.warning("No strategies enabled!")

        return strategies
    
    def get_market_data(self, symbol: str) -> pd.DataFrame:
        """Get market data for a symbol."""
        try:
            data = self.client.get_historical_klines(
                symbol=symbol,
                interval=self.timeframe,
                limit=100  # Get enough data for indicators
            )
            return data
        except Exception as e:
            self.logger.error(f"Error getting market data for {symbol}: {e}")
            return pd.DataFrame()
    
    def process_signals(self, symbol: str, data: pd.DataFrame):
        """Process trading signals for a symbol with enhanced risk management."""
        if data.empty:
            return

        current_price = data['close'].iloc[-1]

        # Update portfolio with current prices
        self.portfolio.update_position_pnl(symbol, current_price)

        # Get comprehensive market analysis (for logging only - not blocking trades)
        market_analysis = self.market_monitor.get_comprehensive_market_analysis()
        if not market_analysis['healthy']:
            self.logger.info(f"Market conditions noted: {market_analysis['reason']} - CONTINUING ANYWAY")

        # Enhanced market condition assessment for this specific symbol (for logging only)
        symbol_conditions = self.risk_manager.assess_market_conditions(data, data['volume'] if 'volume' in data.columns else None)
        if not symbol_conditions['healthy']:
            self.logger.info(f"Symbol conditions noted for {symbol}: {symbol_conditions['reason']} - CONTINUING ANYWAY")

        # Check risk management
        if self.risk_manager.should_stop_trading():
            self.logger.warning("Trading stopped due to risk limits")
            return

        # Check existing position for enhanced exit conditions
        position = self.portfolio.positions.get(symbol)
        if position:
            # Get profit manager recommendation
            market_conditions_for_profit = {
                'volatility': symbol_conditions['conditions'].get('volatility', 0),
                'trend_strength': symbol_conditions['conditions'].get('price_range', 0)
            }

            exit_recommendation = self.profit_manager.get_exit_recommendation(
                symbol, current_price, market_conditions_for_profit
            )

            # Act on exit recommendation
            if exit_recommendation['action'] == 'CLOSE_FULL':
                self.close_position(symbol, current_price, exit_recommendation['reason'])
                return
            elif exit_recommendation['action'] == 'CLOSE_PARTIAL':
                # Handle partial close (would need implementation in close_position)
                self.logger.info(f"Partial close recommended for {symbol}: {exit_recommendation['reason']}")
                # For now, treat as full close following context.txt rule
                self.close_position(symbol, current_price, exit_recommendation['reason'])
                return

            # Fallback to original exit conditions
            if self.risk_manager.check_immediate_profit_exit(position, current_price):
                self.close_position(symbol, current_price, "Immediate Profit Exit (Context.txt Rule)")
                return

            if self.risk_manager.check_stop_loss(position, current_price):
                self.close_position(symbol, current_price, "Stop Loss")
                return

        # Check for scalping opportunities when market conditions are unclear
        scalping_activated = False
        for strategy in self.strategies:
            if isinstance(strategy, ScalpingStrategy):
                if strategy.should_activate(market_analysis, symbol_conditions):
                    scalping_activated = True
                    self.logger.info(f"Scalping activated for {symbol} due to unclear market conditions")

                    # Generate scalping signals
                    scalp_signals = strategy.generate_signals(symbol, data)
                    for scalp_signal in scalp_signals:
                        if scalp_signal['action'] in ['BUY', 'SELL'] and not position:
                            self.open_scalp_position(symbol, scalp_signal, data)

                    # Check scalp exits
                    exit_signals = strategy.check_scalp_exits()
                    for exit_signal in exit_signals:
                        if exit_signal['symbol'] == symbol:
                            self.close_position(symbol, current_price, exit_signal['reason'])

                    break  # Only use scalping when activated

        # Generate signals from regular strategies (if not scalping)
        if not scalping_activated:
            for strategy in self.strategies:
                if not isinstance(strategy, ScalpingStrategy):
                    signal = strategy.generate_signal(data, symbol)

                    if signal == 'BUY' and not position:
                        self.open_position(symbol, 'BUY', current_price, strategy.name, data)
                    elif signal == 'SELL':
                        if position and position['side'] == 'BUY':
                            self.close_position(symbol, current_price, f"{strategy.name} Signal")
                        elif not position:
                            self.open_position(symbol, 'SELL', current_price, strategy.name, data)
    
    def open_scalp_position(self, symbol: str, scalp_signal: Dict, data: pd.DataFrame = None):
        """Open a scalping position with specific scalping parameters."""
        try:
            side = scalp_signal['action']
            price = scalp_signal['price']
            quantity = scalp_signal['quantity']
            take_profit = scalp_signal['take_profit']
            stop_loss = scalp_signal['stop_loss']

            self.logger.info(f"Opening scalp position: {side} {symbol} @ {price:.6f}")
            self.logger.info(f"Scalp targets - TP: {take_profit:.6f}, SL: {stop_loss:.6f}")

            # Calculate actual quantity based on portfolio
            portfolio_value = self.portfolio.get_total_value({symbol: price})
            actual_quantity = (portfolio_value * quantity) / price

            # Set leverage for scalping (lower leverage for safety)
            leverage = 3  # Conservative leverage for scalping

            # Place the order
            order_result = self.client.futures_place_market_order(symbol, side, actual_quantity)

            if order_result:
                # Add position to portfolio with scalping metadata
                self.portfolio.add_position(
                    symbol=symbol,
                    side=side,
                    quantity=actual_quantity,
                    entry_price=price,
                    timestamp=pd.Timestamp.now()
                )

                # Set leverage
                self.client.futures_change_leverage(symbol, leverage)

                # Log the trade
                self.trade_logger.log_trade({
                    'timestamp': pd.Timestamp.now(),
                    'symbol': symbol,
                    'side': side,
                    'quantity': actual_quantity,
                    'price': price,
                    'leverage': leverage,
                    'strategy': 'SCALPING',
                    'reason': scalp_signal['reason'],
                    'take_profit': take_profit,
                    'stop_loss': stop_loss
                })

                # Update scalping strategy position tracking
                for strategy in self.strategies:
                    if isinstance(strategy, ScalpingStrategy):
                        strategy.update_scalp_positions(symbol, side, price)
                        break

                self.logger.info(f"Scalp position opened: {side} {actual_quantity:.6f} {symbol} @ {price:.6f}")

        except Exception as e:
            self.logger.error(f"Error opening scalp position for {symbol}: {e}")

    def check_profit_targets(self):
        """Check all active positions for profit targets and auto-exit."""
        try:
            positions_to_close = []

            for symbol, position in self.active_positions.items():
                try:
                    # Validate position data first
                    required_fields = ['entry_price', 'quantity', 'side', 'leverage', 'entry_time']
                    for field in required_fields:
                        if field not in position:
                            self.logger.error(f"❌ Missing {field} in position data for {symbol}")
                            continue

                    # Get current price with error handling
                    ticker = self.client.client.futures_symbol_ticker(symbol=symbol)
                    if not ticker or 'price' not in ticker:
                        self.logger.warning(f"⚠️  Could not get current price for {symbol}")
                        continue

                    current_price = float(ticker['price'])

                    # Calculate current profit/loss in USDT with safety checks
                    entry_price = position['entry_price']
                    quantity = position['quantity']
                    side = position['side']
                    leverage = position['leverage']

                    # SAFETY CHECK: Prevent division by zero
                    if entry_price <= 0 or current_price <= 0 or quantity <= 0:
                        self.logger.warning(f"⚠️  Invalid price data for {symbol}: entry={entry_price}, current={current_price}, qty={quantity}")
                        continue

                    if side == 'BUY':
                        price_diff = current_price - entry_price
                    else:  # SELL
                        price_diff = entry_price - current_price

                    # Calculate profit in USDT (considering leverage) with safety check
                    try:
                        profit_usdt = (price_diff / entry_price) * (quantity * entry_price) * leverage
                        self.position_profits[symbol] = profit_usdt
                    except ZeroDivisionError:
                        self.logger.error(f"❌ Division by zero error for {symbol}: entry_price={entry_price}")
                        continue

                    # INTERVAL-BASED PROFIT TAKING (every 5 minutes check)
                    time_held = pd.Timestamp.now() - position['entry_time']
                    time_held_minutes = time_held.total_seconds() / 60

                    # Check profit conditions based on configurable time intervals
                    should_close = False
                    close_reason = ""

                    # Stage 1: After min_profit_hold_minutes, take any profit
                    if time_held_minutes >= self.min_profit_hold_minutes:
                        if profit_usdt > 0:
                            should_close = True
                            close_reason = f"Interval profit-taking: {profit_usdt:+.2f} USDT after {time_held_minutes:.1f}min"

                    # Stage 2: After breakeven_hold_minutes, close if loss is acceptable
                    if time_held_minutes >= self.breakeven_hold_minutes:
                        if profit_usdt > -self.max_acceptable_loss:
                            should_close = True
                            close_reason = f"Breakeven exit: {profit_usdt:+.2f} USDT after {time_held_minutes:.1f}min (loss < ${self.max_acceptable_loss})"

                    # Stage 3: After max_hold_minutes, force close regardless of profit/loss
                    if time_held_minutes >= self.max_hold_minutes:
                        should_close = True
                        close_reason = f"Force close: {profit_usdt:+.2f} USDT after {time_held_minutes:.1f}min (max hold time reached)"

                    if should_close:
                        self.logger.info(f"💰 {close_reason}")
                        positions_to_close.append(symbol)
                    else:
                        # Log current status with next action time
                        if time_held_minutes < self.min_profit_hold_minutes:
                            next_action = f"profit check in {self.min_profit_hold_minutes - time_held_minutes:.1f}min"
                        elif time_held_minutes < self.breakeven_hold_minutes:
                            next_action = f"breakeven check in {self.breakeven_hold_minutes - time_held_minutes:.1f}min"
                        else:
                            next_action = f"force close in {self.max_hold_minutes - time_held_minutes:.1f}min"

                        self.logger.info(f"📊 {symbol}: {profit_usdt:+.2f} USDT, held: {time_held_minutes:.1f}min ({next_action})")

                except Exception as e:
                    self.logger.error(f"Error checking profit for {symbol}: {e}")

            # Close profitable positions
            for symbol in positions_to_close:
                current_price = float(self.client.client.futures_symbol_ticker(symbol=symbol)['price'])
                self.close_position(symbol, current_price, f"Profit target reached: {self.position_profits[symbol]:.2f} USDT")

        except Exception as e:
            self.logger.error(f"Error checking profit targets: {e}")

    def cleanup_invalid_positions(self):
        """Clean up positions with invalid data (zero quantity, zero price, etc.)."""
        try:
            positions_to_remove = []

            for symbol, position in self.active_positions.items():
                # Check for invalid position data
                quantity = position.get('quantity', 0)
                entry_price = position.get('entry_price', 0)

                if quantity <= 0 or entry_price <= 0:
                    self.logger.warning(f"🧹 Cleaning up invalid position for {symbol}: qty={quantity}, price={entry_price}")
                    positions_to_remove.append(symbol)

            # Remove invalid positions
            for symbol in positions_to_remove:
                if symbol in self.active_positions:
                    del self.active_positions[symbol]
                if symbol in self.position_profits:
                    del self.position_profits[symbol]

                # Also clean up from portfolio if it exists
                if hasattr(self, 'portfolio') and symbol in self.portfolio.positions:
                    try:
                        self.portfolio.close_position(symbol, 0)  # Close with zero price to clean up
                        self.logger.info(f"🧹 Cleaned up portfolio position for {symbol}")
                    except:
                        pass  # Ignore errors during cleanup

                self.logger.info(f"✅ Removed invalid position for {symbol}")

            if positions_to_remove:
                self.logger.info(f"🧹 Cleaned up {len(positions_to_remove)} invalid positions")
                self.logger.info(f"📊 Valid active positions: {len(self.active_positions)}/{self.max_positions}")

                # Enable force entry mode temporarily if we had invalid positions
                if len(self.active_positions) == 0:
                    self.force_entry_mode = True
                    self.logger.info(f"⚡ AUTO FORCE ENTRY: Enabled due to position cleanup - ready for new trades!")

        except Exception as e:
            self.logger.error(f"Error cleaning up positions: {e}")

    def sync_positions_with_exchange(self):
        """Sync active positions with actual exchange positions."""
        try:
            # Get actual positions from exchange
            exchange_positions = self.client.futures_get_positions()

            if not exchange_positions:
                self.logger.info("🔄 No positions found on exchange, clearing all local positions")
                self.active_positions.clear()
                self.position_profits.clear()
                return

            # Filter to only positions with actual quantity
            real_positions = {}
            for pos in exchange_positions:
                symbol = pos.get('symbol', '')
                quantity = float(pos.get('positionAmt', 0))

                if abs(quantity) > 0:  # Has actual position
                    real_positions[symbol] = {
                        'quantity': abs(quantity),
                        'side': 'BUY' if quantity > 0 else 'SELL',
                        'entry_price': float(pos.get('entryPrice', 0)),
                        'unrealized_pnl': float(pos.get('unRealizedProfit', 0))
                    }

            # Update active positions to match exchange
            symbols_to_remove = []
            for symbol in self.active_positions:
                if symbol not in real_positions:
                    symbols_to_remove.append(symbol)

            for symbol in symbols_to_remove:
                self.logger.info(f"🔄 Removing {symbol} - not found on exchange")
                if symbol in self.active_positions:
                    del self.active_positions[symbol]
                if symbol in self.position_profits:
                    del self.position_profits[symbol]

            self.logger.info(f"🔄 Position sync complete: {len(real_positions)} real positions on exchange")

        except Exception as e:
            self.logger.error(f"Error syncing positions with exchange: {e}")

    def get_time_since_last_trade(self) -> int:
        """Get minutes since last successful trade."""
        try:
            if self.last_successful_trade_time is None:
                return 999  # Large number to indicate no recent trades

            time_diff = pd.Timestamp.now() - self.last_successful_trade_time
            return int(time_diff.total_seconds() / 60)

        except Exception as e:
            self.logger.error(f"Error calculating time since last trade: {e}")
            return 999

    def check_market_conditions(self) -> bool:
        """
        CONTEXT.TXT MARKET CONDITION AWARENESS
        Check if market conditions are healthy for trading.
        """
        try:
            # Get major altcoin data for market assessment
            major_altcoins = ['ADAUSDT', 'SOLUSDT', 'DOGEUSDT', 'LINKUSDT']
            unhealthy_signals = 0
            total_checked = 0

            for symbol in major_altcoins:
                try:
                    # Get recent data
                    data = self.client.get_historical_klines(symbol, '5m', 50)
                    if data.empty:
                        continue

                    total_checked += 1

                    # Calculate indicators
                    data['RSI'] = self.calculate_rsi(data['close'])
                    data['volume_avg'] = data['volume'].rolling(20).mean()

                    latest = data.iloc[-1]

                    # CONTEXT.TXT unhealthy market signals
                    # 1. Low volume
                    if latest['volume'] < latest['volume_avg'] * 0.5:
                        unhealthy_signals += 1
                        self.logger.info(f"⚠️  {symbol}: Low volume detected")

                    # 2. RSI near neutral (no strength)
                    if 40 <= latest['RSI'] <= 60:
                        unhealthy_signals += 1
                        self.logger.info(f"⚠️  {symbol}: RSI neutral ({latest['RSI']:.1f}) - no strength")

                    # 3. High volatility without direction
                    price_change = abs(data['close'].pct_change().tail(10).std())
                    if price_change > 0.05:  # 5% volatility
                        unhealthy_signals += 1
                        self.logger.info(f"⚠️  {symbol}: High volatility without direction")

                except Exception as e:
                    self.logger.warning(f"Error checking {symbol} market condition: {e}")

            # Market health assessment
            if total_checked == 0:
                self.logger.warning("Could not assess market conditions")
                return True  # Default to healthy if can't check

            unhealthy_ratio = unhealthy_signals / (total_checked * 3)  # 3 checks per symbol

            if unhealthy_ratio > 0.6:  # More than 60% unhealthy signals
                self.logger.warning(f"🚨 CONTEXT.TXT: Market conditions unhealthy ({unhealthy_ratio:.1%} signals)")
                self.logger.warning("   Activating global cooldown period...")
                self.global_cooldown = pd.Timestamp.now()
                return False
            else:
                self.logger.info(f"✅ CONTEXT.TXT: Market conditions healthy ({unhealthy_ratio:.1%} unhealthy signals)")
                return True

        except Exception as e:
            self.logger.error(f"Error checking market conditions: {e}")
            return True  # Default to healthy on error

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI for market condition analysis."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def open_position(self, symbol: str, side: str, price: float, reason: str, data: pd.DataFrame = None):
        """Open a new position with advanced trading algorithm."""
        try:
            # ADVANCED TRADING ALGORITHM - ENHANCED SIGNAL ANALYSIS
            if data is not None and len(data) >= 20:
                # 1. Market Regime Analysis
                regime_analysis = self.regime_detector.analyze_market_regime(data, symbol)
                self.logger.info(f"🌍 Market Regime for {symbol}: {self.regime_detector.get_regime_description(regime_analysis)} ({regime_analysis.confidence:.1f}% confidence)")

                # 2. Signal Quality Assessment
                signal_quality = self.signal_filter.assess_signal_quality(data, side, symbol, reason)
                self.logger.info(f"📊 Signal Quality for {symbol}: {signal_quality.overall_score:.1f}% (strength: {signal_quality.strength:.1f}%, confidence: {signal_quality.confidence:.1f}%)")

                # 3. Advanced Signal Filtering
                should_trade, filter_reason = self.signal_filter.should_trade_signal(signal_quality, symbol, side)
                if not should_trade:
                    self.logger.info(f"🚫 ADVANCED FILTER: Signal rejected for {symbol} - {filter_reason}")

                    # SCALPING FALLBACK - Try scalping when main signal is rejected
                    time_since_last = self.get_time_since_last_trade()
                    should_scalp = self.scalping_fallback.should_activate_scalping(
                        main_signal_rejected=True,
                        market_regime=regime_analysis.primary_regime.value if regime_analysis else 'unknown',
                        time_since_last_trade=time_since_last
                    )

                    if should_scalp and self.scalping_fallback.is_scalping_time_valid():
                        scalping_signal = self.scalping_fallback.generate_scalping_signal(data, symbol)
                        self.scalping_fallback.log_scalping_decision(scalping_signal, symbol)

                        if scalping_signal.signal != 'HOLD' and scalping_signal.confidence > 50:
                            self.logger.info(f"🎯 SCALPING ACTIVATED: Using {scalping_signal.signal} signal for {symbol}")
                            # Override main signal with scalping signal
                            side = scalping_signal.signal
                            reason = f"SCALPING: {scalping_signal.entry_reason}"
                            # Use smaller position size for scalping
                            scalping_mode = True
                        else:
                            self.logger.info(f"🎯 SCALPING: No viable scalping opportunity for {symbol}")
                            return
                    else:
                        return
                else:
                    self.logger.info(f"✅ ADVANCED FILTER: High-quality signal approved for {symbol}")
                    scalping_mode = False
            else:
                # Fallback for insufficient data
                regime_analysis = None
                signal_quality = None
                scalping_mode = False
                self.logger.warning(f"⚠️  Insufficient data for advanced analysis on {symbol}, using basic mode")
            # Check if we already have a position for this symbol
            if symbol in self.active_positions:
                self.logger.info(f"⚠️  Already have position for {symbol}, skipping new entry")
                return

            # STRICT LIMIT: Check maximum position limit (MAX 3 POSITIONS)
            if len(self.active_positions) >= self.max_positions and not self.force_entry_mode:
                self.logger.info(f"🚫 STRICT LIMIT: Maximum {self.max_positions} positions reached, skipping {symbol}")
                self.logger.info(f"   Active positions ({len(self.active_positions)}/{self.max_positions}): {list(self.active_positions.keys())}")
                self.logger.info(f"   💡 TIP: Invalid positions will be cleaned up automatically")
                self.logger.info(f"   💡 TIP: Set force_entry_mode=True to override temporarily")
                return
            elif self.force_entry_mode:
                self.logger.info(f"⚡ FORCE ENTRY MODE: Ignoring position limit for {symbol}")

            # CONTEXT.TXT COOLDOWN CHECK - Wait after each trade
            if symbol in self.last_trade_time:
                time_since_last = pd.Timestamp.now() - self.last_trade_time[symbol]
                cooldown_remaining = self.cooldown_minutes - (time_since_last.total_seconds() / 60)

                if cooldown_remaining > 0:
                    self.logger.info(f"⏰ CONTEXT.TXT cooldown active for {symbol}: {cooldown_remaining:.1f} minutes remaining")
                    return

            # CONTEXT.TXT GLOBAL COOLDOWN CHECK - Market condition based
            if self.global_cooldown:
                time_since_global = pd.Timestamp.now() - self.global_cooldown
                global_cooldown_remaining = 30 - (time_since_global.total_seconds() / 60)  # 30 min global cooldown

                if global_cooldown_remaining > 0:
                    self.logger.info(f"🌍 CONTEXT.TXT global cooldown active: {global_cooldown_remaining:.1f} minutes remaining")
                    return
            # Calculate market volatility if data is provided
            market_volatility = None
            if data is not None and len(data) > 20:
                market_volatility = self.risk_manager.calculate_market_volatility(data)
                self.logger.info(f"Market volatility for {symbol}: {market_volatility:.4f}")

            # ADVANCED POSITION SIZING with signal quality and market regime
            portfolio_value = self.portfolio.get_total_value({symbol: price})

            if signal_quality and regime_analysis and not scalping_mode:
                # Use advanced position sizing for main signals
                quantity, leverage = self.risk_manager.calculate_advanced_position_size(
                    portfolio_value=portfolio_value,
                    price=price,
                    signal_quality=signal_quality.overall_score,
                    market_regime_adjustment=regime_analysis.risk_adjustment,
                    volatility=regime_analysis.volatility_level,
                    symbol=symbol
                )
                self.logger.info(f"🚀 ADVANCED SIZING: Quality={signal_quality.overall_score:.1f}%, Regime={regime_analysis.risk_adjustment:.2f}x, Vol={regime_analysis.volatility_level:.3f}")
            elif scalping_mode:
                # Use smaller position sizing for scalping
                base_quantity, base_leverage = self.risk_manager.calculate_position_size(
                    portfolio_value, price, market_volatility=market_volatility, symbol=symbol
                )
                scalp_multiplier = self.scalping_fallback.get_scalping_position_size_multiplier()
                quantity = base_quantity * scalp_multiplier
                leverage = base_leverage
                self.logger.info(f"🎯 SCALPING SIZING: Base reduced by {scalp_multiplier:.1f}x for quick scalping")
            else:
                # Fallback to standard position sizing
                quantity, leverage = self.risk_manager.calculate_position_size(
                    portfolio_value, price, market_volatility=market_volatility, symbol=symbol
                )
                self.logger.info(f"📊 STANDARD SIZING: Using fallback position calculation")

            # VALIDATE $100 MAXIMUM TRADE VALUE
            trade_value = (quantity * price) / leverage  # Actual USD value without leverage
            if trade_value > self.max_trade_value:
                self.logger.error(f"🚫 Trade value ${trade_value:.2f} exceeds ${self.max_trade_value} limit for {symbol}")
                return

            # Log position details with value validation
            self.logger.info(f"Position calculation for {symbol}: quantity={quantity:.8f}, leverage={leverage}x")
            self.logger.info(f"💰 Trade value: ${trade_value:.2f} (limit: ${self.max_trade_value})")

            # Get symbol precision info for validation
            precision_info = self.client.get_symbol_precision(symbol)
            if precision_info:
                self.logger.info(f"Symbol {symbol} precision: qty={precision_info.get('quantity_precision', 'N/A')}, step_size={precision_info.get('step_size', 'N/A')}")
            else:
                self.logger.warning(f"Could not get precision info for {symbol} - order may fail")

            # TESTNET TRADING ONLY - Set leverage for futures trading
            self.client.futures_change_leverage(symbol=symbol, leverage=leverage)
            self.logger.info(f"Leverage set to {leverage}x for {symbol} on TESTNET")

            # Place futures market order on TESTNET
            order = self.client.futures_place_market_order(symbol, side, quantity)
            if not order:
                self.logger.error(f"Failed to place TESTNET futures order for {symbol}")
                return

            # Use actual fill price and quantity from TESTNET
            fill_price = float(order.get('avgPrice', price))
            fill_quantity = float(order.get('executedQty', quantity))

            self.logger.info(f"🚀 TESTNET TRADE EXECUTED: {side} {fill_quantity:.6f} {symbol} at {fill_price:.6f} (leverage: {leverage}x)")

            # Add to portfolio with leverage information
            position_info = {
                'leverage': leverage,
                'market_volatility': market_volatility
            }
            self.portfolio.add_position(symbol, side, fill_quantity, fill_price, position_info)

            # Track active position for scalping management
            self.active_positions[symbol] = {
                'side': side,
                'quantity': fill_quantity,
                'entry_price': fill_price,
                'leverage': leverage,
                'entry_time': pd.Timestamp.now(),
                'reason': reason
            }
            self.position_profits[symbol] = 0.0  # Initialize profit tracking

            # Record trade time for cooldown tracking (CONTEXT.TXT compliance)
            self.last_trade_time[symbol] = pd.Timestamp.now()
            self.last_successful_trade_time = pd.Timestamp.now()  # For scalping fallback

            # Disable force entry mode after successful trade
            if self.force_entry_mode:
                self.force_entry_mode = False
                self.logger.info(f"⚡ FORCE ENTRY MODE: Disabled after successful trade")

            # STRICT LIMITS STATUS
            self.logger.info(f"📊 STRICT LIMITS STATUS:")
            self.logger.info(f"   💰 Trade value: ${trade_value:.2f} / ${self.max_trade_value} max")
            self.logger.info(f"   🔢 Active positions: {len(self.active_positions)}/{self.max_positions} max")
            self.logger.info(f"   📋 Positions: {list(self.active_positions.keys())}")
            self.logger.info(f"⏰ Cooldown timer started for {symbol}: {self.cooldown_minutes} minutes")

            # Initialize profit manager tracking
            self.profit_manager.initialize_position_tracking(symbol, fill_price, side, fill_quantity)

            # Track position entry time
            self.position_entry_times[symbol] = pd.Timestamp.now()

            # Update strategy position tracking
            for strategy in self.strategies:
                strategy.add_position(symbol, side, fill_quantity, fill_price)

            # Log trade
            trade_data = {
                'timestamp': pd.Timestamp.now().isoformat(),
                'symbol': symbol,
                'side': side,
                'action': 'OPEN',
                'quantity': fill_quantity,
                'price': fill_price,
                'leverage': leverage,
                'strategy': reason,
                'reason': reason,
                'market_volatility': market_volatility or 0,
                'portfolio_value': self.portfolio.get_total_value({symbol: fill_price}),
                'position_size_pct': (fill_quantity * fill_price) / self.portfolio.get_total_value({symbol: fill_price}),
                'technical_indicators': self._get_technical_indicators(data) if data is not None else {},
                'market_conditions': self._get_market_conditions_summary()
            }
            self.trade_logger.log_trade(trade_data)

            self.logger.info(f"Futures position opened: {side} {fill_quantity:.6f} {symbol} at {fill_price} "
                           f"(leverage: {leverage}x, reason: {reason})")

        except Exception as e:
            self.logger.error(f"Error opening position for {symbol}: {e}")
    
    def close_position(self, symbol: str, price: float, reason: str):
        """Close an existing futures position."""
        try:
            position = self.portfolio.positions.get(symbol)
            if not position:
                return

            side = 'SELL' if position['side'] == 'BUY' else 'BUY'
            quantity = position['quantity']

            # TESTNET TRADING ONLY - Use futures API to close position
            order = self.client.futures_close_position(symbol)
            if not order:
                self.logger.error(f"Failed to close TESTNET futures position for {symbol}")
                return

            # Use actual fill price from TESTNET
            fill_price = float(order.get('avgPrice', price))

            # Get actual PnL from TESTNET futures API
            actual_pnl = self.client.futures_get_pnl(symbol)
            if actual_pnl != 0:
                self.logger.info(f"Actual TESTNET futures PnL from API: {actual_pnl:.4f}")

            self.logger.info(f"📉 TESTNET POSITION CLOSED: {side} {quantity:.6f} {symbol} at {fill_price:.6f}")

            # Calculate P&L (with leverage consideration)
            realized_pnl = self.portfolio.calculate_realized_pnl(position, fill_price)
            leverage = position.get('leverage', 1)
            if leverage > 1:
                realized_pnl *= leverage  # Apply leverage to P&L
                self.logger.info(f"P&L adjusted for {leverage}x leverage: {realized_pnl:.4f}")

            # Close position in portfolio
            self.portfolio.close_position(symbol, fill_price)

            # Remove from active positions tracking (scalping management)
            if symbol in self.active_positions:
                final_profit = self.position_profits.get(symbol, 0.0)
                self.logger.info(f"💰 Final profit for {symbol}: {final_profit:+.2f} USDT")
                del self.active_positions[symbol]
                if symbol in self.position_profits:
                    del self.position_profits[symbol]

                self.logger.info(f"📊 Active positions after close: {len(self.active_positions)}/{self.max_positions}")
                if self.active_positions:
                    self.logger.info(f"   Remaining positions: {list(self.active_positions.keys())}")

            # Update risk manager with enhanced tracking
            self.risk_manager.update_daily_pnl(realized_pnl)

            # Calculate trade duration
            duration_minutes = 0
            if symbol in self.position_entry_times:
                duration = pd.Timestamp.now() - self.position_entry_times[symbol]
                duration_minutes = duration.total_seconds() / 60
                del self.position_entry_times[symbol]

            # Remove from profit manager tracking
            self.profit_manager.remove_position_tracking(symbol)

            # Remove from strategy tracking
            for strategy in self.strategies:
                strategy.remove_position(symbol)

            # Log trade
            trade_data = {
                'timestamp': pd.Timestamp.now().isoformat(),
                'symbol': symbol,
                'side': 'SELL' if position['side'] == 'BUY' else 'BUY',  # Closing side
                'action': 'CLOSE_FULL',
                'quantity': quantity,
                'price': fill_price,
                'pnl': realized_pnl,
                'pnl_percentage': (realized_pnl / (position['entry_price'] * quantity)) * 100,
                'leverage': leverage,
                'entry_price': position['entry_price'],
                'exit_price': fill_price,
                'duration_minutes': duration_minutes,
                'strategy': 'Enhanced_Futures_Strategy',
                'reason': reason,
                'portfolio_value': self.portfolio.get_total_value({symbol: fill_price}),
                'position_size_pct': (quantity * fill_price) / self.portfolio.get_total_value({symbol: fill_price}),
                'market_conditions': self._get_market_conditions_summary()
            }
            self.trade_logger.log_trade(trade_data)

            self.logger.info(f"Futures position closed: {symbol} at {fill_price} "
                           f"P&L: {realized_pnl:.4f} (leverage: {leverage}x, reason: {reason})")

        except Exception as e:
            self.logger.error(f"Error closing futures position for {symbol}: {e}")

    def _get_technical_indicators(self, data: pd.DataFrame) -> Dict:
        """Extract technical indicators from data for logging."""
        if data is None or data.empty:
            return {}

        try:
            latest = data.iloc[-1]
            indicators = {
                'close': float(latest['close']),
                'volume': float(latest['volume']) if 'volume' in data.columns else 0,
                'high': float(latest['high']) if 'high' in data.columns else 0,
                'low': float(latest['low']) if 'low' in data.columns else 0
            }

            # Add calculated indicators if available
            if 'RSI' in data.columns:
                indicators['rsi'] = float(latest['RSI'])
            if 'EMA_fast' in data.columns:
                indicators['ema_fast'] = float(latest['EMA_fast'])
            if 'EMA_slow' in data.columns:
                indicators['ema_slow'] = float(latest['EMA_slow'])
            if 'MACD' in data.columns:
                indicators['macd'] = float(latest['MACD'])

            return indicators

        except Exception as e:
            self.logger.error(f"Error extracting technical indicators: {e}")
            return {}

    def _get_market_conditions_summary(self) -> Dict:
        """Get current market conditions summary for logging."""
        try:
            market_summary = self.market_monitor.get_market_summary()
            risk_metrics = self.risk_manager.get_risk_metrics()

            return {
                'market_status': market_summary.get('status', 'UNKNOWN'),
                'market_recommendation': market_summary.get('recommendation', 'UNKNOWN'),
                'volume_health': market_summary.get('volume_health', 'unknown'),
                'volatility_health': market_summary.get('volatility_health', 'unknown'),
                'trend_health': market_summary.get('trend_health', 'unknown'),
                'daily_pnl': risk_metrics.get('daily_pnl', 0),
                'daily_trades': risk_metrics.get('daily_trades', 0),
                'consecutive_losses': risk_metrics.get('consecutive_losses', 0),
                'market_pause_active': risk_metrics.get('market_pause_active', False)
            }

        except Exception as e:
            self.logger.error(f"Error getting market conditions summary: {e}")
            return {}

    def refresh_altcoin_list(self):
        """Refresh the altcoin list if dynamic scanning is enabled."""
        if self.use_dynamic_scanning and self.altcoin_scanner:
            try:
                if self.max_symbols is None:
                    self.logger.info("🔄 Refreshing ALL altcoin list...")
                else:
                    self.logger.info(f"🔄 Refreshing top {self.max_symbols} altcoin list...")

                new_symbols = self.altcoin_scanner.get_top_altcoins(self.max_symbols)

                if new_symbols and len(new_symbols) > 0:
                    # Compare with current symbols
                    added_symbols = set(new_symbols) - set(self.symbols)
                    removed_symbols = set(self.symbols) - set(new_symbols)

                    if added_symbols:
                        safe_added = [str(s) for s in added_symbols if isinstance(s, str)]
                        self.logger.info(f"Added {len(safe_added)} new altcoins: {', '.join(safe_added[:5])}...")

                    if removed_symbols:
                        safe_removed = [str(s) for s in removed_symbols if isinstance(s, str)]
                        self.logger.info(f"Removed {len(safe_removed)} altcoins: {', '.join(safe_removed[:5])}...")

                    self.symbols = new_symbols

                    # Update market monitor with new altcoin sample
                    altcoin_sample = [s for s in self.symbols if s not in {'BTCUSDT', 'ETHUSDT', 'BNBUSDT'}][:10]
                    self.market_monitor.altcoin_sample = altcoin_sample

                    self.logger.info(f"Updated symbol list: {len(self.symbols)} altcoins")
                else:
                    self.logger.warning("Failed to refresh altcoin list, keeping current symbols")

            except Exception as e:
                self.logger.error(f"Error refreshing altcoin list: {e}")

    def run_cycle(self):
        """Run one chart-based single position trading cycle."""
        self.logger.info(f"📊 Running chart-based trading cycle at {get_timestamp()}")

        # STEP 1: Check and manage existing position
        if self.position_manager.has_active_position():
            self.manage_active_position()
            return  # Skip new position search when managing existing position

        # STEP 2: Search for new chart-based trading opportunities
        self.search_chart_based_opportunities()

    def manage_active_position(self):
        """Manage the current active position."""
        try:
            position = self.position_manager.get_active_position()
            if not position:
                return

            # Get current price
            current_price = self.client.get_current_price(position.symbol)
            if not current_price:
                self.logger.error(f"Could not get current price for {position.symbol}")
                return

            # Check if position should be closed
            should_close, close_reason = self.position_manager.should_close_position(current_price)

            # Get position status for logging
            status = self.position_manager.get_position_status(current_price)

            self.logger.info(f"📊 ACTIVE POSITION STATUS:")
            self.logger.info(f"   Symbol: {status['symbol']}")
            self.logger.info(f"   Side: {status['side']}")
            self.logger.info(f"   Entry: ${status['entry_price']:.6f}")
            self.logger.info(f"   Current: ${current_price:.6f}")
            self.logger.info(f"   Hold Time: {status['hold_minutes']:.1f} minutes")
            self.logger.info(f"   P&L: ${status['current_pnl']:.2f} ({status['pnl_percentage']:.2f}%)")
            self.logger.info(f"   Target: ${status['target_price']:.6f}")
            self.logger.info(f"   Stop Loss: ${status['stop_loss']:.6f}")

            if should_close:
                self.logger.info(f"🔄 CLOSING POSITION: {close_reason}")

                # Close position on exchange
                try:
                    # Determine close side (opposite of entry)
                    close_side = 'SELL' if position.side == 'BUY' else 'BUY'

                    # Place market order to close
                    order = self.client.futures_place_market_order(
                        position.symbol, close_side, position.quantity
                    )

                    if order:
                        # Calculate realized P&L
                        realized_pnl = status['current_pnl']

                        # Update portfolio
                        self.portfolio.update_balance(realized_pnl)

                        # Close position in manager
                        self.position_manager.close_position(current_price, close_reason, realized_pnl)

                        self.logger.info(f"✅ POSITION CLOSED SUCCESSFULLY")
                        self.logger.info(f"   Realized P&L: ${realized_pnl:.2f}")
                        self.logger.info(f"   New Balance: ${self.portfolio.get_total_value():.2f}")

                        # Log trading statistics
                        stats = self.position_manager.get_trading_statistics()
                        self.logger.info(f"📊 TRADING STATS: {stats['total_trades']} trades, {stats['win_rate']:.1f}% win rate, ${stats['total_pnl']:.2f} total P&L")

                    else:
                        self.logger.error(f"Failed to close position for {position.symbol}")

                except Exception as e:
                    self.logger.error(f"Error closing position: {e}")
            else:
                self.logger.info(f"📊 HOLDING POSITION: {close_reason}")

        except Exception as e:
            self.logger.error(f"Error managing active position: {e}")

    def search_chart_based_opportunities(self):
        """Search for new chart-based trading opportunities."""
        try:
            # Check if we can open new position
            can_open, reason = self.position_manager.can_open_new_position()
            if not can_open:
                self.logger.info(f"🚫 Cannot open new position: {reason}")
                return

            self.logger.info(f"🔍 Searching for chart-based opportunities across {len(self.symbols)} altcoins...")

            # Sync portfolio balance with actual testnet balance (every cycle)
            try:
                account_info = self.client.futures_get_account_info()
                if account_info:
                    assets = account_info.get('assets', [])
                    usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)

                    if usdt_asset:
                        actual_balance = float(usdt_asset.get('availableBalance', 0))
                        self.portfolio.sync_balance(actual_balance)
            except Exception as e:
                self.logger.warning(f"Could not sync balance: {e}")

            # CONTEXT.TXT MARKET CONDITION CHECK - Before any trading decisions
            market_healthy = self.check_market_conditions()
            if not market_healthy:
                self.logger.info("🚨 CONTEXT.TXT: Skipping trading cycle due to unhealthy market conditions")
                return  # Skip entire trading cycle

            # Analyze altcoins for chart-based opportunities
            best_opportunity = None
            best_confidence = 0.0

            for symbol in self.symbols:
                try:
                    # Get multi-timeframe data for chart analysis
                    chart_data = self.multi_timeframe.get_chart_analysis_data(symbol)

                    # Check if we have valid data for any timeframe
                    valid_data = False
                    for tf, data in chart_data.items():
                        if data is not None and not data.empty and len(data) >= 20:
                            valid_data = True
                            break

                    if not valid_data:
                        self.logger.debug(f"No valid chart data available for {symbol}")
                        continue

                    # Perform comprehensive chart analysis
                    analysis = self.chart_analyzer.comprehensive_chart_analysis(
                        data_5m=chart_data.get('5m'),
                        data_15m=chart_data.get('15m'),
                        data_1h=chart_data.get('1h'),
                        symbol=symbol
                    )

                    # Log analysis results
                    self.logger.info(f"📊 CHART ANALYSIS for {symbol}:")
                    self.logger.info(f"   Signal: {analysis.signal}")
                    self.logger.info(f"   Confidence: {analysis.confidence:.1f}%")
                    self.logger.info(f"   Pattern: {analysis.pattern.value}")
                    self.logger.info(f"   Entry Reason: {analysis.entry_reason}")
                    self.logger.info(f"   Risk/Reward: {analysis.risk_reward_ratio:.2f}")
                    self.logger.info(f"   Volume Confirmed: {analysis.volume_confirmation}")
                    self.logger.info(f"   Timeframe Alignment: {analysis.timeframe_alignment}")

                    # Check if this is a valid trading signal
                    if (analysis.signal in ['BUY', 'SELL'] and
                        analysis.confidence >= self.chart_analyzer.min_confidence_threshold and
                        analysis.risk_reward_ratio >= self.chart_analyzer.min_risk_reward_ratio):

                        if analysis.confidence > best_confidence:
                            best_opportunity = (symbol, analysis)
                            best_confidence = analysis.confidence
                            self.logger.info(f"🎯 NEW BEST OPPORTUNITY: {symbol} ({analysis.confidence:.1f}%)")
                    else:
                        reasons = []
                        if analysis.signal == 'HOLD':
                            reasons.append("No clear signal")
                        if analysis.confidence < self.chart_analyzer.min_confidence_threshold:
                            reasons.append(f"Low confidence: {analysis.confidence:.1f}% < {self.chart_analyzer.min_confidence_threshold}%")
                        if analysis.risk_reward_ratio < self.chart_analyzer.min_risk_reward_ratio:
                            reasons.append(f"Poor risk/reward: {analysis.risk_reward_ratio:.2f} < {self.chart_analyzer.min_risk_reward_ratio}")

                        self.logger.info(f"🚫 REJECTED {symbol}: {', '.join(reasons)}")

                except Exception as e:
                    self.logger.error(f"Error analyzing {symbol}: {e}")
                    continue

            # Execute best opportunity if found
            if best_opportunity:
                symbol, analysis = best_opportunity
                self.execute_chart_based_trade(symbol, analysis)
            else:
                self.logger.info("🔍 No high-quality chart-based opportunities found this cycle")

                # Only try scalping fallback if no position and no good chart signals
                self.try_scalping_fallback()

        except Exception as e:
            self.logger.error(f"Error in chart-based opportunity search: {e}")

    def execute_chart_based_trade(self, symbol: str, analysis):
        """Execute a chart-based trade."""
        try:
            self.logger.info(f"🚀 EXECUTING CHART-BASED TRADE for {symbol}")

            # Get current price
            current_price = self.client.get_current_price(symbol)
            if not current_price:
                self.logger.error(f"Could not get current price for {symbol}")
                return

            # Calculate position size based on chart analysis confidence
            portfolio_value = self.portfolio.get_total_value({symbol: current_price})

            # Use chart analysis confidence for position sizing
            quantity, leverage = self.risk_manager.calculate_advanced_position_size(
                portfolio_value=portfolio_value,
                price=current_price,
                signal_quality=analysis.confidence,
                market_regime_adjustment=1.0,  # Neutral for chart-based
                volatility=0.3,  # Moderate volatility assumption
                symbol=symbol
            )

            # Validate trade value
            trade_value = (quantity * current_price) / leverage
            if trade_value > self.max_trade_value:
                self.logger.error(f"🚫 Trade value ${trade_value:.2f} exceeds ${self.max_trade_value} limit")
                return

            self.logger.info(f"📊 CHART-BASED POSITION SIZING:")
            self.logger.info(f"   Confidence: {analysis.confidence:.1f}%")
            self.logger.info(f"   Quantity: {quantity:.6f}")
            self.logger.info(f"   Leverage: {leverage}x")
            self.logger.info(f"   Trade Value: ${trade_value:.2f}")

            # Set leverage and place order
            try:
                self.client.futures_change_leverage(symbol=symbol, leverage=leverage)
                order = self.client.futures_place_market_order(symbol, analysis.signal, quantity)

                if order:
                    # Record position in manager
                    success = self.position_manager.open_position(
                        symbol=symbol,
                        side=analysis.signal,
                        entry_price=current_price,
                        quantity=quantity,
                        leverage=leverage,
                        entry_reason=analysis.entry_reason,
                        target_price=analysis.target_price,
                        stop_loss=analysis.stop_loss,
                        support_level=analysis.support_level,
                        resistance_level=analysis.resistance_level,
                        chart_pattern=analysis.pattern.value,
                        risk_reward_ratio=analysis.risk_reward_ratio
                    )

                    if success:
                        self.logger.info(f"✅ CHART-BASED TRADE EXECUTED SUCCESSFULLY")

                        # Update portfolio (reserve the trade value)
                        self.portfolio.update_balance(-trade_value)

                        # Log trade details
                        self.logger.info(f"📊 TRADE DETAILS:")
                        self.logger.info(f"   Order ID: {order.get('orderId', 'N/A')}")
                        self.logger.info(f"   Pattern: {analysis.pattern.value}")
                        self.logger.info(f"   Support: ${analysis.support_level:.6f}")
                        self.logger.info(f"   Resistance: ${analysis.resistance_level:.6f}")
                        self.logger.info(f"   Target: ${analysis.target_price:.6f}")
                        self.logger.info(f"   Stop Loss: ${analysis.stop_loss:.6f}")
                        self.logger.info(f"   Risk/Reward: {analysis.risk_reward_ratio:.2f}")
                    else:
                        self.logger.error(f"Failed to record position in manager")
                else:
                    self.logger.error(f"Failed to place order for {symbol}")

            except Exception as e:
                self.logger.error(f"Error placing chart-based order: {e}")

        except Exception as e:
            self.logger.error(f"Error executing chart-based trade: {e}")

    def try_scalping_fallback(self):
        """Try scalping fallback only when no position is active."""
        try:
            # Only use scalping when no active position
            if self.position_manager.has_active_position():
                return

            # Check if scalping should be activated
            time_since_last = self.get_time_since_last_trade()
            should_scalp = self.scalping_fallback.should_activate_scalping(
                main_signal_rejected=True,  # Chart signals were rejected
                market_regime='sideways_ranging',  # Assume ranging for scalping
                time_since_last_trade=time_since_last
            )

            if should_scalp and self.scalping_fallback.is_scalping_time_valid():
                self.logger.info("🎯 SCALPING FALLBACK: No chart opportunities, scalping disabled in single position mode")

        except Exception as e:
            self.logger.error(f"Error in scalping fallback: {e}")

        # Clean up invalid positions and sync with exchange
        self.cleanup_invalid_positions()
        self.sync_positions_with_exchange()

        # Check profit targets for active positions (immediate profit-taking)
        if self.active_positions:
            self.logger.info(f"💰 Checking profit targets for {len(self.active_positions)} active positions...")
            self.check_profit_targets()
        else:
            self.logger.info("🔍 No active positions - ready to find new trading opportunities")

        # Refresh altcoin list periodically (every 24 hours)
        if hasattr(self, '_last_altcoin_refresh'):
            time_since_refresh = pd.Timestamp.now() - self._last_altcoin_refresh
            if time_since_refresh.total_seconds() > (24 * 3600):  # 24 hours
                self.refresh_altcoin_list()
                self._last_altcoin_refresh = pd.Timestamp.now()
        else:
            self._last_altcoin_refresh = pd.Timestamp.now()

        for symbol in self.symbols:
            try:
                # Get market data
                data = self.get_market_data(symbol)
                if data.empty:
                    continue

                # Process signals
                self.process_signals(symbol, data)

            except Exception as e:
                if "Invalid symbol" in str(e):
                    self.logger.warning(f"Invalid symbol {symbol} - removing from trading list")
                    # Remove invalid symbol from the list
                    if symbol in self.symbols:
                        self.symbols.remove(symbol)
                else:
                    self.logger.error(f"Error processing {symbol}: {e}")

        # Log portfolio status
        self.log_portfolio_status()
        self.last_update = pd.Timestamp.now()
    
    def log_portfolio_status(self):
        """Log enhanced portfolio status with all metrics."""
        metrics = self.portfolio.get_performance_metrics()
        positions = self.portfolio.get_positions_summary()
        risk_metrics = self.risk_manager.get_risk_metrics()
        performance_summary = self.trade_logger.get_performance_summary()
        market_summary = self.market_monitor.get_market_summary()

        self.logger.info(f"=== ENHANCED PORTFOLIO STATUS ===")

        # Portfolio metrics
        balance = metrics.get('current_balance', self.portfolio.balances.get(self.base_currency, 0))
        self.logger.info(f"💰 Balance: {balance:.2f} {self.base_currency}")
        self.logger.info(f"📈 Total P&L: {metrics['total_pnl']:.4f}")
        self.logger.info(f"📊 Total Return: {metrics['total_return']:.2f}%")

        # Trading performance
        self.logger.info(f"🔄 Total Trades: {performance_summary['total_trades']}")
        self.logger.info(f"🎯 Win Rate: {performance_summary['win_rate']:.2%}")
        self.logger.info(f"🏆 Best Trade: {performance_summary['best_trade']:.4f}")
        self.logger.info(f"📉 Worst Trade: {performance_summary['worst_trade']:.4f}")
        self.logger.info(f"🔥 Current Streak: {performance_summary['current_streak']}")

        # Risk metrics
        self.logger.info(f"⚠️ Daily P&L: {risk_metrics['daily_pnl']:.4f}")
        self.logger.info(f"📊 Daily Trades: {risk_metrics['daily_trades']}")
        self.logger.info(f"❌ Consecutive Losses: {risk_metrics['consecutive_losses']}")
        self.logger.info(f"⏰ Cooldown Remaining: {risk_metrics['cooldown_remaining']}s")

        # Market conditions
        self.logger.info(f"🌍 Market Status: {market_summary.get('status', 'UNKNOWN')}")
        self.logger.info(f"💡 Recommendation: {market_summary.get('recommendation', 'UNKNOWN')}")

        # Open positions
        self.logger.info(f"📋 Open Positions: {len(positions)}")
        for pos in positions:
            profit_summary = self.profit_manager.get_position_summary(pos['symbol'])
            trailing_info = ""
            if profit_summary and profit_summary.get('trailing_stop_active'):
                trailing_info = f" (Trailing: {profit_summary.get('trailing_stop_price', 0):.6f})"

            self.logger.info(f"    {pos['symbol']}: {pos['side']} {pos['quantity']:.6f} "
                           f"@ {pos['entry_price']:.4f} (P&L: {pos['unrealized_pnl']:.4f}){trailing_info}")

        self.logger.info(f"=== END STATUS ===")

        # Log performance snapshot to database
        if hasattr(self, 'trade_logger'):
            self.trade_logger._save_performance_metrics()
    
    def start(self, interval: int = 300):  # 5 minutes default
        """
        Start the trading bot.
        
        Args:
            interval: Update interval in seconds
        """
        self.logger.info(f"Starting trading bot with {interval}s interval")
        self.running = True
        
        try:
            while self.running:
                self.run_cycle()
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.logger.info("Trading bot stopped by user")
        except Exception as e:
            self.logger.error(f"Trading bot error: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the trading bot."""
        self.logger.info("Stopping trading bot...")
        self.running = False
        
        # Save portfolio state
        self.portfolio.save_to_file('portfolio_state.json')
        
        # Log final status
        self.log_portfolio_status()
        self.logger.info("Trading bot stopped")

def main():
    """Main entry point."""
    try:
        # Trading mode confirmation
        config = load_config('config/config.yaml')
        trading_mode = config.get('trading', {}).get('mode', 'unknown')

        if trading_mode == 'live':
            print("\n" + "=" * 60)
            print("⚠️  WARNING: LIVE TRADING MODE ENABLED ⚠️")
            print("This bot will place REAL trades with REAL money!")
            print("=" * 60)

            confirmation = input("\nType 'CONFIRM' to start live trading or anything else to exit: ")
            if confirmation != 'CONFIRM':
                print("Live trading cancelled by user.")
                sys.exit(0)

            print("\n🚀 Starting bot in LIVE trading mode...\n")

        elif trading_mode == 'testnet':
            print("\n" + "=" * 60)
            print("🧪 TESTNET MODE ENABLED 🧪")
            print("Using Binance Futures Testnet for demo trading")
            print("No real money at risk - perfect for testing!")
            print("Testnet URL: https://testnet.binancefuture.com")
            print("=" * 60)

            # Check testnet status
            try:
                from binance_client import BinanceClient
                client = BinanceClient(testnet=True)

                # Fix timestamp sync first to prevent errors
                print("🔄 Syncing with Binance server time...")
                client.fix_timestamp_sync()

                # Get account info with retry logic
                account = client.futures_get_account_info()

                if account:
                    # Check balance
                    assets = account.get('assets', [])
                    usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)

                    if usdt_asset:
                        balance = float(usdt_asset.get('availableBalance', 0))
                        print(f"\n💰 Testnet Balance: {balance:.2f} USDT")

                        if balance < 10:
                            print("⚠️  WARNING: Low testnet balance")
                            print("   You should get more testnet funds before trading")
                            print("   Run python testnet_setup.py and select option 3")
                    else:
                        print("\n⚠️  No USDT found in testnet account")
                        print("   You need to get testnet funds before trading")
                        print("   Run python testnet_setup.py and select option 3")
                else:
                    print("\n⚠️  Could not connect to testnet account")
                    print("   Check your API keys and try again")
                    print("   Run python fix_timestamp_permanently.py to fix timestamp issues")
            except Exception as e:
                print(f"\n⚠️  Error checking testnet status: {e}")
                print("   If you see timestamp errors, run python fix_timestamp_permanently.py")

            confirmation = input("\nPress Enter to start testnet trading or type 'exit' to cancel: ")
            if confirmation.lower() == 'exit':
                print("Testnet trading cancelled by user.")
                sys.exit(0)

            print("\n🚀 Starting bot in TESTNET mode...\n")

        # Initialize and start bot
        bot = TradingBot()
        bot.start(interval=300)  # Run every 5 minutes
    except KeyboardInterrupt:
        logging.info("Trading bot stopped by user")
        print("\nTrading bot stopped by user.")
    except Exception as e:
        logging.error(f"Failed to start trading bot: {e}")
        print(f"\nError: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
