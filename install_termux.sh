#!/bin/bash
# Termux Trading Bot Installation Script

echo "📱 TERMUX TRADING BOT INSTALLER"
echo "================================"

# Update packages
echo "🔄 Updating Termux packages..."
pkg update -y && pkg upgrade -y

# Install essential packages
echo "📦 Installing essential packages..."
pkg install -y python python-pip git curl wget
pkg install -y build-essential clang cmake
pkg install -y libffi openssl zlib

# Install Python packages
echo "🐍 Installing Python packages..."
pip install --upgrade pip

# Core packages
pip install numpy pandas requests pyyaml python-dateutil pytz

# Trading packages
pip install python-binance websocket-client

# Technical analysis
echo "📊 Installing technical analysis packages..."
pip install ta-lib || echo "⚠️  ta-lib installation failed, continuing..."

# Create directories
echo "📁 Creating directories..."
mkdir -p ~/trading-bot/logs
mkdir -p ~/trading-bot/data
mkdir -p ~/trading-bot/config

# Set permissions
echo "🔐 Setting permissions..."
chmod +x run_termux.py

# Create startup alias
echo "🔗 Creating startup alias..."
echo 'alias trading-bot="cd ~/trading-bot && python run_termux.py"' >> ~/.bashrc

echo ""
echo "✅ INSTALLATION COMPLETE!"
echo ""
echo "📱 NEXT STEPS:"
echo "1. Edit .env file with your API keys"
echo "2. Run: python run_termux.py"
echo "3. Or use alias: trading-bot"
echo ""
echo "💡 TIPS FOR TERMUX:"
echo "• Keep Termux app open to prevent Android from killing it"
echo "• Use 'Acquire Wakelock' in Termux settings"
echo "• Consider using Termux:Boot for auto-start"
echo ""
