# 🚀 Live Trading Setup Guide

This guide will help you configure the enhanced futures trading bot for live trading on Binance.

## ⚠️ IMPORTANT WARNINGS

**READ THIS CAREFULLY BEFORE PROCEEDING:**

1. **RISK WARNING**: Live trading involves real money and can result in significant losses
2. **START SMALL**: Begin with a small amount you can afford to lose
3. **TEST FIRST**: Always test thoroughly in paper mode before going live
4. **MONITOR CLOSELY**: Never leave the bot running unattended initially
5. **UNDERSTAND THE STRATEGY**: Make sure you understand how the bot works

## 📋 Prerequisites

### 1. Binance Account Setup
- ✅ Verified Binance account
- ✅ Futures trading enabled
- ✅ API access enabled
- ✅ Sufficient USDT balance in Futures wallet

### 2. API Key Configuration
Create API keys with the following permissions:
- ✅ **Futures Trading** (required for placing orders)
- ✅ **Read Info** (required for account data)
- ❌ **Withdraw** (NOT recommended for security)

### 3. Security Settings
- ✅ Enable API key IP restrictions
- ✅ Use strong API secret
- ✅ Enable 2FA on your Binance account

## 🔧 Configuration Steps

### Step 1: Environment Variables
Create a `.env` file in the project root:

```bash
# Binance API Configuration
BINANCE_API_KEY=your_actual_api_key_here
BINANCE_SECRET_KEY=your_actual_secret_key_here

# Initial Balance (for tracking purposes)
INITIAL_BALANCE=1000

# Optional: Telegram Notifications
TELEGRAM_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
```

### Step 2: Verify Configuration
The bot is now configured for live trading in `config/config.yaml`:

```yaml
trading:
  mode: "live"  # live trading mode enabled
```

### Step 3: Risk Management Settings
Review and adjust risk settings in `config/config.yaml`:

```yaml
risk_management:
  max_position_size: 0.05  # 5% max position size (ADJUST AS NEEDED)
  min_position_size: 0.02  # 2% min position size
  stop_loss_percentage: 0.015  # 1.5% stop loss
  max_daily_loss: 0.05  # 5% daily loss limit (ADJUST AS NEEDED)
  max_leverage: 5  # Max 5x leverage (REDUCE IF NEEDED)
  max_daily_trades: 20  # Maximum trades per day
```

**RECOMMENDED ADJUSTMENTS FOR BEGINNERS:**
- Reduce `max_position_size` to 0.02 (2%)
- Reduce `max_leverage` to 2 or 3
- Reduce `max_daily_loss` to 0.02 (2%)

## 🧪 Pre-Live Testing

### 1. API Connection Test
```bash
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
from src.binance_client import BinanceClient

client = BinanceClient()
account = client.futures_get_account_info()
if account:
    print('✅ API connection successful')
    print(f'Account balance: {account.get(\"totalWalletBalance\", \"N/A\")} USDT')
else:
    print('❌ API connection failed')
"
```

### 2. Test Market Data
```bash
python -c "
from src.binance_client import BinanceClient
client = BinanceClient()
data = client.get_historical_klines('ADAUSDT', '5m', 10)
print(f'✅ Market data test: {len(data)} candles received')
"
```

### 3. Test Leverage Setting
```bash
python -c "
from src.binance_client import BinanceClient
client = BinanceClient()
result = client.futures_change_leverage('ADAUSDT', 2)
if result:
    print('✅ Leverage setting test successful')
else:
    print('❌ Leverage setting test failed')
"
```

## 🚀 Starting Live Trading

### 1. Final Checklist
- [ ] API keys configured and tested
- [ ] Risk settings reviewed and adjusted
- [ ] Sufficient balance in Futures wallet
- [ ] Telegram alerts configured (optional)
- [ ] Monitoring setup ready

### 2. Start the Bot
```bash
# Start with verbose logging
python main.py

# Or run in background (Linux/Mac)
nohup python main.py > trading.log 2>&1 &
```

### 3. Monitor Initial Performance
- Watch the first few trades closely
- Check log files regularly: `tail -f logs/trading_bot.log`
- Monitor Telegram alerts if configured
- Review trade database: `logs/trades.csv`

## 📊 Monitoring and Management

### Real-Time Monitoring
```bash
# View recent logs
tail -f logs/trading_bot.log

# Check trade history
python -c "
import pandas as pd
trades = pd.read_csv('logs/trades.csv')
print(trades.tail(10))
"

# View performance metrics
python -c "
import json
with open('logs/performance.json', 'r') as f:
    perf = json.load(f)
    print(f'Total Trades: {perf[\"total_trades\"]}')
    print(f'Win Rate: {perf[\"win_rate\"]*100:.1f}%')
    print(f'Total P&L: {perf[\"total_pnl\"]}')
"
```

### Emergency Stop
If you need to stop the bot immediately:

1. **Stop the process**: `Ctrl+C` or kill the process
2. **Close all positions manually** via Binance interface if needed
3. **Review logs** to understand what happened

## 🔧 Troubleshooting

### Common Issues

**API Permission Errors:**
- Verify API key has Futures trading permissions
- Check IP restrictions
- Ensure API key is not expired

**Insufficient Balance:**
- Check Futures wallet balance
- Transfer USDT from Spot to Futures wallet

**Position Size Errors:**
- Reduce position sizes in config
- Check minimum order sizes for symbols

**Leverage Errors:**
- Some symbols may have different max leverage
- Bot will automatically adjust if needed

### Log Analysis
```bash
# Check for errors
grep -i error logs/trading_bot.log

# Check trade executions
grep -i "position opened\|position closed" logs/trading_bot.log

# Check risk management triggers
grep -i "risk\|stop\|pause" logs/trading_bot.log
```

## 📈 Performance Optimization

### After Initial Testing
1. **Analyze Results**: Review first week of trading
2. **Adjust Parameters**: Fine-tune based on performance
3. **Scale Gradually**: Increase position sizes slowly
4. **Monitor Market Conditions**: Adjust for different market phases

### Key Metrics to Watch
- **Win Rate**: Should be >50% for profitable trading
- **Profit Factor**: Should be >1.2 for good performance
- **Max Drawdown**: Keep under acceptable risk levels
- **Trade Frequency**: Ensure not overtrading

## 🛡️ Risk Management

### Daily Routine
1. Check overnight performance
2. Review any stopped positions
3. Monitor market conditions
4. Adjust settings if needed

### Weekly Review
1. Analyze trade performance
2. Review risk metrics
3. Adjust strategy parameters
4. Update stop-loss levels if needed

## 📞 Support

If you encounter issues:
1. Check logs first: `logs/trading_bot.log`
2. Review this setup guide
3. Test API connections
4. Check Binance status page
5. Review trade database for patterns

---

**Remember: Trading involves risk. Never invest more than you can afford to lose. Start small and scale gradually as you gain confidence in the system.**
