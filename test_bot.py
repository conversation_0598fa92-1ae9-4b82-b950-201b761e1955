#!/usr/bin/env python3
"""
Comprehensive test script for the crypto trading bot.
Run this script to verify all components are working correctly.
"""

import os
import sys
import traceback
from datetime import datetime, timedel<PERSON>

def print_header(title):
    """Print a formatted header."""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_test_result(test_name, success, message=""):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} - {test_name}")
    if message:
        print(f"    {message}")

def test_python_environment():
    """Test Python environment and basic imports."""
    print_header("Testing Python Environment")
    
    # Test Python version
    try:
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            print_test_result("Python Version", True, f"Python {version.major}.{version.minor}.{version.micro}")
        else:
            print_test_result("Python Version", False, f"Python {version.major}.{version.minor} - Need 3.8+")
            return False
    except Exception as e:
        print_test_result("Python Version", False, str(e))
        return False
    
    # Test basic imports
    required_modules = ['pandas', 'numpy', 'yaml', 'requests']
    all_imports_ok = True
    
    for module in required_modules:
        try:
            __import__(module)
            print_test_result(f"Import {module}", True)
        except ImportError:
            print_test_result(f"Import {module}", False, "Module not installed")
            all_imports_ok = False
    
    return all_imports_ok

def test_project_structure():
    """Test project file structure."""
    print_header("Testing Project Structure")
    
    required_files = [
        'main.py',
        'config/config.yaml',
        '.env.example',
        'requirements.txt',
        'src/binance_client.py',
        'src/strategies/sma_crossover.py',
        'src/portfolio.py',
        'src/risk_management.py'
    ]
    
    all_files_exist = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print_test_result(f"File {file_path}", True)
        else:
            print_test_result(f"File {file_path}", False, "File missing")
            all_files_exist = False
    
    return all_files_exist

def test_configuration():
    """Test configuration loading."""
    print_header("Testing Configuration")
    
    try:
        sys.path.append('src')
        from utils import load_config
        
        config = load_config('config/config.yaml')
        if config:
            print_test_result("Load config.yaml", True)
            
            # Check required sections
            required_sections = ['trading', 'strategies', 'risk_management']
            for section in required_sections:
                if section in config:
                    print_test_result(f"Config section '{section}'", True)
                else:
                    print_test_result(f"Config section '{section}'", False, "Section missing")
                    return False
            
            return True
        else:
            print_test_result("Load config.yaml", False, "Config is empty")
            return False
            
    except Exception as e:
        print_test_result("Configuration Test", False, str(e))
        return False

def test_strategies():
    """Test trading strategies with sample data."""
    print_header("Testing Trading Strategies")
    
    try:
        sys.path.append('src')
        import pandas as pd
        import numpy as np
        from strategies.sma_crossover import SMACrossoverStrategy
        from strategies.rsi_strategy import RSIStrategy
        
        # Create sample data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='1h')  # Fixed: 'H' -> 'h'
        prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
        
        sample_data = pd.DataFrame({
            'open': prices,
            'high': prices * 1.01,
            'low': prices * 0.99,
            'close': prices,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        # Test SMA Strategy
        try:
            sma_config = {'short_window': 10, 'long_window': 30}
            sma_strategy = SMACrossoverStrategy(sma_config)
            signal = sma_strategy.generate_signal(sample_data, 'BTCUSDT')
            
            if signal in ['BUY', 'SELL', 'HOLD']:
                print_test_result("SMA Strategy", True, f"Generated signal: {signal}")
            else:
                print_test_result("SMA Strategy", False, f"Invalid signal: {signal}")
                
        except Exception as e:
            print_test_result("SMA Strategy", False, str(e))
        
        # Test RSI Strategy
        try:
            rsi_config = {'rsi_period': 14, 'oversold_threshold': 30, 'overbought_threshold': 70}
            rsi_strategy = RSIStrategy(rsi_config)
            signal = rsi_strategy.generate_signal(sample_data, 'BTCUSDT')
            
            if signal in ['BUY', 'SELL', 'HOLD']:
                print_test_result("RSI Strategy", True, f"Generated signal: {signal}")
            else:
                print_test_result("RSI Strategy", False, f"Invalid signal: {signal}")
                
        except Exception as e:
            print_test_result("RSI Strategy", False, str(e))
        
        return True
        
    except Exception as e:
        print_test_result("Strategy Test", False, str(e))
        return False

def test_portfolio_and_risk():
    """Test portfolio and risk management."""
    print_header("Testing Portfolio & Risk Management")
    
    try:
        sys.path.append('src')
        from portfolio import Portfolio
        from risk_management import RiskManager
        
        # Test Portfolio
        portfolio = Portfolio(10000, 'USDT')
        portfolio.add_position('BTCUSDT', 'BUY', 0.1, 50000)
        
        if len(portfolio.positions) == 1:
            print_test_result("Portfolio - Add Position", True)
        else:
            print_test_result("Portfolio - Add Position", False)
        
        # Test closing position
        portfolio.close_position('BTCUSDT', 51000)
        
        if len(portfolio.positions) == 0:
            print_test_result("Portfolio - Close Position", True)
        else:
            print_test_result("Portfolio - Close Position", False)
        
        # Test Risk Management
        risk_config = {
            'max_position_size': 0.1,
            'stop_loss_percentage': 0.02,
            'take_profit_percentage': 0.04
        }
        risk_manager = RiskManager(risk_config)
        
        position_result = risk_manager.calculate_position_size(10000, 50000)
        if isinstance(position_result, tuple) and len(position_result) == 2:
            quantity, leverage = position_result
            if quantity > 0:
                print_test_result("Risk Management - Position Size", True, f"Quantity: {quantity:.6f}, Leverage: {leverage}x")
            else:
                print_test_result("Risk Management - Position Size", False, "Zero quantity calculated")
        else:
            print_test_result("Risk Management - Position Size", False, f"Unexpected return type: {type(position_result)}")
        
        return True
        
    except Exception as e:
        print_test_result("Portfolio & Risk Test", False, str(e))
        return False

def test_binance_client():
    """Test Binance client (read-only mode)."""
    print_header("Testing Binance Client")
    
    try:
        sys.path.append('src')
        from binance_client import BinanceClient
        
        # Test client initialization (without API keys)
        client = BinanceClient()
        print_test_result("Binance Client Init", True)
        
        # Test getting current price (public endpoint)
        try:
            price = client.get_current_price('BTCUSDT')
            if price > 0:
                print_test_result("Get Current Price", True, f"BTC: ${price:,.2f}")
            else:
                print_test_result("Get Current Price", False, "Price is 0")
        except Exception as e:
            print_test_result("Get Current Price", False, "API connection failed - this is OK for offline testing")
        
        # Test getting historical data
        try:
            data = client.get_historical_klines('BTCUSDT', '1h', 10)
            if not data.empty:
                print_test_result("Get Historical Data", True, f"Got {len(data)} data points")
            else:
                print_test_result("Get Historical Data", False, "No data returned")
        except Exception as e:
            print_test_result("Get Historical Data", False, "API connection failed - this is OK for offline testing")
        
        return True
        
    except Exception as e:
        print_test_result("Binance Client Test", False, str(e))
        return False

def test_environment_file():
    """Test environment file setup."""
    print_header("Testing Environment Setup")
    
    if os.path.exists('.env'):
        print_test_result("Environment file (.env)", True, "File exists")
        
        # Check if it has required variables
        try:
            with open('.env', 'r') as f:
                content = f.read()
                
            required_vars = ['BINANCE_API_KEY', 'BINANCE_SECRET_KEY', 'TRADING_MODE']
            for var in required_vars:
                if var in content:
                    print_test_result(f"Environment variable {var}", True)
                else:
                    print_test_result(f"Environment variable {var}", False, "Variable not found")
        except Exception as e:
            print_test_result("Read .env file", False, str(e))
    else:
        print_test_result("Environment file (.env)", False, "File missing - copy from .env.example")
        return False
    
    return True

def run_all_tests():
    """Run all tests."""
    print("🧪 CRYPTO TRADING BOT - COMPREHENSIVE TEST SUITE")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Python Environment", test_python_environment),
        ("Project Structure", test_project_structure),
        ("Configuration", test_configuration),
        ("Trading Strategies", test_strategies),
        ("Portfolio & Risk Management", test_portfolio_and_risk),
        ("Binance Client", test_binance_client),
        ("Environment Setup", test_environment_file),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print_test_result(test_name, False, f"Test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print_header("TEST SUMMARY")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! The bot is ready for paper trading.")
        print("\nNext steps:")
        print("1. Set up your .env file with API keys")
        print("2. Run: python main.py (in paper mode)")
        print("3. Monitor with: python monitor.py")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please fix the issues before proceeding.")
        print("\nCommon fixes:")
        print("- Install missing packages: pip install -r requirements.txt")
        print("- Copy .env.example to .env")
        print("- Check file paths and permissions")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\nUnexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)
