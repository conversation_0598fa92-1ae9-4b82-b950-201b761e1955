@echo off
echo ========================================
echo  Crypto Trading Bot - Installation
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python found!
python --version

echo.
echo Installing required packages...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install packages
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo.
echo Setting up environment file...
if not exist .env (
    copy .env.example .env
    echo Created .env file from template
) else (
    echo .env file already exists
)

echo.
echo Creating necessary directories...
if not exist logs mkdir logs
if not exist data mkdir data
if not exist backtest_results mkdir backtest_results

echo.
echo Running comprehensive tests...
python test_bot.py

echo.
echo ========================================
echo  Installation Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Edit .env file with your Binance API keys
echo 2. Run the bot: python main.py
echo 3. Monitor: python monitor.py
echo.
echo For paper trading (recommended):
echo   Set TRADING_MODE=paper in .env file
echo.
pause
