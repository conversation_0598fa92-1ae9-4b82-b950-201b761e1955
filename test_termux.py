#!/usr/bin/env python3
"""
Termux-Specific Test Script for Trading Bot
Simplified tests that work reliably on Android/Termux.
"""

import sys
import os
import warnings

# Suppress pandas warnings
warnings.filterwarnings('ignore', category=FutureWarning)

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print('='*60)

def print_test_result(test_name, passed, details=""):
    """Print test result with formatting."""
    status = "✅ PASS" if passed else "❌ FAIL"
    if details:
        print(f"{status} - {test_name}")
        print(f"    {details}")
    else:
        print(f"{status} - {test_name}")

def test_python_environment():
    """Test Python environment and basic imports."""
    print_header("Testing Python Environment")
    
    # Test Python version
    if sys.version_info >= (3, 7):
        print_test_result("Python Version", True, f"Python {sys.version.split()[0]}")
    else:
        print_test_result("Python Version", False, f"Python {sys.version.split()[0]} (need 3.7+)")
        return False
    
    # Test core imports
    try:
        import pandas as pd
        print_test_result("Pandas Import", True, f"Version {pd.__version__}")
    except ImportError as e:
        print_test_result("Pandas Import", False, str(e))
        return False
    
    try:
        import numpy as np
        print_test_result("NumPy Import", True, f"Version {np.__version__}")
    except ImportError as e:
        print_test_result("NumPy Import", False, str(e))
        return False
    
    try:
        import requests
        print_test_result("Requests Import", True)
    except ImportError as e:
        print_test_result("Requests Import", False, str(e))
        return False
    
    try:
        from dotenv import load_dotenv
        print_test_result("Python-dotenv Import", True)
    except ImportError as e:
        print_test_result("Python-dotenv Import", False, str(e))
        return False
    
    return True

def test_project_structure():
    """Test project file structure."""
    print_header("Testing Project Structure")
    
    required_files = [
        'main.py',
        'config/config.yaml',
        '.env',
        'src/binance_client.py',
        'src/portfolio.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print_test_result(f"File: {file_path}", True)
        else:
            print_test_result(f"File: {file_path}", False, "Missing")
            all_exist = False
    
    return all_exist

def test_environment_variables():
    """Test environment variables."""
    print_header("Testing Environment Variables")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = [
            'BINANCE_API_KEY',
            'BINANCE_SECRET_KEY',
            'TRADING_MODE'
        ]
        
        all_present = True
        for var in required_vars:
            value = os.getenv(var)
            if value:
                # Don't show full API keys for security
                if 'KEY' in var:
                    display_value = f"{value[:8]}...{value[-8:]}" if len(value) > 16 else "***"
                else:
                    display_value = value
                print_test_result(f"Environment Variable: {var}", True, display_value)
            else:
                print_test_result(f"Environment Variable: {var}", False, "Not found")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print_test_result("Environment Variables", False, str(e))
        return False

def test_basic_imports():
    """Test basic trading bot imports."""
    print_header("Testing Trading Bot Imports")
    
    # Add src to path
    sys.path.append('src')
    
    try:
        from binance_client import BinanceClient
        print_test_result("BinanceClient Import", True)
    except ImportError as e:
        print_test_result("BinanceClient Import", False, str(e))
        return False
    
    try:
        from portfolio import Portfolio
        print_test_result("Portfolio Import", True)
    except ImportError as e:
        print_test_result("Portfolio Import", False, str(e))
        return False
    
    try:
        from altcoin_scanner import AltcoinScanner
        print_test_result("AltcoinScanner Import", True)
    except ImportError as e:
        print_test_result("AltcoinScanner Import", False, str(e))
        return False
    
    return True

def test_simple_data_processing():
    """Test simple data processing with pandas."""
    print_header("Testing Data Processing")
    
    try:
        import pandas as pd
        import numpy as np
        
        # Create simple test data
        dates = pd.date_range('2023-01-01', periods=10, freq='1h')  # Fixed: 'H' -> 'h'
        prices = [100 + i for i in range(10)]
        
        df = pd.DataFrame({
            'timestamp': dates,
            'close': prices,
            'volume': [1000 + i*100 for i in range(10)]
        })
        
        # Test basic operations
        if len(df) == 10:
            print_test_result("DataFrame Creation", True, f"{len(df)} rows")
        else:
            print_test_result("DataFrame Creation", False)
            return False
        
        # Test calculations
        df['sma'] = df['close'].rolling(window=3).mean()
        if not df['sma'].isna().all():
            print_test_result("Simple Moving Average", True)
        else:
            print_test_result("Simple Moving Average", False)
            return False
        
        return True
        
    except Exception as e:
        print_test_result("Data Processing", False, str(e))
        return False

def test_network_connectivity():
    """Test network connectivity to testnet."""
    print_header("Testing Network Connectivity")
    
    try:
        import requests
        
        # Test testnet endpoints
        testnet_endpoints = [
            "https://testnet.binancefuture.com/fapi/v1/ping",
            "https://testnet.binance.vision/api/v3/ping"
        ]
        
        success_count = 0
        for endpoint in testnet_endpoints:
            try:
                response = requests.get(endpoint, timeout=10)
                if response.status_code == 200:
                    print_test_result(f"Testnet: {endpoint.split('/')[2]}", True)
                    success_count += 1
                else:
                    print_test_result(f"Testnet: {endpoint.split('/')[2]}", False, f"HTTP {response.status_code}")
            except Exception as e:
                print_test_result(f"Testnet: {endpoint.split('/')[2]}", False, str(e))
        
        return success_count > 0
        
    except Exception as e:
        print_test_result("Network Connectivity", False, str(e))
        return False

def main():
    """Run all tests."""
    print("🤖 TERMUX TRADING BOT TEST SUITE")
    print("Simplified tests for Android/Termux compatibility")
    print("=" * 60)
    
    tests = [
        ("Python Environment", test_python_environment),
        ("Project Structure", test_project_structure),
        ("Environment Variables", test_environment_variables),
        ("Trading Bot Imports", test_basic_imports),
        ("Data Processing", test_simple_data_processing),
        ("Network Connectivity", test_network_connectivity),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print_test_result(test_name, False, f"Test crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    print_header("TEST SUMMARY")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("Your Termux environment is ready for trading bot!")
        print("Run: python main.py")
    else:
        print(f"\n⚠️  {total - passed} tests failed")
        print("Please fix the issues above before running the trading bot")
        
        if passed >= total * 0.7:  # 70% pass rate
            print("\n💡 Most tests passed - you can try running the bot anyway:")
            print("python main.py")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite crashed: {e}")
        sys.exit(1)
