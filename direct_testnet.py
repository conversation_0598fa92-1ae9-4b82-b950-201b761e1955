"""
Direct testnet client that bypasses the wrapper.
"""

import os
import sys
import time
from dotenv import load_dotenv
from binance.client import Client
from binance.exceptions import Binance<PERSON>IException

def main():
    """Test direct testnet connection."""
    print("🧪 Direct Testnet Connection Test")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Get API keys
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    
    if not api_key or not secret_key:
        print("❌ API keys not found in .env file")
        return
    
    print(f"API Key: {api_key[:5]}...{api_key[-5:]}")
    print(f"Secret Key: {secret_key[:5]}...{secret_key[-5:]}")
    
    try:
        # Create client with extended recv_window
        print("\n🔄 Creating testnet client...")
        client = Client(
            api_key=api_key,
            api_secret=secret_key,
            testnet=True
        )

        # Set recv_window to 5000ms (5 seconds)
        client.RECV_WINDOW = 5000
        
        print("✅ Client created successfully")
        
        # Test ping
        print("\n🔄 Testing ping...")
        client.ping()
        print("✅ Ping successful")
        
        # Get server time
        print("\n🔄 Getting server time...")
        server_time = client.get_server_time()
        local_time = int(time.time() * 1000)
        time_diff = abs(server_time['serverTime'] - local_time)
        
        print(f"✅ Server time: {server_time['serverTime']}")
        print(f"✅ Local time: {local_time}")
        print(f"Time difference: {time_diff} ms")
        
        # Test futures account
        print("\n🔄 Getting futures account info...")
        try:
            account = client.futures_account()
            
            print("✅ Futures account info retrieved")
            
            # Check balance
            assets = account.get('assets', [])
            usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
            
            if usdt_asset:
                balance = float(usdt_asset.get('availableBalance', 0))
                print(f"💰 Available Balance: {balance:.2f} USDT")
                
                if balance < 10:
                    print("⚠️  Low balance - you may need more testnet funds")
                else:
                    print("✅ Sufficient balance for testing")
            else:
                print("❌ No USDT asset found")
        except BinanceAPIException as e:
            print(f"❌ API Error: {e}")
            print("\n🔧 Trying with even larger recv_window...")
            
            # Try with even larger recv_window
            client.RECV_WINDOW = 60000  # 60 seconds
            
            try:
                account = client.futures_account()
                print("✅ Futures account info retrieved with extended recv_window")
                
                # Check balance
                assets = account.get('assets', [])
                usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
                
                if usdt_asset:
                    balance = float(usdt_asset.get('availableBalance', 0))
                    print(f"💰 Available Balance: {balance:.2f} USDT")
                else:
                    print("❌ No USDT asset found")
            except BinanceAPIException as e:
                print(f"❌ API Error with extended recv_window: {e}")
                
                # Try with timestamp adjustment
                print("\n🔧 Trying with timestamp adjustment...")
                try:
                    # Get server time
                    server_time = client.get_server_time()
                    
                    # Set timestamp offset
                    client.timestamp_offset = server_time['serverTime'] - int(time.time() * 1000)
                    print(f"Setting timestamp offset: {client.timestamp_offset} ms")
                    
                    # Try again
                    account = client.futures_account()
                    print("✅ Futures account info retrieved with timestamp adjustment")
                    
                    # Check balance
                    assets = account.get('assets', [])
                    usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
                    
                    if usdt_asset:
                        balance = float(usdt_asset.get('availableBalance', 0))
                        print(f"💰 Available Balance: {balance:.2f} USDT")
                    else:
                        print("❌ No USDT asset found")
                except BinanceAPIException as e:
                    print(f"❌ API Error with timestamp adjustment: {e}")
        
        print("\n🚀 Test completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
