"""
Permanent fix for Binance API timestamp errors.
"""

import os
import sys
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient

def test_timestamp_fix():
    """Test and fix timestamp synchronization."""
    print("🔧 Fixing Binance API Timestamp Issues")
    print("=" * 50)
    
    try:
        # Initialize client
        print("📡 Initializing Binance client...")
        client = BinanceClient(testnet=True)
        
        # Test initial connection
        print("🔄 Testing initial connection...")
        try:
            account = client.futures_get_account_info()
            if account:
                print("✅ Connection successful!")
                return True
        except Exception as e:
            print(f"❌ Initial connection failed: {e}")
        
        # Try manual timestamp fix
        print("🔧 Attempting timestamp synchronization...")
        if client.fix_timestamp_sync():
            print("✅ Timestamp sync successful!")
            
            # Test connection again
            print("🔄 Testing connection after timestamp fix...")
            account = client.futures_get_account_info()
            
            if account:
                print("✅ Connection successful after timestamp fix!")
                
                # Show account info
                assets = account.get('assets', [])
                usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
                
                if usdt_asset:
                    balance = float(usdt_asset.get('availableBalance', 0))
                    print(f"💰 Testnet Balance: {balance:.2f} USDT")
                
                return True
            else:
                print("❌ Connection still failing after timestamp fix")
                return False
        else:
            print("❌ Timestamp sync failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during timestamp fix: {e}")
        return False

def check_system_time():
    """Check if system time is synchronized."""
    print("\n🕐 Checking System Time Synchronization")
    print("=" * 50)
    
    try:
        import requests
        
        # Get system time
        local_time = int(time.time() * 1000)
        
        # Get Binance server time
        response = requests.get('https://api.binance.com/api/v3/time', timeout=10)
        if response.status_code == 200:
            server_time = response.json()['serverTime']
            
            time_diff = abs(server_time - local_time)
            
            print(f"Local time: {time.ctime(local_time / 1000)}")
            print(f"Binance time: {time.ctime(server_time / 1000)}")
            print(f"Time difference: {time_diff} ms")
            
            if time_diff > 5000:  # 5 seconds
                print("❌ System time is significantly out of sync!")
                print("\n🔧 To fix on Windows:")
                print("1. Right-click on clock → 'Adjust date/time'")
                print("2. Turn ON 'Set time automatically'")
                print("3. Click 'Sync now'")
                print("4. Or run as admin: w32tm /resync")
                return False
            elif time_diff > 1000:  # 1 second
                print("⚠️  System time has minor drift")
                return True
            else:
                print("✅ System time is well synchronized")
                return True
        else:
            print("❌ Could not get Binance server time")
            return False
            
    except Exception as e:
        print(f"❌ Error checking system time: {e}")
        return False

def main():
    """Main function to fix timestamp issues."""
    print("🚀 Binance API Timestamp Fix Tool")
    print("=" * 60)
    
    # Check system time first
    time_ok = check_system_time()
    
    if not time_ok:
        print("\n⚠️  Please fix your system time first, then run this script again.")
        return False
    
    # Test timestamp fix
    if test_timestamp_fix():
        print("\n🎉 Timestamp fix successful!")
        print("✅ You can now run the trading bot without timestamp errors")
        print("\n🚀 Next steps:")
        print("1. Run: python main.py")
        print("2. The bot should connect successfully to testnet")
        return True
    else:
        print("\n❌ Timestamp fix failed")
        print("\n🔧 Manual solutions:")
        print("1. Sync your system clock")
        print("2. Check your internet connection")
        print("3. Try using a VPN if Binance is blocked")
        print("4. Verify your API keys are for testnet")
        return False

if __name__ == "__main__":
    main()
