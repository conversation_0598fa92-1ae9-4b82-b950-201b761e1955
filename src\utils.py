"""
Utility functions for the trading bot.
"""

import logging
import os
import yaml
from typing import Dict, Any
from datetime import datetime
import pandas as pd

def setup_logging(log_level: str = 'INFO', log_file: str = None):
    """
    Setup logging configuration.
    
    Args:
        log_level: Logging level
        log_file: Log file path (optional)
    """
    # Create logs directory if it doesn't exist
    if log_file:
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
    
    # Configure logging with UTF-8 support
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(log_format))

    handlers = [console_handler]

    # File handler with UTF-8 encoding
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        handlers.append(file_handler)

    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        handlers=handlers,
        force=True  # Override any existing configuration
    )

def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from YAML file.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Configuration dictionary
    """
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        return config
    except Exception as e:
        logging.error(f"Error loading config from {config_path}: {e}")
        return {}

def save_config(config: Dict[str, Any], config_path: str):
    """
    Save configuration to YAML file.
    
    Args:
        config: Configuration dictionary
        config_path: Path to save configuration
    """
    try:
        # Create directory if it doesn't exist
        config_dir = os.path.dirname(config_path)
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir)
        
        with open(config_path, 'w') as file:
            yaml.dump(config, file, default_flow_style=False, indent=2)
        logging.info(f"Configuration saved to {config_path}")
    except Exception as e:
        logging.error(f"Error saving config to {config_path}: {e}")

def format_currency(amount: float, currency: str = 'USDT', decimals: int = 2) -> str:
    """
    Format currency amount for display.
    
    Args:
        amount: Amount to format
        currency: Currency symbol
        decimals: Number of decimal places
        
    Returns:
        Formatted currency string
    """
    return f"{amount:.{decimals}f} {currency}"

def format_percentage(value: float, decimals: int = 2) -> str:
    """
    Format percentage for display.
    
    Args:
        value: Percentage value (0.05 = 5%)
        decimals: Number of decimal places
        
    Returns:
        Formatted percentage string
    """
    return f"{value * 100:.{decimals}f}%"

def calculate_returns(prices: pd.Series) -> pd.Series:
    """
    Calculate returns from price series.
    
    Args:
        prices: Price series
        
    Returns:
        Returns series
    """
    return prices.pct_change().dropna()

def calculate_volatility(returns: pd.Series, periods: int = 252) -> float:
    """
    Calculate annualized volatility.
    
    Args:
        returns: Returns series
        periods: Number of periods per year (252 for daily, 8760 for hourly)
        
    Returns:
        Annualized volatility
    """
    return returns.std() * (periods ** 0.5)

def calculate_sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.0, 
                          periods: int = 252) -> float:
    """
    Calculate Sharpe ratio.
    
    Args:
        returns: Returns series
        risk_free_rate: Risk-free rate (annualized)
        periods: Number of periods per year
        
    Returns:
        Sharpe ratio
    """
    excess_returns = returns.mean() * periods - risk_free_rate
    volatility = calculate_volatility(returns, periods)
    
    if volatility == 0:
        return 0.0
    
    return excess_returns / volatility

def calculate_max_drawdown(equity_curve: pd.Series) -> float:
    """
    Calculate maximum drawdown.
    
    Args:
        equity_curve: Equity curve series
        
    Returns:
        Maximum drawdown (as positive percentage)
    """
    peak = equity_curve.expanding().max()
    drawdown = (equity_curve - peak) / peak
    return abs(drawdown.min())

def validate_symbol(symbol: str) -> bool:
    """
    Validate trading symbol format.
    
    Args:
        symbol: Trading symbol
        
    Returns:
        True if valid symbol format
    """
    # Basic validation for Binance symbols
    if not symbol or len(symbol) < 6:
        return False
    
    # Should end with common quote currencies
    quote_currencies = ['USDT', 'BTC', 'ETH', 'BNB', 'BUSD']
    return any(symbol.endswith(quote) for quote in quote_currencies)

def round_to_precision(value: float, precision: int) -> float:
    """
    Round value to specified precision.
    
    Args:
        value: Value to round
        precision: Number of decimal places
        
    Returns:
        Rounded value
    """
    return round(value, precision)

def get_timestamp() -> str:
    """Get current timestamp as string."""
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def create_directory(path: str):
    """Create directory if it doesn't exist."""
    if not os.path.exists(path):
        os.makedirs(path)
        logging.info(f"Directory created: {path}")

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Safely divide two numbers.
    
    Args:
        numerator: Numerator
        denominator: Denominator
        default: Default value if division by zero
        
    Returns:
        Division result or default value
    """
    if denominator == 0:
        return default
    return numerator / denominator
