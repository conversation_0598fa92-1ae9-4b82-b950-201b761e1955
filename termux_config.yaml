# Termux-Optimized Trading Bot Configuration

# API Configuration (use environment variables)
binance:
  testnet: true  # Always use testnet on mobile for safety
  api_key: ${BINANCE_API_KEY}
  secret_key: ${BINANCE_SECRET_KEY}

# Mobile-Optimized Trading Settings
trading:
  max_position_value_usd: 50    # Reduced for mobile trading
  max_concurrent_positions: 1   # Single position only
  trading_interval_seconds: 600 # 10 minutes (longer for mobile)
  
# Chart Analysis (Lightweight for Mobile)
chart_analysis:
  enabled: true
  min_confidence_threshold: 75  # Higher threshold for mobile
  min_risk_reward_ratio: 2.0    # Better risk/reward for mobile
  support_resistance_periods: 15 # Reduced for performance
  
# Mobile-Friendly Dashboard
trading_dashboard:
  enabled: true
  type: text                    # Text-only dashboard for mobile
  update_interval: 60           # Update every minute
  max_history: 20               # Reduced history for memory
  display_symbols: 5            # Show top 5 symbols only
  
# Reduced Altcoin Scanning for Mobile
altcoin_scanning:
  enabled: true
  max_symbols: 8                # Limit to 8 symbols for performance
  scan_interval_minutes: 15     # Scan every 15 minutes
  
# Mobile-Optimized Risk Management
risk_management:
  max_position_value_usd: 50    # $50 max per trade on mobile
  max_concurrent_positions: 1   # Single position only
  profit_check_interval_minutes: 10  # Check profits every 10 min
  max_hold_minutes: 60          # Max 1 hour hold time
  
# Profit Taking (Mobile-Friendly)
single_position:
  enabled: true
  max_positions: 1
  profit_taking_intervals:
    10_minutes:
      min_profit: 0.0           # Take any profit after 10 min
    30_minutes:
      max_loss: 1.0             # Close if loss > $1 after 30 min
    60_minutes:
      force_close: true         # Force close after 1 hour
      
# Logging (Reduced for Mobile Storage)
logging:
  level: INFO
  max_log_files: 3
  max_log_size_mb: 5
  
# Performance Optimizations for Mobile
performance:
  enable_caching: true
  cache_duration_minutes: 30
  reduce_api_calls: true
  batch_operations: true
