#!/usr/bin/env python3
"""
Real-time Altcoin Trading Dashboard
"""

import json
import sys
import os
from datetime import datetime

def main():
    print("🪙 ALTCOIN TRADING BOT - LIVE DASHBOARD")
    print("=" * 60)
    print()

    # Read recent logs to see discovered altcoins
    try:
        with open('logs/trading_bot.log', 'r') as f:
            lines = f.readlines()
        
        print("🔍 ALTCOIN DISCOVERY IN PROGRESS:")
        print("-" * 40)
        
        discovered_altcoins = []
        analyzing_altcoins = []
        
        for line in lines:
            # Look for discovered altcoins
            if "Vol: $" in line and "Change:" in line and "Price: $" in line:
                parts = line.split(" - ")[-1].strip()
                if "#" in parts:
                    discovered_altcoins.append(parts)
            
            # Look for altcoins being analyzed
            elif "Analyzing" in line and "USDT" in line:
                parts = line.split(" - ")[-1].strip()
                analyzing_altcoins.append(parts)
        
        # Show discovered altcoins
        if discovered_altcoins:
            print("💎 TOP ALTCOINS DISCOVERED:")
            for altcoin in discovered_altcoins[-5:]:  # Last 5
                print(f"  {altcoin}")
            print()
        
        # Show analysis progress
        if analyzing_altcoins:
            print("🔬 CURRENTLY ANALYZING:")
            for analysis in analyzing_altcoins[-5:]:  # Last 5
                print(f"  {analysis}")
            print()
        
        # Show recent activity
        print("📊 RECENT BOT ACTIVITY:")
        print("-" * 30)
        for line in lines[-8:]:
            if line.strip():
                parts = line.split(' - ')
                if len(parts) >= 3:
                    timestamp = parts[0]
                    message = ' - '.join(parts[2:]).strip()
                    time_only = timestamp.split(' ')[-1] if ' ' in timestamp else timestamp
                    print(f"{time_only}: {message}")
        print()
        
    except Exception as e:
        print(f"Error reading logs: {e}")

    # Check portfolio
    try:
        with open('portfolio_state.json', 'r') as f:
            portfolio = json.load(f)
        
        print("💰 ALTCOIN PORTFOLIO STATUS:")
        print("-" * 30)
        balance = portfolio["balances"]["USDT"]
        total_pnl = portfolio["performance"]["total_pnl"]
        total_trades = portfolio["performance"]["total_trades"]
        positions = len(portfolio["positions"])
        
        print(f"Balance: ${balance:,.2f} USDT")
        print(f"Total P&L: ${total_pnl:.4f}")
        print(f"Total Trades: {total_trades}")
        print(f"Open Positions: {positions}")
        
        # Show open positions if any
        if portfolio["positions"]:
            print("\n🎯 OPEN ALTCOIN POSITIONS:")
            for symbol, pos in portfolio["positions"].items():
                side = pos["side"]
                quantity = pos["quantity"]
                entry_price = pos["entry_price"]
                print(f"  {symbol}: {side} {quantity:.6f} @ ${entry_price:.4f}")
        
        print()
        
    except Exception as e:
        print(f"Error reading portfolio: {e}")

    # Show current altcoin prices
    try:
        sys.path.append('src')
        from binance_client import BinanceClient
        
        client = BinanceClient()
        
        # Popular altcoins to check
        altcoins = [
            'ADAUSDT', 'SOLUSDT', 'DOTUSDT', 'LINKUSDT', 'MATICUSDT',
            'AVAXUSDT', 'ATOMUSDT', 'NEARUSDT', 'FTMUSDT', 'ALGOUSDT'
        ]
        
        print("📈 MONITORED ALTCOIN PRICES:")
        print("-" * 30)
        for symbol in altcoins:
            try:
                price = client.get_current_price(symbol)
                coin_name = symbol.replace('USDT', '')
                print(f"{coin_name:>6}: ${price:>8.4f}")
            except:
                pass
        print()
        
    except Exception as e:
        print(f"Error getting altcoin prices: {e}")

    print("🤖 ALTCOIN BOT FEATURES:")
    print("-" * 30)
    print("✓ Excludes Bitcoin, Ethereum, and major coins")
    print("✓ Scans 400+ altcoins automatically")
    print("✓ Lower confidence threshold (65%) for more opportunities")
    print("✓ Prefers volatile altcoins (0.5% - 15% daily change)")
    print("✓ Minimum $500K daily volume requirement")
    print("✓ Multi-timeframe analysis (15m, 1h, 4h)")
    print("✓ Advanced technical indicators")
    print("✓ Maximum 5 concurrent altcoin positions")
    print("✓ Scans for new altcoins every 15 minutes")
    print()

    print("⚡ ALTCOIN TRADING CRITERIA:")
    print("-" * 30)
    print("• Confidence Score: ≥ 65% (lower for more altcoin opportunities)")
    print("• Volume: ≥ $500K daily trading volume")
    print("• Price Range: $0.001 - $500 (excludes very expensive tokens)")
    print("• Volatility: 0.5% - 15% daily change preferred")
    print("• Technical Analysis: Multi-timeframe alignment required")
    print("• Risk Management: Position sizing and stop-losses active")
    print()

    # Check if bot is running
    try:
        with open('logs/trading_bot.log', 'r') as f:
            lines = f.readlines()
        
        if lines:
            last_line = lines[-1]
            if "Analyzing" in last_line:
                print("🟢 STATUS: ACTIVELY ANALYZING ALTCOINS")
            else:
                print("🟢 STATUS: ALTCOIN BOT RUNNING")
        else:
            print("🔴 STATUS: NO RECENT ACTIVITY")
            
    except:
        print("❓ STATUS: UNKNOWN")
    
    print("🛡️  Mode: PAPER TRADING (Safe - No Real Money)")
    print("🪙 Focus: ALTCOINS ONLY (No BTC/ETH)")
    print("🛑 Stop with: Ctrl+C in the bot terminal")
    print()
    print(f"📅 Dashboard updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
