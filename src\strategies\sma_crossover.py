"""
Simple Moving Average (SMA) Crossover Strategy.
"""

import pandas as pd
import numpy as np
from typing import Dict
from .base_strategy import BaseStrategy
import logging

logger = logging.getLogger(__name__)

class SMACrossoverStrategy(BaseStrategy):
    """
    SMA Crossover Strategy:
    - Buy when short SMA crosses above long SMA
    - Sell when short SMA crosses below long SMA
    """
    
    def __init__(self, config: Dict):
        """
        Initialize SMA Crossover strategy.
        
        Args:
            config: Strategy configuration containing:
                - short_window: Short SMA period
                - long_window: Long SMA period
        """
        super().__init__("SMA_Crossover", config)
        self.short_window = config.get('short_window', 10)
        self.long_window = config.get('long_window', 30)
        
        logger.info(f"SMA Crossover Strategy initialized: {self.short_window}/{self.long_window}")
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate SMA indicators.
        
        Args:
            data: OHLCV data
            
        Returns:
            DataFrame with SMA indicators added
        """
        df = data.copy()
        
        # Calculate SMAs
        df[f'SMA_{self.short_window}'] = df['close'].rolling(window=self.short_window).mean()
        df[f'SMA_{self.long_window}'] = df['close'].rolling(window=self.long_window).mean()
        
        # Calculate crossover signals
        df['SMA_diff'] = df[f'SMA_{self.short_window}'] - df[f'SMA_{self.long_window}']
        df['SMA_diff_prev'] = df['SMA_diff'].shift(1)
        
        # Identify crossovers
        df['golden_cross'] = (df['SMA_diff'] > 0) & (df['SMA_diff_prev'] <= 0)
        df['death_cross'] = (df['SMA_diff'] < 0) & (df['SMA_diff_prev'] >= 0)
        
        return df
    
    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """
        Generate trading signal based on SMA crossover.
        
        Args:
            data: OHLCV data with indicators
            symbol: Trading symbol
            
        Returns:
            Signal: 'BUY', 'SELL', or 'HOLD'
        """
        if len(data) < self.long_window:
            return 'HOLD'
        
        # Calculate indicators
        df = self.calculate_indicators(data)
        
        # Get latest values
        latest = df.iloc[-1]
        current_price = latest['close']
        
        # Check for crossover signals
        if latest['golden_cross']:
            signal = 'BUY'
        elif latest['death_cross']:
            signal = 'SELL'
        else:
            signal = 'HOLD'
        
        # Log signal with indicators
        indicators = {
            f'SMA_{self.short_window}': latest[f'SMA_{self.short_window}'],
            f'SMA_{self.long_window}': latest[f'SMA_{self.long_window}'],
            'SMA_diff': latest['SMA_diff']
        }
        
        if signal != 'HOLD':
            self.log_signal(symbol, signal, current_price, indicators)
        
        return signal
    
    def get_strategy_info(self) -> Dict:
        """Get strategy information."""
        return {
            'name': self.name,
            'type': 'Trend Following',
            'short_window': self.short_window,
            'long_window': self.long_window,
            'description': f'SMA Crossover ({self.short_window}/{self.long_window})'
        }
