"""
Update trading symbols with all available altcoins from Binance Futures.
"""

import os
import sys
import logging
import yaml
from dotenv import load_dotenv
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from altcoin_scanner import AltcoinScanner
from binance_client import BinanceClient
from utils import setup_logging

def load_config(config_path: str = 'config/config.yaml'):
    """Load configuration from YAML file."""
    try:
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"Error loading config: {e}")
        return None

def save_config(config, config_path: str = 'config/config.yaml'):
    """Save configuration to YAML file."""
    try:
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        return True
    except Exception as e:
        print(f"Error saving config: {e}")
        return False

def update_trading_symbols(max_symbols: int = 100):
    """
    Update trading symbols with all available altcoins.
    
    Args:
        max_symbols: Maximum number of symbols to include
    """
    print("🔍 Scanning for all available altcoins on Binance Futures...")
    
    # Load environment variables
    load_dotenv()
    
    # Setup logging
    setup_logging(log_level="INFO", log_file="logs/altcoin_update.log")
    logger = logging.getLogger(__name__)
    
    # Load configuration
    config_path = 'config/config.yaml'
    config = load_config(config_path)
    
    if not config:
        print("❌ Failed to load configuration")
        return False
    
    # Initialize Binance client
    client = BinanceClient()
    
    # Initialize altcoin scanner
    scanner_config = {
        'min_volume_24h': 500000,  # $500K minimum volume
        'min_price': 0.0001,       # Minimum price
        'max_price': 10000,        # Maximum price
        'quote_asset': 'USDT',     # Only USDT pairs
        'scan_interval_hours': 24  # Rescan daily
    }
    
    scanner = AltcoinScanner(client, scanner_config)
    
    # Scan for all qualifying altcoins
    print("Scanning Binance Futures for all available altcoins...")
    altcoins = scanner.scan_altcoins(force_rescan=True)
    
    if not altcoins:
        print("❌ No altcoins found")
        return False
    
    # Limit to max_symbols
    if len(altcoins) > max_symbols:
        print(f"⚠️  Limiting to top {max_symbols} altcoins by volume")
        altcoins = altcoins[:max_symbols]
    
    # Update configuration
    if 'trading' not in config:
        config['trading'] = {}
    
    # Save original symbols
    original_symbols = config['trading'].get('symbols', [])
    
    # Update symbols
    config['trading']['symbols'] = altcoins
    
    # Save configuration
    if save_config(config, config_path):
        print(f"✅ Updated trading symbols with {len(altcoins)} altcoins")
        print(f"   Original symbol count: {len(original_symbols)}")
        print(f"   New symbol count: {len(altcoins)}")
        
        # Export full list to file
        scanner.export_altcoin_list('altcoins_full.txt')
        
        # Show top 10 symbols
        print("\n📊 Top 10 altcoins by volume:")
        for i, symbol in enumerate(altcoins[:10], 1):
            print(f"   {i}. {symbol}")
        
        # Show summary
        summary = scanner.get_altcoin_summary()
        print(f"\n📈 Total 24h Volume: ${summary['total_volume_24h']:,.2f}")
        print(f"📉 Average Price Change: {summary['avg_price_change_24h']:.2f}%")
        
        return True
    else:
        print("❌ Failed to save configuration")
        return False

def main():
    """Main function."""
    print("🚀 Altcoin Symbol Updater for Binance Futures")
    print("=" * 60)
    print("This script will update your trading bot configuration with all available altcoins.")
    print("It will replace the current symbol list with all qualifying altcoins from Binance Futures.")
    print()
    
    # Ask for confirmation
    response = input("Do you want to update trading symbols with all available altcoins? (y/N): ").lower()
    
    if response != 'y':
        print("Update cancelled.")
        return
    
    # Ask for maximum symbols
    max_symbols = 100
    try:
        max_input = input(f"Maximum number of symbols to include (default: {max_symbols}): ").strip()
        if max_input:
            max_symbols = int(max_input)
    except ValueError:
        print(f"Invalid input. Using default: {max_symbols}")
    
    # Update symbols
    success = update_trading_symbols(max_symbols)
    
    if success:
        print("\n✅ Trading symbols updated successfully!")
        print("\n📋 Next Steps:")
        print("1. Review the updated configuration in config/config.yaml")
        print("2. Check the full altcoin list in altcoins_full.txt")
        print("3. Test your setup: python test_live_setup.py")
        print("4. Start the bot: python main.py")
    else:
        print("\n❌ Failed to update trading symbols")
        print("Please check the logs for more information")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nUpdate cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
