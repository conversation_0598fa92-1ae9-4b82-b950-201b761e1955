"""
Test script to verify precision handling fixes the -1111 error.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient

def test_symbol_precision():
    """Test getting symbol precision information."""
    print("🔍 Testing Symbol Precision Fetching")
    print("=" * 60)
    
    try:
        # Initialize client
        client = BinanceClient(testnet=True)
        
        # Test symbols that were failing
        test_symbols = ['MKRUSDT', 'LINKUSDT', 'DOGEUSDT', 'BTCUSDT']
        
        for symbol in test_symbols:
            print(f"\n📊 Testing precision for {symbol}...")
            
            precision_info = client.get_symbol_precision(symbol)
            
            if precision_info:
                print(f"   ✅ Precision info retrieved:")
                print(f"      Quantity precision: {precision_info.get('quantity_precision', 'N/A')}")
                print(f"      Price precision: {precision_info.get('price_precision', 'N/A')}")
                print(f"      Step size: {precision_info.get('step_size', 'N/A')}")
                print(f"      Tick size: {precision_info.get('tick_size', 'N/A')}")
            else:
                print(f"   ❌ Failed to get precision info for {symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing symbol precision: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_precision_rounding():
    """Test precision rounding functions."""
    print("\n🔧 Testing Precision Rounding")
    print("=" * 60)
    
    try:
        client = BinanceClient(testnet=True)
        
        # Test cases that were causing errors
        test_cases = [
            {'symbol': 'MKRUSDT', 'quantity': 1.640588, 'price': 1234.567890},
            {'symbol': 'LINKUSDT', 'quantity': 195.438384, 'price': 12.345678},
            {'symbol': 'DOGEUSDT', 'quantity': 1000.123456789, 'price': 0.123456789},
            {'symbol': 'BTCUSDT', 'quantity': 0.001234567, 'price': 50000.123456}
        ]
        
        for test_case in test_cases:
            symbol = test_case['symbol']
            original_qty = test_case['quantity']
            original_price = test_case['price']
            
            print(f"\n📊 Testing {symbol}:")
            print(f"   Original quantity: {original_qty}")
            print(f"   Original price: {original_price}")
            
            # Get precision info
            precision_info = client.get_symbol_precision(symbol)
            if not precision_info:
                print(f"   ❌ No precision info for {symbol}")
                continue
            
            # Test quantity rounding
            if 'step_size' in precision_info:
                rounded_qty = client.round_to_step_size(original_qty, precision_info['step_size'])
                print(f"   Rounded quantity (step): {rounded_qty}")
            else:
                rounded_qty = client.round_to_precision(original_qty, precision_info['quantity_precision'])
                print(f"   Rounded quantity (precision): {rounded_qty}")
            
            # Test price rounding
            if 'tick_size' in precision_info:
                rounded_price = client.round_to_tick_size(original_price, precision_info['tick_size'])
                print(f"   Rounded price (tick): {rounded_price}")
            else:
                rounded_price = client.round_to_precision(original_price, precision_info['price_precision'])
                print(f"   Rounded price (precision): {rounded_price}")
            
            print(f"   ✅ Rounding successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing precision rounding: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_placement():
    """Test actual order placement with precision (small test orders)."""
    print("\n🚀 Testing Order Placement with Precision")
    print("=" * 60)
    
    try:
        client = BinanceClient(testnet=True)
        
        # Test with very small quantities to avoid large testnet trades
        test_orders = [
            {'symbol': 'DOGEUSDT', 'side': 'BUY', 'quantity': 10.123456789},  # Small DOGE order
            {'symbol': 'BTCUSDT', 'side': 'BUY', 'quantity': 0.001234567}    # Very small BTC order
        ]
        
        for test_order in test_orders:
            symbol = test_order['symbol']
            side = test_order['side']
            quantity = test_order['quantity']
            
            print(f"\n📊 Testing order: {side} {quantity} {symbol}")
            
            # This should now handle precision automatically
            order_result = client.futures_place_market_order(symbol, side, quantity)
            
            if order_result:
                print(f"   ✅ Order placed successfully!")
                print(f"      Order ID: {order_result.get('orderId', 'N/A')}")
                print(f"      Executed quantity: {order_result.get('executedQty', 'N/A')}")
                print(f"      Average price: {order_result.get('avgPrice', 'N/A')}")
            else:
                print(f"   ❌ Order failed (check logs for details)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing order placement: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all precision fix tests."""
    print("🧪 Precision Fix Test Suite")
    print("=" * 70)
    
    print("⚠️  This test will place small orders on TESTNET")
    print("   No real money will be used - testnet only")
    
    response = input("\nContinue with precision tests? (y/N): ")
    if response.lower() != 'y':
        print("Tests cancelled.")
        return
    
    tests = [
        ("Symbol Precision", test_symbol_precision),
        ("Precision Rounding", test_precision_rounding),
        ("Order Placement", test_order_placement)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All precision fixes working correctly!")
        print("\n🚀 Fixes Applied:")
        print("✅ Symbol precision fetching from exchange info")
        print("✅ Automatic quantity rounding to step size")
        print("✅ Automatic price rounding to tick size")
        print("✅ Precision caching for performance")
        print("✅ Enhanced error logging")
        print("\n🎯 Expected Result: No more -1111 precision errors!")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
