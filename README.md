# 🤖 Advanced AI Crypto Trading Bot

An intelligent cryptocurrency trading bot with **Advanced AI** capabilities, **automatic altcoin discovery**, and **multi-timeframe analysis**. Built for Binance with sophisticated technical analysis and risk management.

## 🚀 **Key Features**

### 🧠 **Advanced AI Trading**
- **Automatic Token Discovery**: Scans 1000+ tokens to find profitable opportunities
- **Multi-Timeframe Analysis**: Analyzes 15m, 1h, 4h, 1d charts simultaneously
- **Advanced Technical Analysis**: 20+ indicators including RSI, MACD, Bollinger Bands, Stochastic
- **Chart Pattern Recognition**: Detects bullish/bearish patterns automatically
- **Market Condition Assessment**: Adapts strategy based on market trends

### 🪙 **Altcoin Focus Mode**
- **Altcoin-Only Trading**: Excludes Bitcoin, Ethereum, and major coins
- **Smart Altcoin Discovery**: Finds hidden gems with high potential
- **Volatility Optimization**: Prefers altcoins with 0.5%-15% daily movement
- **Lower Volume Requirements**: Trades smaller altcoins ($500K+ volume)
- **Higher Position Limits**: Up to 5 concurrent altcoin positions

### 🛡️ **Advanced Risk Management**
- **Intelligent Position Sizing**: Automatically calculates optimal position sizes
- **Multi-Layer Stop Losses**: Technical and percentage-based stop losses
- **Portfolio Risk Monitoring**: Prevents overexposure and excessive risk
- **Daily Loss Limits**: Stops trading if daily losses exceed threshold
- **Confidence-Based Trading**: Only trades setups with 65%+ confidence

### 📊 **Comprehensive Analytics**
- **Real-Time Monitoring**: Live dashboard with portfolio status
- **Performance Tracking**: Win rate, Sharpe ratio, drawdown analysis
- **Backtesting Engine**: Test strategies on historical data
- **Signal Analysis**: Detailed breakdown of trading decisions
- **Multi-Asset Tracking**: Monitor multiple altcoins simultaneously

### 🔧 **Easy Setup & Management**
- **One-Click Installation**: Automated setup scripts for Windows/Linux/Mac
- **Paper Trading Mode**: Test with fake money before risking real funds
- **CLI Management**: Command-line tools for easy bot control
- **Live Monitoring**: Real-time dashboards and status updates
- **Comprehensive Logging**: Detailed activity logs for transparency

## 🎯 **Trading Strategies**

### 🤖 **Advanced AI Strategy** (Primary)
- **Multi-timeframe analysis** across 4 timeframes
- **20+ technical indicators** with intelligent weighting
- **Automatic token discovery** every 15 minutes
- **Market condition adaptation** (bullish/bearish/sideways)
- **Volume and momentum confirmation**
- **Support/resistance level detection**

### 📈 **Classic Strategies** (Optional)
- **SMA Crossover**: Simple moving average crossover signals
- **RSI Strategy**: Relative Strength Index overbought/oversold signals
- **Custom Strategies**: Extensible framework for your own strategies

## 🚀 **Quick Start**

### **Option 1: Automated Installation (Recommended)**

**Windows:**
```bash
# Download and run
install.bat
```

**Linux/Mac:**
```bash
# Download and run
chmod +x install.sh
./install.sh
```

### **Option 2: Mobile Installation (Termux) 📱**

**Run your trading bot on Android!**

```bash
# Install Termux from F-Droid, then:
git clone https://github.com/Mrteesoft/trading-bot.git
cd trading-bot
chmod +x install_termux.sh
./install_termux.sh
```

**📱 [Complete Termux Setup Guide](TERMUX_SETUP.md)**

### **Option 3: Manual Installation**

1. **Clone the repository**
   ```bash
   git clone https://github.com/Mrteesoft/trading-bot.git
   cd trading-bot
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup environment**
   ```bash
   cp .env.example .env
   # Edit .env with your Binance API keys
   ```

4. **Run comprehensive tests**
   ```bash
   python test_bot.py
   ```

### **🔑 API Setup**

1. **Get Binance API Keys**:
   - Go to [Binance API Management](https://www.binance.com/en/my/settings/api-management)
   - Create new API key
   - **For testing**: Enable only "Enable Reading"
   - **For trading**: Enable "Enable Trading" (be careful!)

2. **Configure .env file**:
   ```env
   BINANCE_API_KEY=your_actual_api_key_here
   BINANCE_SECRET_KEY=your_actual_secret_key_here
   TRADING_MODE=paper  # Start with paper trading!
   ```

## 🎮 **Usage**

### **🤖 Start the AI Trading Bot**

```bash
# Start the Advanced AI Altcoin Trading Bot
python main.py
```

### **📊 Monitor the Bot**

```bash
# Real-time altcoin dashboard
python altcoin_dashboard.py

# General status dashboard
python status_dashboard.py

# Live monitoring interface
python monitor.py
```

### **🧪 Test and Analyze**

```bash
# Run comprehensive tests
python test_bot.py

# Analyze current signals
python signal_monitor.py

# Run backtests
python cli.py backtest advanced_ai ADAUSDT 2023-12-01
```

### **⚙️ CLI Management**

```bash
# Setup the bot
python cli.py setup

# Configure settings
python cli.py config

# Run backtests
python cli.py backtest sma_crossover SOLUSDT 2023-01-01

# Monitor live
python cli.py monitor
```

## ⚙️ **Configuration**

### **🪙 Altcoin-Focused Configuration**

The bot is pre-configured for optimal altcoin trading:

```yaml
trading:
  mode: "paper"  # Always start with paper trading!
  symbols:
    # ALTCOINS ONLY - No BTC/ETH
    - "ADAUSDT"    # Cardano
    - "SOLUSDT"    # Solana
    - "DOTUSDT"    # Polkadot
    - "LINKUSDT"   # Chainlink
    - "MATICUSDT"  # Polygon
    - "AVAXUSDT"   # Avalanche
    # ... more altcoins
  base_currency: "USDT"

strategies:
  advanced_ai:
    enabled: true
    min_confidence: 65          # Lower threshold for more altcoin opportunities
    max_positions: 5            # More positions for diversification
    scan_interval: 3            # Scan every 15 minutes
    min_volume_24h: 500000      # $500K minimum for altcoins
    altcoin_focus: true         # Exclude major coins

risk_management:
  max_position_size: 0.15       # 15% per position (higher for altcoins)
  stop_loss_percentage: 0.03    # 3% stop loss
  take_profit_percentage: 0.06  # 6% take profit
  max_daily_loss: 0.08          # 8% max daily loss
```

### Environment Variables (`.env`)

```bash
# Binance API
BINANCE_API_KEY=your_api_key
BINANCE_SECRET_KEY=your_secret_key

# Trading
TRADING_MODE=paper
INITIAL_BALANCE=10000
DEFAULT_SYMBOL=BTCUSDT
BASE_CURRENCY=USDT

# Risk Management
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENTAGE=0.02
TAKE_PROFIT_PERCENTAGE=0.04

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log
```

## Trading Strategies

### SMA Crossover Strategy

- **Buy Signal**: When short-term SMA crosses above long-term SMA
- **Sell Signal**: When short-term SMA crosses below long-term SMA
- **Parameters**: `short_window`, `long_window`

### RSI Strategy

- **Buy Signal**: When RSI crosses above oversold threshold
- **Sell Signal**: When RSI crosses above overbought threshold
- **Parameters**: `rsi_period`, `oversold_threshold`, `overbought_threshold`

## Risk Management

The bot includes comprehensive risk management features:

- **Position Sizing**: Automatically calculates position size based on portfolio percentage
- **Stop Loss**: Automatically closes losing positions at specified percentage loss
- **Take Profit**: Automatically closes winning positions at specified percentage gain
- **Daily Loss Limit**: Stops trading if daily losses exceed threshold
- **Portfolio Monitoring**: Tracks overall portfolio performance

## Testing

Run the test suite to verify everything is working:

```bash
# Run all tests
pytest tests/

# Run specific test file
pytest tests/test_strategies.py

# Run with verbose output
pytest -v tests/
```

## Monitoring and Logging

The bot provides comprehensive logging:

- **Console Output**: Real-time trading activity
- **Log Files**: Detailed logs saved to `logs/trading_bot.log`
- **Portfolio State**: Automatically saved to `portfolio_state.json`

### Log Levels

- `INFO`: General trading activity
- `WARNING`: Risk management alerts
- `ERROR`: Trading errors and failures

## File Structure

```
trading-bot/
├── main.py                 # Main bot application
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
├── README.md              # This file
├── config/
│   └── config.yaml        # Trading configuration
├── src/
│   ├── binance_client.py  # Binance API wrapper
│   ├── portfolio.py       # Portfolio management
│   ├── risk_management.py # Risk management
│   ├── utils.py          # Utility functions
│   └── strategies/
│       ├── base_strategy.py    # Base strategy class
│       ├── sma_crossover.py   # SMA crossover strategy
│       └── rsi_strategy.py    # RSI strategy
├── tests/
│   ├── test_strategies.py # Strategy tests
│   └── ...
└── logs/                  # Log files (created automatically)
```

## 🛡️ **Safety & Risk Management**

### **⚠️ Important Safety Guidelines**

1. **🧪 Always Start with Paper Trading**
   - Test all strategies with fake money first
   - Run for at least 24-48 hours before considering live trading
   - Understand the bot's behavior in different market conditions

2. **💰 Start Small with Live Trading**
   - Begin with small amounts ($50-100)
   - Gradually increase as you gain confidence
   - Never risk more than you can afford to lose

3. **📊 Monitor Regularly**
   - Check the bot daily, especially during volatile markets
   - Use the monitoring dashboards provided
   - Set up alerts for significant losses

4. **🔐 API Security**
   - Use read-only API keys for testing
   - Restrict API keys to your IP address
   - Never share your API keys
   - Enable only necessary permissions

5. **🎯 Risk Management**
   - The bot includes automatic stop-losses
   - Daily loss limits prevent catastrophic losses
   - Position sizing limits prevent overexposure
   - Confidence thresholds ensure quality trades

### **📈 Expected Performance**

- **Altcoin Focus**: Higher volatility = higher potential returns AND risks
- **AI-Driven**: More sophisticated analysis = better trade selection
- **Risk-Managed**: Multiple safety layers = controlled downside
- **Adaptive**: Learns market conditions = improved performance over time

**Remember**: Past performance does not guarantee future results. Cryptocurrency trading involves substantial risk.

## 📁 **Project Structure**

```
Trading/
├── main.py                     # Main bot application
├── altcoin_dashboard.py        # Altcoin-specific dashboard
├── status_dashboard.py         # General status dashboard
├── signal_monitor.py           # Real-time signal analysis
├── test_bot.py                 # Comprehensive test suite
├── cli.py                      # Command-line interface
├── backtest.py                 # Backtesting engine
├── monitor.py                  # Live monitoring
├── setup.py                    # Setup script
├── requirements.txt            # Python dependencies
├── .env.example               # Environment template
├── config/
│   └── config.yaml            # Trading configuration
├── src/
│   ├── binance_client.py      # Binance API wrapper
│   ├── token_scanner.py       # Automatic token discovery
│   ├── advanced_technical_analysis.py  # Advanced TA engine
│   ├── multi_timeframe_analysis.py     # Multi-TF analysis
│   ├── portfolio.py           # Portfolio management
│   ├── risk_management.py     # Risk management
│   ├── utils.py              # Utility functions
│   └── strategies/
│       ├── base_strategy.py       # Base strategy class
│       ├── advanced_ai_strategy.py # AI strategy (main)
│       ├── sma_crossover.py      # SMA strategy
│       └── rsi_strategy.py       # RSI strategy
└── tests/
    └── test_strategies.py     # Strategy tests
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## ⚠️ **Disclaimer**

**FOR EDUCATIONAL PURPOSES ONLY**

This trading bot is provided for educational and research purposes only. Cryptocurrency trading involves substantial risk of loss and is not suitable for all investors.

**The authors and contributors are not responsible for any financial losses incurred through the use of this software.**

- Always do your own research (DYOR)
- Start with paper trading
- Never invest more than you can afford to lose
- Consider consulting with a financial advisor
- Understand the risks involved in cryptocurrency trading

**USE AT YOUR OWN RISK**

## 🌟 **Star the Repository**

If you find this project helpful, please consider giving it a star ⭐ on GitHub!

---

**Happy Trading! 🚀🪙**

*Built with ❤️ for the crypto community*

## Troubleshooting

### Common Issues

1. **API Connection Errors**
   - Check your API keys in `.env`
   - Verify API permissions on Binance
   - Check internet connection

2. **Insufficient Balance**
   - Ensure you have enough balance in your Binance account
   - Check the base currency (USDT) balance

3. **Strategy Not Triggering**
   - Verify strategy is enabled in `config.yaml`
   - Check if market conditions meet strategy criteria
   - Review log files for detailed information

### Getting Help

- Check the log files in `logs/` directory
- Review the configuration files
- Run tests to verify installation: `pytest tests/`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new features
4. Submit a pull request

## License

This project is for educational purposes. Use at your own risk.

## Disclaimer

This trading bot is provided for educational and research purposes only. Cryptocurrency trading involves substantial risk of loss and is not suitable for all investors. The authors are not responsible for any financial losses incurred through the use of this software. Always do your own research and consider consulting with a financial advisor before making investment decisions.
