"""
Simple test for Chart-Based Trading System
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test if all modules can be imported."""
    print("🧪 Testing Chart-Based Trading System Imports")
    print("=" * 60)
    
    try:
        from chart_analysis_engine import ChartAnalysisEngine, ChartPattern
        print("✅ Chart Analysis Engine imported")
        
        from single_position_manager import SinglePositionManager, ActivePosition
        print("✅ Single Position Manager imported")
        
        from multi_timeframe_analysis import MultiTimeframeAnalysis
        print("✅ Multi-Timeframe Analysis imported")
        
        print("\n🎉 All chart-based trading modules imported successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\n⚙️ Testing Configuration")
    print("=" * 40)
    
    try:
        from utils import load_config
        
        config = load_config('config/config.yaml')
        
        # Check chart analysis config
        chart_config = config.get('chart_analysis', {})
        print(f"📊 Chart Analysis Config:")
        print(f"   Enabled: {chart_config.get('enabled', False)}")
        print(f"   Min confidence: {chart_config.get('min_confidence_threshold', 0)}%")
        print(f"   Min risk/reward: {chart_config.get('min_risk_reward_ratio', 0)}")
        
        # Check single position config
        single_config = config.get('single_position', {})
        print(f"\n🎯 Single Position Config:")
        print(f"   Enabled: {single_config.get('enabled', False)}")
        print(f"   Max positions: {single_config.get('max_positions', 0)}")
        
        # Check scalping config
        scalping_config = config.get('scalping_fallback', {})
        print(f"\n🎯 Scalping Fallback Config:")
        print(f"   Enabled: {scalping_config.get('enabled', True)} (should be False)")
        
        if (chart_config.get('enabled') and 
            single_config.get('enabled') and 
            single_config.get('max_positions') == 1):
            print(f"\n✅ Configuration is correct for chart-based single position trading")
            return True
        else:
            print(f"\n❌ Configuration needs adjustment")
            return False
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def show_system_overview():
    """Show the chart-based trading system overview."""
    print("\n📊 Chart-Based Single Position Trading System")
    print("=" * 70)
    
    print("🎯 KEY FEATURES:")
    print("   ✅ Chart analysis priority over multi-strategy")
    print("   ✅ Single position limit (max 1 concurrent trade)")
    print("   ✅ Multi-timeframe analysis (5m, 15m, 1h)")
    print("   ✅ Price action pattern detection")
    print("   ✅ Support/resistance level validation")
    print("   ✅ Volume confirmation requirements")
    print("   ✅ Interval-based profit taking (5min, 15min, 30min)")
    print("   ✅ $100 maximum per trade maintained")
    print("   ✅ Enhanced position sizing based on chart confidence")
    print()
    
    print("🔄 TRADING FLOW:")
    print("   1. Check if active position exists")
    print("   2. If yes: Manage existing position (profit taking)")
    print("   3. If no: Scan altcoins for chart opportunities")
    print("   4. Analyze charts across multiple timeframes")
    print("   5. Detect patterns, S/R levels, volume confirmation")
    print("   6. Execute best opportunity with 70%+ confidence")
    print("   7. Monitor position until profit-taking rules trigger")
    print()
    
    print("⏰ PROFIT TAKING RULES:")
    print("   • 5+ minutes: Take any profit > $0")
    print("   • 15+ minutes: Close if loss < $2")
    print("   • 30+ minutes: Force close regardless of P&L")
    print("   • Stop loss/target price monitoring")
    print()
    
    print("🚫 DISABLED FEATURES:")
    print("   • Scalping fallback (disabled when position active)")
    print("   • Multiple concurrent positions")
    print("   • Strategy-based signal mixing")

def main():
    """Run simple chart-based trading tests."""
    print("🧪 Chart-Based Single Position Trading - Simple Test")
    print("=" * 80)
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
    
    show_system_overview()
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 CHART-BASED SINGLE POSITION TRADING SYSTEM READY!")
        print("\n🚀 Ready to run with:")
        print("   python main.py")
        print("\n📊 Expected behavior:")
        print("   • Bot will analyze charts before making any trades")
        print("   • Maximum 1 position at a time across all altcoins")
        print("   • High-quality chart-based entries only")
        print("   • Automatic profit taking at intervals")
        print("   • No scalping when position is active")
    else:
        print("⚠️  Some tests failed. Check configuration and imports.")
    
    return passed == total

if __name__ == "__main__":
    main()
