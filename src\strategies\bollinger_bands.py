"""
Bollinger Bands Strategy Implementation
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Optional
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class BollingerBandsStrategy(BaseStrategy):
    """
    Bollinger Bands Strategy: Uses volatility bands placed above and below a moving average.
    
    - BUY: When price touches the lower band (oversold condition)
    - SELL: When price touches the upper band (overbought condition)
    """
    
    def __init__(self, client, config: Dict):
        super().__init__(client, config)
        self.name = "Bollinger_Bands"
        
        # Strategy parameters
        self.window = config.get('window', 20)
        self.num_std = config.get('num_std', 2)
        self.touch_threshold = config.get('touch_threshold', 0.001)  # 0.1% threshold for "touching"
        self.min_data_points = self.window + 5
        
        logger.info(f"Bollinger Bands Strategy initialized: {self.window} period, {self.num_std} std dev")
    
    def bollinger_bands_strategy(self, data: pd.DataFrame, window: int, num_std: float) -> pd.DataFrame:
        """
        Compute Bollinger Bands signals.
        
        Args:
            data: Price data with OHLCV
            window: Moving average window
            num_std: Number of standard deviations for bands
            
        Returns:
            DataFrame with signals
        """
        # Compute rolling mean and standard deviation
        data['rolling_mean'] = data['close'].rolling(window=window).mean()
        data['rolling_std'] = data['close'].rolling(window=window).std()
        
        # Compute upper and lower bands
        data['upper_band'] = data['rolling_mean'] + (data['rolling_std'] * num_std)
        data['lower_band'] = data['rolling_mean'] - (data['rolling_std'] * num_std)
        
        # Generate buy/sell signals
        data['signal'] = 0
        data.loc[data['close'] < data['lower_band'], 'signal'] = 1   # BUY signal (oversold)
        data.loc[data['close'] > data['upper_band'], 'signal'] = -1  # SELL signal (overbought)
        
        return data
    
    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """
        Generate trading signal based on Bollinger Bands.
        
        Args:
            data: Historical price data
            symbol: Trading symbol
            
        Returns:
            'BUY', 'SELL', or 'HOLD'
        """
        try:
            if len(data) < self.min_data_points:
                logger.warning(f"[{symbol}] Insufficient data: {len(data)} < {self.min_data_points}")
                return 'HOLD'
            
            # Apply Bollinger Bands strategy
            strategy_data = self.bollinger_bands_strategy(
                data.copy(), self.window, self.num_std
            )
            
            # Get the latest data
            latest = strategy_data.iloc[-1]
            
            current_price = latest['close']
            upper_band = latest['upper_band']
            lower_band = latest['lower_band']
            middle_band = latest['rolling_mean']
            
            # Calculate band positions
            band_width = upper_band - lower_band
            price_position = (current_price - lower_band) / band_width  # 0 = lower band, 1 = upper band
            
            # Check for signals with threshold
            upper_threshold = 1 - self.touch_threshold
            lower_threshold = self.touch_threshold
            
            if price_position <= lower_threshold:
                # Price near or below lower band - BUY signal (oversold)
                logger.info(f"[{symbol}] 🚀 BUY signal - Bollinger Bands:")
                logger.info(f"[{symbol}]   Price: {current_price:.6f}")
                logger.info(f"[{symbol}]   Lower Band: {lower_band:.6f}")
                logger.info(f"[{symbol}]   Middle Band: {middle_band:.6f}")
                logger.info(f"[{symbol}]   Upper Band: {upper_band:.6f}")
                logger.info(f"[{symbol}]   ✅ Price at lower band (oversold condition)")
                return 'BUY'
            
            elif price_position >= upper_threshold:
                # Price near or above upper band - SELL signal (overbought)
                logger.info(f"[{symbol}] 📉 SELL signal - Bollinger Bands:")
                logger.info(f"[{symbol}]   Price: {current_price:.6f}")
                logger.info(f"[{symbol}]   Lower Band: {lower_band:.6f}")
                logger.info(f"[{symbol}]   Middle Band: {middle_band:.6f}")
                logger.info(f"[{symbol}]   Upper Band: {upper_band:.6f}")
                logger.info(f"[{symbol}]   ✅ Price at upper band (overbought condition)")
                return 'SELL'
            
            else:
                # Price within bands - HOLD
                logger.info(f"[{symbol}] ➡️  No Bollinger signal:")
                logger.info(f"[{symbol}]   Price: {current_price:.6f}")
                logger.info(f"[{symbol}]   Band Position: {price_position:.1%} (0%=lower, 100%=upper)")
                logger.info(f"[{symbol}]   Band Width: {band_width:.6f}")
                return 'HOLD'
        
        except Exception as e:
            logger.error(f"[{symbol}] Error in Bollinger Bands strategy: {e}")
            return 'HOLD'
    
    def get_strategy_info(self) -> Dict:
        """Get strategy information."""
        return {
            'name': self.name,
            'type': 'Mean Reversion',
            'window': self.window,
            'num_std': self.num_std,
            'touch_threshold': self.touch_threshold,
            'min_data_points': self.min_data_points,
            'description': f'Bollinger Bands {self.window}p, {self.num_std}σ'
        }
    
    def validate_signal(self, data: pd.DataFrame, signal: str) -> bool:
        """
        Validate the generated signal.
        
        Args:
            data: Price data
            signal: Generated signal
            
        Returns:
            True if signal is valid
        """
        try:
            if signal == 'HOLD':
                return True
            
            latest = data.iloc[-1]
            
            # Ensure we have valid band values
            required_fields = ['upper_band', 'lower_band', 'rolling_mean', 'rolling_std']
            for field in required_fields:
                if pd.isna(latest.get(field)):
                    logger.warning(f"Invalid {field} value: {latest.get(field)}")
                    return False
            
            # Ensure bands are properly ordered
            if latest['lower_band'] >= latest['upper_band']:
                logger.warning(f"Invalid band order: lower={latest['lower_band']}, upper={latest['upper_band']}")
                return False
            
            # Ensure standard deviation is reasonable
            if latest['rolling_std'] <= 0:
                logger.warning(f"Invalid standard deviation: {latest['rolling_std']}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating signal: {e}")
            return False

    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Bollinger Bands indicators.

        Args:
            data: Price data

        Returns:
            DataFrame with calculated indicators
        """
        # Calculate rolling mean and standard deviation
        data['rolling_mean'] = data['close'].rolling(window=self.window).mean()
        data['rolling_std'] = data['close'].rolling(window=self.window).std()

        # Calculate upper and lower bands
        data['upper_band'] = data['rolling_mean'] + (data['rolling_std'] * self.num_std)
        data['lower_band'] = data['rolling_mean'] - (data['rolling_std'] * self.num_std)

        return data

    def should_buy(self, data: pd.DataFrame) -> bool:
        """
        Check if conditions are met for a BUY signal.

        Args:
            data: Price data with indicators

        Returns:
            True if should buy
        """
        if len(data) < self.min_data_points:
            return False

        latest = data.iloc[-1]

        # Check if price is at or below lower band (oversold)
        return latest['close'] <= latest['lower_band'] * (1 + self.touch_threshold)

    def should_sell(self, data: pd.DataFrame) -> bool:
        """
        Check if conditions are met for a SELL signal.

        Args:
            data: Price data with indicators

        Returns:
            True if should sell
        """
        if len(data) < self.min_data_points:
            return False

        latest = data.iloc[-1]

        # Check if price is at or above upper band (overbought)
        return latest['close'] >= latest['upper_band'] * (1 - self.touch_threshold)
