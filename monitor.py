"""
Simple monitoring dashboard for the trading bot.
"""

import os
import sys
import json
import time
from datetime import datetime
import pandas as pd

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from utils import load_config, format_currency, format_percentage

class TradingBotMonitor:
    """Simple monitoring dashboard for the trading bot."""
    
    def __init__(self):
        """Initialize monitor."""
        self.config = load_config('config/config.yaml')
        self.client = BinanceClient()
        
    def load_portfolio_state(self) -> dict:
        """Load portfolio state from file."""
        try:
            with open('portfolio_state.json', 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
        except Exception as e:
            print(f"Error loading portfolio state: {e}")
            return {}
    
    def get_current_prices(self) -> dict:
        """Get current prices for monitored symbols."""
        symbols = self.config.get('trading', {}).get('symbols', [])
        prices = {}
        
        for symbol in symbols:
            try:
                price = self.client.get_current_price(symbol)
                prices[symbol] = price
            except Exception as e:
                print(f"Error getting price for {symbol}: {e}")
                prices[symbol] = 0.0
        
        return prices
    
    def calculate_unrealized_pnl(self, positions: dict, current_prices: dict) -> dict:
        """Calculate unrealized P&L for open positions."""
        unrealized_pnl = {}
        
        for symbol, position in positions.items():
            if symbol in current_prices:
                entry_price = position['entry_price']
                quantity = position['quantity']
                side = position['side']
                current_price = current_prices[symbol]
                
                if side == 'BUY':
                    pnl = (current_price - entry_price) * quantity
                else:
                    pnl = (entry_price - current_price) * quantity
                
                unrealized_pnl[symbol] = {
                    'pnl': pnl,
                    'pnl_percentage': (pnl / (entry_price * quantity)) * 100,
                    'current_price': current_price,
                    'entry_price': entry_price,
                    'side': side,
                    'quantity': quantity
                }
        
        return unrealized_pnl
    
    def display_dashboard(self):
        """Display the monitoring dashboard."""
        # Clear screen
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("=" * 80)
        print("CRYPTO TRADING BOT MONITOR")
        print("=" * 80)
        print(f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Load portfolio state
        portfolio_state = self.load_portfolio_state()
        
        if not portfolio_state:
            print("No portfolio state found. Bot may not be running or no trades executed.")
            return
        
        # Get current prices
        current_prices = self.get_current_prices()
        
        # Display portfolio summary
        print("PORTFOLIO SUMMARY")
        print("-" * 40)
        
        performance = portfolio_state.get('performance', {})
        print(f"Initial Balance:    {format_currency(portfolio_state.get('initial_balance', 0))}")
        print(f"Current Balance:    {format_currency(performance.get('current_balance', 0))}")
        print(f"Total P&L:          {format_currency(performance.get('total_pnl', 0))}")
        print(f"Total Return:       {format_percentage(performance.get('total_return', 0) / 100)}")
        print(f"Total Trades:       {performance.get('total_trades', 0)}")
        print(f"Win Rate:           {format_percentage(performance.get('win_rate', 0))}")
        print()
        
        # Display open positions
        positions = portfolio_state.get('positions', {})
        
        if positions:
            print("OPEN POSITIONS")
            print("-" * 80)
            print(f"{'Symbol':<12} {'Side':<6} {'Quantity':<15} {'Entry':<12} {'Current':<12} {'P&L':<15} {'P&L%':<10}")
            print("-" * 80)
            
            unrealized_pnl = self.calculate_unrealized_pnl(positions, current_prices)
            total_unrealized = 0
            
            for symbol, pnl_data in unrealized_pnl.items():
                total_unrealized += pnl_data['pnl']
                print(f"{symbol:<12} {pnl_data['side']:<6} {pnl_data['quantity']:<15.6f} "
                      f"{pnl_data['entry_price']:<12.4f} {pnl_data['current_price']:<12.4f} "
                      f"{pnl_data['pnl']:<15.4f} {pnl_data['pnl_percentage']:<10.2f}%")
            
            print("-" * 80)
            print(f"Total Unrealized P&L: {format_currency(total_unrealized)}")
        else:
            print("OPEN POSITIONS")
            print("-" * 40)
            print("No open positions")
        
        print()
        
        # Display recent trades
        trade_history = portfolio_state.get('trade_history', [])
        
        if trade_history:
            print("RECENT TRADES (Last 10)")
            print("-" * 80)
            print(f"{'Symbol':<12} {'Side':<6} {'Entry':<12} {'Exit':<12} {'P&L':<15} {'Date':<20}")
            print("-" * 80)
            
            recent_trades = trade_history[-10:]  # Last 10 trades
            for trade in recent_trades:
                exit_time = trade.get('exit_time', '')
                if isinstance(exit_time, str) and 'T' in exit_time:
                    exit_time = exit_time.split('T')[0]  # Just the date part
                
                print(f"{trade.get('symbol', ''):<12} {trade.get('side', ''):<6} "
                      f"{trade.get('entry_price', 0):<12.4f} {trade.get('exit_price', 0):<12.4f} "
                      f"{trade.get('realized_pnl', 0):<15.4f} {exit_time:<20}")
        else:
            print("RECENT TRADES")
            print("-" * 40)
            print("No trades executed yet")
        
        print()
        
        # Display current market prices
        print("CURRENT MARKET PRICES")
        print("-" * 40)
        for symbol, price in current_prices.items():
            print(f"{symbol:<12} {price:<15.4f}")
        
        print()
        print("Press Ctrl+C to exit")
    
    def run(self, refresh_interval: int = 30):
        """
        Run the monitoring dashboard.
        
        Args:
            refresh_interval: Refresh interval in seconds
        """
        try:
            while True:
                self.display_dashboard()
                time.sleep(refresh_interval)
        except KeyboardInterrupt:
            print("\nMonitoring stopped.")

def main():
    """Main function."""
    monitor = TradingBotMonitor()
    
    print("Starting Trading Bot Monitor...")
    print("This will refresh every 30 seconds.")
    print("Press Ctrl+C to exit.")
    print()
    
    monitor.run(refresh_interval=30)

if __name__ == "__main__":
    main()
