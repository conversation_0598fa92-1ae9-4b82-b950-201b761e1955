"""
Test script to verify $100 max per trade and 3 position limits.
"""

import os
import sys
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from risk_management import RiskManager
from utils import load_config

def test_100_dollar_limit():
    """Test that no trade exceeds $100."""
    print("💰 Testing $100 Maximum Trade Limit")
    print("=" * 60)
    
    try:
        # Load config
        config = load_config('config/config.yaml')
        risk_config = config.get('risk_management', {})
        
        # Initialize risk manager
        risk_manager = RiskManager(risk_config)
        
        print(f"📊 Configuration:")
        print(f"   max_position_value_usd: ${risk_manager.max_position_value_usd}")
        print(f"   max_concurrent_positions: {risk_manager.max_concurrent_positions}")
        
        # Test scenarios with different portfolio values and prices
        test_scenarios = [
            {
                'name': 'Small portfolio, low price',
                'portfolio_value': 1000,  # $1000 portfolio
                'price': 1.0,            # $1 per token
                'expected_max_value': 100
            },
            {
                'name': 'Large portfolio, low price',
                'portfolio_value': 10000,  # $10000 portfolio
                'price': 0.1,             # $0.1 per token
                'expected_max_value': 100
            },
            {
                'name': 'Large portfolio, high price',
                'portfolio_value': 50000,  # $50000 portfolio
                'price': 50000,           # $50000 per token (like BTC)
                'expected_max_value': 100
            }
        ]
        
        all_passed = True
        
        for scenario in test_scenarios:
            print(f"\n📊 {scenario['name']}:")
            
            portfolio_value = scenario['portfolio_value']
            price = scenario['price']
            
            # Calculate position size
            quantity, leverage = risk_manager.calculate_position_size(
                portfolio_value, price, symbol='TESTUSDT'
            )
            
            # Calculate actual trade value (without leverage)
            trade_value = (quantity * price) / leverage
            
            print(f"   Portfolio: ${portfolio_value:,}")
            print(f"   Price: ${price}")
            print(f"   Quantity: {quantity:.8f}")
            print(f"   Leverage: {leverage}x")
            print(f"   Trade value: ${trade_value:.2f}")
            
            if trade_value <= scenario['expected_max_value']:
                print(f"   ✅ PASS - Trade value ≤ ${scenario['expected_max_value']}")
            else:
                print(f"   ❌ FAIL - Trade value > ${scenario['expected_max_value']}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing $100 limit: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_3_position_limit():
    """Test that bot enforces maximum 3 positions."""
    print("\n🔢 Testing 3 Position Maximum Limit")
    print("=" * 60)
    
    try:
        # Import here to avoid circular imports
        sys.path.append(os.path.join(os.path.dirname(__file__)))
        from main import TradingBot
        
        # Initialize bot
        bot = TradingBot()
        
        print(f"📊 Position Management:")
        print(f"   max_positions: {bot.max_positions}")
        print(f"   current_positions: {len(bot.active_positions)}")
        
        # Test adding positions up to limit
        test_symbols = ['DOGEUSDT', 'ADAUSDT', 'SOLUSDT', 'BTCUSDT', 'ETHUSDT']
        
        positions_added = 0
        
        for i, symbol in enumerate(test_symbols):
            print(f"\n📊 Attempting to add position {i+1}: {symbol}")
            
            # Check if we can add more positions
            current_count = len(bot.active_positions)
            
            if current_count >= bot.max_positions:
                print(f"   🚫 Position limit reached ({current_count}/{bot.max_positions})")
                print(f"   ✅ CORRECT - Bot should reject {symbol}")
                break
            else:
                print(f"   ✅ Can add position ({current_count}/{bot.max_positions})")
                
                # Simulate adding position
                bot.active_positions[symbol] = {
                    'side': 'BUY',
                    'quantity': 100.0,
                    'entry_price': 1.0,
                    'leverage': 3,
                    'entry_time': pd.Timestamp.now(),
                    'reason': 'Test position'
                }
                bot.position_profits[symbol] = 0.0
                positions_added += 1
                
                print(f"   📊 Added test position for {symbol}")
        
        print(f"\n📊 Final Results:")
        print(f"   Positions added: {positions_added}")
        print(f"   Active positions: {len(bot.active_positions)}/{bot.max_positions}")
        print(f"   Position symbols: {list(bot.active_positions.keys())}")
        
        # Verify limit enforcement
        if len(bot.active_positions) <= bot.max_positions:
            print(f"   ✅ PASS - Position limit enforced correctly")
            return True
        else:
            print(f"   ❌ FAIL - Position limit exceeded")
            return False
        
    except Exception as e:
        print(f"❌ Error testing 3 position limit: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_combined_limits():
    """Test both limits working together."""
    print("\n🔒 Testing Combined Limits ($100 + 3 Positions)")
    print("=" * 60)
    
    try:
        config = load_config('config/config.yaml')
        risk_config = config.get('risk_management', {})
        
        print("📋 STRICT TRADING LIMITS:")
        print(f"   💰 Maximum trade value: ${risk_config.get('max_position_value_usd', 100)}")
        print(f"   🔢 Maximum positions: {risk_config.get('max_concurrent_positions', 3)}")
        print(f"   ⚖️  Maximum leverage: {risk_config.get('max_leverage', 5)}x")
        
        # Calculate maximum total exposure
        max_trade_value = risk_config.get('max_position_value_usd', 100)
        max_positions = risk_config.get('max_concurrent_positions', 3)
        max_leverage = risk_config.get('max_leverage', 5)
        
        max_total_exposure = max_trade_value * max_positions * max_leverage
        
        print(f"\n📊 Maximum Possible Exposure:")
        print(f"   Per trade: ${max_trade_value} × {max_leverage}x leverage = ${max_trade_value * max_leverage} exposure")
        print(f"   Total (3 positions): ${max_trade_value} × {max_positions} × {max_leverage}x = ${max_total_exposure} max exposure")
        print(f"   Actual capital at risk: ${max_trade_value * max_positions} (without leverage)")
        
        # Safety assessment
        if max_total_exposure <= 2000:  # $2000 max exposure seems reasonable
            print(f"   ✅ SAFE - Total exposure ≤ $2000")
        else:
            print(f"   ⚠️  HIGH - Total exposure > $2000")
        
        if max_trade_value * max_positions <= 500:  # $500 max capital at risk
            print(f"   ✅ CONSERVATIVE - Capital at risk ≤ $500")
        else:
            print(f"   ⚠️  AGGRESSIVE - Capital at risk > $500")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing combined limits: {e}")
        return False

def main():
    """Run all position limit tests."""
    print("🧪 Position Limits Test Suite")
    print("=" * 80)
    
    print("🎯 Testing strict trading limits:")
    print("   • Maximum $100 per trade")
    print("   • Maximum 3 positions at once")
    print("   • Combined risk assessment")
    
    tests = [
        ("$100 Trade Limit", test_100_dollar_limit),
        ("3 Position Limit", test_3_position_limit),
        ("Combined Limits", test_combined_limits)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 ALL POSITION LIMITS WORKING CORRECTLY!")
        print("\n✅ Strict Limits Enforced:")
        print("   ✅ Maximum $100 per trade (hard limit)")
        print("   ✅ Maximum 3 positions at once (hard limit)")
        print("   ✅ Risk manager integration")
        print("   ✅ Position value validation")
        print("   ✅ Safe total exposure limits")
        
        print("\n🛡️ Risk Protection:")
        print("   • Maximum $300 capital at risk (3 × $100)")
        print("   • Maximum $1500 total exposure (3 × $100 × 5x)")
        print("   • Conservative position sizing")
        print("   • Automatic limit enforcement")
        
        print("\n🚀 Ready for safe trading with strict limits!")
    else:
        print("⚠️  Some limit tests failed. Fix before trading.")
    
    return passed == total

if __name__ == "__main__":
    main()
