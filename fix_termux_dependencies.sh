#!/bin/bash
# Fix Termux Dependencies for Trading Bot

echo "🔧 FIXING TERMUX DEPENDENCIES"
echo "=============================="

# Update packages first
echo "📦 Updating Termux packages..."
pkg update -y && pkg upgrade -y

# Install Python dependencies
echo "🐍 Installing Python packages..."
python -m pip install --upgrade pip

# Core dependencies
python -m pip install python-dotenv
python -m pip install pandas
python -m pip install numpy
python -m pip install requests
python -m pip install python-binance
python -m pip install pyyaml
python -m pip install ta-lib || echo "⚠️  TA-Lib failed (optional)"

# Fix pandas deprecation warnings
echo "📊 Fixing pandas compatibility..."
python -c "import pandas as pd; print(f'Pandas version: {pd.__version__}')"

# Test imports
echo "🧪 Testing imports..."
python -c "
try:
    import dotenv
    print('✅ python-dotenv: OK')
except ImportError as e:
    print(f'❌ python-dotenv: {e}')

try:
    import pandas as pd
    print('✅ pandas: OK')
except ImportError as e:
    print(f'❌ pandas: {e}')

try:
    import numpy as np
    print('✅ numpy: OK')
except ImportError as e:
    print(f'❌ numpy: {e}')

try:
    from binance.client import Client
    print('✅ python-binance: OK')
except ImportError as e:
    print(f'❌ python-binance: {e}')
"

echo "✅ Dependency fix complete!"
echo "Now run: python test_bot.py"
