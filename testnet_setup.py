"""
Binance Futures Testnet Setup Helper.
Helps with setting up and testing the Binance Futures Testnet connection.
"""

import os
import sys
import logging
from dotenv import load_dotenv
import pandas as pd
import time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from utils import setup_logging, load_config

def test_testnet_connection():
    """Test connection to Binance Futures Testnet."""
    print("\n🧪 Testing Binance Futures Testnet Connection")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    # Get API keys
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    
    if not api_key or not secret_key:
        print("❌ API keys not found in environment variables")
        print("Please set BINANCE_API_KEY and BINANCE_SECRET_KEY in your .env file")
        return False
    
    print("✅ API keys found in environment variables")
    
    # Initialize client
    try:
        client = BinanceClient(testnet=True)
    except Exception as e:
        print(f"❌ Failed to initialize Binance client: {e}")
        return False
    
    # Test connection
    print("\n🔄 Testing API connection...")
    try:
        # Try a simple ping first
        client.client.ping()
        print("✅ API ping successful")
        
        # Get server time
        server_time = client.client.get_server_time()
        local_time = int(time.time() * 1000)
        time_diff = abs(server_time['serverTime'] - local_time)
        
        print(f"✅ Server time: {pd.to_datetime(server_time['serverTime'], unit='ms')}")
        print(f"   Time difference: {time_diff} ms")
        
        if time_diff > 1000:
            print("⚠️  Warning: Time difference is greater than 1 second")
            print("   This might cause timestamp errors")
        
        # Get account info
        print("\n🔄 Getting account info...")
        account = client.futures_get_account_info()
        
        if not account:
            print("❌ Failed to get account info")
            return False
        
        print("✅ Account info retrieved successfully")
        
        # Check balance
        assets = account.get('assets', [])
        usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
        
        if usdt_asset:
            wallet_balance = float(usdt_asset.get('walletBalance', 0))
            available_balance = float(usdt_asset.get('availableBalance', 0))
            
            print(f"\n💰 Testnet USDT Balance:")
            print(f"   Wallet Balance: {wallet_balance:.2f} USDT")
            print(f"   Available Balance: {available_balance:.2f} USDT")
            
            if wallet_balance < 100:
                print("\n⚠️  Your testnet balance is low")
                print("   You may need to get more testnet funds")
                print("   Visit https://testnet.binancefuture.com and click 'Get Assets'")
        else:
            print("❌ No USDT asset found in account")
        
        # Check positions
        print("\n🔄 Checking positions...")
        positions = client.futures_get_position_info()
        
        if positions is None:
            print("❌ Failed to get position info")
        else:
            print(f"✅ Position info retrieved: {len(positions)} symbols")
            
            # Check for any open positions
            open_positions = [pos for pos in positions if float(pos.get('positionAmt', 0)) != 0]
            
            if open_positions:
                print(f"⚠️  Found {len(open_positions)} open positions:")
                for pos in open_positions:
                    symbol = pos.get('symbol', 'Unknown')
                    size = pos.get('positionAmt', '0')
                    entry_price = pos.get('entryPrice', '0')
                    leverage = pos.get('leverage', '0')
                    pnl = pos.get('unRealizedProfit', '0')
                    print(f"   {symbol}: {size} @ {entry_price} (Leverage: {leverage}x, PnL: {pnl})")
            else:
                print("✅ No open positions found")
        
        # Test market data
        print("\n🔄 Testing market data...")
        data = client.get_historical_klines('BTCUSDT', '5m', 10)
        
        if data.empty:
            print("❌ Failed to get market data")
        else:
            print(f"✅ Market data retrieved: {len(data)} candles")
            print(f"   Latest BTC price: {data['close'].iloc[-1]:.2f} USDT")
        
        # Test leverage setting
        print("\n🔄 Testing leverage setting...")
        try:
            result = client.futures_change_leverage('BTCUSDT', 2)

            if not result:
                print("❌ Failed to set leverage")
            else:
                print("✅ Leverage setting successful")
                print(f"   Set leverage to 2x for BTCUSDT")
        except Exception as e:
            print(f"⚠️  Leverage setting test: {e}")
            print("   This is normal if you don't have any positions or funds")
        
        print("\n✅ Testnet connection test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error testing testnet connection: {e}")
        return False

def place_test_order():
    """Place a test order on Binance Futures Testnet."""
    print("\n🧪 Placing Test Order on Binance Futures Testnet")
    print("=" * 60)

    # Load environment variables
    load_dotenv()

    # Initialize client
    try:
        client = BinanceClient(testnet=True)
    except Exception as e:
        print(f"❌ Failed to initialize client: {e}")
        return False

    # First check if we have funds
    try:
        account = client.futures_get_account_info()
        if not account:
            print("❌ Cannot get account info. Please check your API keys.")
            return False

        # Check USDT balance
        assets = account.get('assets', [])
        usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)

        if not usdt_asset:
            print("❌ No USDT found in account")
            print("💡 You need to get testnet funds first (Menu option 3)")
            return False

        available_balance = float(usdt_asset.get('availableBalance', 0))

        if available_balance < 10:
            print(f"❌ Insufficient balance: {available_balance:.2f} USDT")
            print("💡 You need at least 10 USDT to place test orders")
            print("💡 Get more testnet funds (Menu option 3)")
            return False

        print(f"✅ Available balance: {available_balance:.2f} USDT")

    except Exception as e:
        print(f"❌ Error checking account balance: {e}")
        return False

    # Get current price
    try:
        # Use BTCUSDT for testing
        symbol = 'BTCUSDT'

        # Get current price
        data = client.get_historical_klines(symbol, '1m', 1)
        if data.empty:
            print(f"❌ Failed to get price for {symbol}")
            return False

        price = data['close'].iloc[-1]
        print(f"Current {symbol} price: {price:.2f} USDT")

        # Set leverage
        try:
            client.futures_change_leverage(symbol, 2)
            print(f"✅ Set leverage to 2x for {symbol}")
        except Exception as e:
            print(f"⚠️  Leverage setting: {e}")
            print("   Continuing with default leverage...")

        # Calculate quantity (small test amount based on available balance)
        # Use 1% of available balance for test
        test_amount = min(available_balance * 0.01, 20)  # Max $20 test
        quantity = round(test_amount / price, 6)  # Round to 6 decimal places

        if quantity < 0.001:
            quantity = 0.001  # Minimum quantity

        # Ask for confirmation
        print(f"\nReady to place test order:")
        print(f"Symbol: {symbol}")
        print(f"Side: BUY")
        print(f"Quantity: {quantity} BTC")
        print(f"Estimated cost: {quantity * price:.2f} USDT")
        print(f"Leverage: 2x")

        confirm = input("\nPlace this test order? (y/N): ").lower()
        if confirm != 'y':
            print("Test order cancelled")
            return False

        # Place order
        print("\nPlacing test order...")
        order = client.futures_place_market_order(symbol, 'BUY', quantity)

        if not order:
            print("❌ Failed to place test order")
            return False

        print("✅ Test order placed successfully!")

        # Extract order details
        order_id = order.get('orderId', 'Unknown')
        executed_qty = float(order.get('executedQty', 0))
        avg_price = float(order.get('avgPrice', 0)) if order.get('avgPrice') else price

        print(f"   Order ID: {order_id}")
        print(f"   Executed Quantity: {executed_qty} BTC")
        print(f"   Average Price: {avg_price:.2f} USDT")
        print(f"   Total Cost: {executed_qty * avg_price:.2f} USDT")

        # Wait a moment
        print("\nWaiting 3 seconds for position update...")
        time.sleep(3)

        # Check position
        try:
            positions = client.futures_get_position_info(symbol)
            if positions:
                position = positions[0]
                position_amt = float(position.get('positionAmt', 0))
                entry_price = float(position.get('entryPrice', 0))
                unrealized_pnl = float(position.get('unRealizedProfit', 0))

                if abs(position_amt) > 0:
                    print(f"✅ Position opened:")
                    print(f"   Size: {position_amt} BTC")
                    print(f"   Entry Price: {entry_price:.2f} USDT")
                    print(f"   Unrealized PnL: {unrealized_pnl:.4f} USDT")

                    # Ask to close position
                    close = input("\nClose this test position? (Y/n): ").lower()
                    if close != 'n':
                        print("\nClosing test position...")
                        result = client.futures_close_position(symbol)

                        if result:
                            print("✅ Test position closed successfully!")

                            # Show final PnL
                            final_pnl = float(result.get('realizedPnl', 0))
                            print(f"   Realized PnL: {final_pnl:.4f} USDT")
                        else:
                            print("❌ Failed to close test position")
                            print("   You can close it manually on the testnet website")
                else:
                    print("❌ No position found after order placement")
            else:
                print("❌ Failed to get position information")
        except Exception as e:
            print(f"⚠️  Error checking position: {e}")

        return True

    except Exception as e:
        print(f"❌ Error placing test order: {e}")
        print(f"   Details: {str(e)}")
        return False

def get_testnet_funds():
    """Guide user to get testnet funds."""
    print("\n💰 Getting Testnet Funds")
    print("=" * 60)
    print("To get free testnet funds, follow these steps:")
    print("1. Open your web browser")
    print("2. Go to https://testnet.binancefuture.com")
    print("3. Log in with your testnet account")
    print("4. Click on 'Assets' in the top menu")
    print("5. Click on 'Get Assets' button")
    print("6. You should receive free USDT for testing")
    print("\nAfter getting funds, you need to transfer them to your Futures account:")
    print("1. Go to 'Assets' > 'Futures Account Transfer'")
    print("2. Transfer USDT from Spot to Futures wallet")

    # Ask if user wants to open the testnet site
    open_site = input("\nWould you like to open the Binance Futures Testnet website now? (Y/n): ").lower()
    if open_site != 'n':
        import webbrowser
        webbrowser.open('https://testnet.binancefuture.com')
        print("✅ Opened Binance Futures Testnet in your browser")

    print("\n⚠️  Important: After getting funds, run the connection test again")
    print("to verify your account has the testnet funds available.")

    return True

def show_testnet_instructions():
    """Show detailed testnet instructions."""
    print("\n📚 Binance Futures Testnet Instructions")
    print("=" * 60)
    print("The Binance Futures Testnet is a sandbox environment where you can")
    print("practice trading without using real money.")

    print("\n🔑 Setting Up Testnet Account:")
    print("1. Visit https://testnet.binancefuture.com")
    print("2. Register a new account (separate from your real Binance account)")
    print("3. Generate API keys from the API Management section")
    print("4. Add these keys to your .env file")

    print("\n💰 Getting Testnet Funds:")
    print("1. Log in to your testnet account")
    print("2. Go to Assets > Get Assets")
    print("3. Transfer funds from Spot to Futures wallet")

    print("\n🤖 Configuring the Bot for Testnet:")
    print("1. Ensure trading.mode is set to 'testnet' in config.yaml")
    print("2. Use your testnet API keys in the .env file")
    print("3. Run test_live_setup.py to verify connection")
    print("4. Start the bot with python main.py")

    print("\n⚠️  Testnet Limitations:")
    print("1. Testnet may have less liquidity than the real market")
    print("2. Some features might be limited or behave differently")
    print("3. Testnet data might occasionally be reset")

    print("\n🔄 Switching Between Testnet and Live:")
    print("1. Change trading.mode in config.yaml")
    print("2. Update API keys in .env file")
    print("3. Always test thoroughly before going live")

    return True

def main():
    """Main function."""
    print("🚀 Binance Futures Testnet Setup Helper")
    print("=" * 60)
    print("This script helps you test your Binance Futures Testnet connection")
    print("and place test orders to verify everything is working correctly.")
    print("\nMake sure you have:")
    print("1. Created a Binance Futures Testnet account")
    print("2. Generated API keys from the testnet site")
    print("3. Added these keys to your .env file")
    print("4. Set trading.mode to 'testnet' in config.yaml")
    print("=" * 60)

    # Check config
    config = load_config('config/config.yaml')
    if config:
        trading_mode = config.get('trading', {}).get('mode', 'unknown')
        if trading_mode != 'testnet':
            print(f"⚠️  Warning: Trading mode is set to '{trading_mode}', not 'testnet'")
            print("You should set trading.mode to 'testnet' in config/config.yaml")

            change = input("Change to testnet mode now? (Y/n): ").lower()
            if change != 'n':
                config['trading']['mode'] = 'testnet'
                with open('config/config.yaml', 'w') as f:
                    import yaml
                    yaml.dump(config, f)
                print("✅ Config updated to testnet mode")

    # Menu
    while True:
        print("\n📋 Menu:")
        print("1. Test Testnet Connection")
        print("2. Place Test Order")
        print("3. Get Testnet Funds")
        print("4. Show Testnet Instructions")
        print("5. Start Trading Bot")
        print("6. Exit")

        choice = input("\nEnter your choice (1-6): ")

        if choice == '1':
            test_testnet_connection()
        elif choice == '2':
            place_test_order()
        elif choice == '3':
            get_testnet_funds()
        elif choice == '4':
            show_testnet_instructions()
        elif choice == '5':
            print("\n🚀 Starting Trading Bot...")
            try:
                import subprocess
                subprocess.run(["python", "main.py"])
            except Exception as e:
                print(f"❌ Error starting bot: {e}")
        elif choice == '6':
            print("\nExiting Testnet Setup Helper. Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")

        input("\nPress Enter to continue...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nSetup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
