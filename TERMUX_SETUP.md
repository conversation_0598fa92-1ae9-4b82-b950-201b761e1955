# 📱 Termux Mobile Trading Bot Setup Guide

Run your advanced trading bot directly on Android using Termux!

## 🚀 Quick Start

### 1. Install Termux
- **Recommended**: Download from [F-Droid](https://f-droid.org/packages/com.termux/)
- **Alternative**: Google Play Store (may have limitations)

### 2. One-Command Installation
```bash
# Clone repository and run installer
git clone https://github.com/Mrteesoft/trading-bot.git
cd trading-bot
chmod +x install_termux.sh
./install_termux.sh
```

### 3. Configure API Keys
```bash
# Edit .env file with your Binance API keys
nano .env
```

Add your configuration:
```env
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
TESTNET_MODE=true
```

### 4. Start Trading
```bash
# Run the mobile-optimized bot
python run_termux.py

# Or use the alias
trading-bot
```

## 📱 Mobile Optimizations

### Performance Features
- ✅ **Reduced Resource Usage**: Optimized for mobile CPU/RAM
- ✅ **Battery Efficient**: Longer intervals, reduced API calls
- ✅ **Data Saving**: Caching and batch operations
- ✅ **Text Dashboard**: Mobile-friendly text interface

### Trading Adjustments
- ✅ **Smaller Positions**: $50 max per trade (mobile-safe)
- ✅ **Single Position**: Maximum 1 concurrent trade
- ✅ **Shorter Hold Times**: Max 1 hour per position
- ✅ **Limited Altcoins**: 8 symbols for better performance

### Mobile-Specific Settings
```yaml
# Termux Configuration Highlights
trading:
  max_position_value_usd: 50    # Reduced for mobile
  trading_interval_seconds: 600 # 10 minutes
  
dashboard:
  type: text                    # Text-only for mobile
  update_interval: 60           # 1 minute updates
  
risk_management:
  max_hold_minutes: 60          # 1 hour maximum
  profit_check_interval: 10     # Check every 10 minutes
```

## 🔧 Installation Details

### Manual Installation Steps
```bash
# Update Termux
pkg update && pkg upgrade -y

# Install dependencies
pkg install python python-pip git curl wget -y
pkg install build-essential clang cmake -y
pkg install libffi openssl zlib -y

# Install Python packages
pip install numpy pandas requests pyyaml
pip install python-binance websocket-client
pip install ta-lib  # Technical analysis
```

### Required Packages
- **Core**: `python`, `python-pip`, `git`
- **Build Tools**: `build-essential`, `clang`, `cmake`
- **Libraries**: `libffi`, `openssl`, `zlib`
- **Python**: `numpy`, `pandas`, `requests`, `pyyaml`, `python-binance`

## 🛡️ Keeping Bot Running

### Prevent Android from Killing Termux
1. Open Termux settings (swipe from left)
2. Enable "Acquire Wakelock"
3. Keep Termux app in recent apps

### Background Execution
```bash
# Run in background
nohup python run_termux.py > trading.log 2>&1 &

# Check if running
ps | grep python

# View logs
tail -f trading.log
```

### Auto-Start on Boot (Optional)
```bash
# Install Termux:Boot from F-Droid
# Create boot script
mkdir -p ~/.termux/boot/
echo '#!/data/data/com.termux/files/usr/bin/sh
cd ~/trading-bot && python run_termux.py' > ~/.termux/boot/start-trading.sh
chmod +x ~/.termux/boot/start-trading.sh
```

## 📊 Mobile Dashboard

### Text Dashboard Features
```
🚀 REAL-TIME TRADING DASHBOARD
================================================================================
📊 TRADING SUMMARY
----------------------------------------
Active Symbols:               8
With Signals:                 2
Total Trades:                 1
Recent Errors:                0

📈 SYMBOL OVERVIEW (Top 5)
------------------------------------------------------------
Symbol       Signal Conf%  Price        Status
------------------------------------------------------------
ADAUSDT      🟢BUY   86.8   $0.770000    ✅ OK
SOLUSDT      🟢BUY   72.1   $169.063333  ✅ OK
XRPUSDT      ⚪HOLD  65.2   $3.005267    ✅ OK

💰 RECENT TRADES
--------------------------------------------------
08:33:26 | 🔴SELL ADAUSDT @ $0.770000
         Qty: 542.73 | Reason: RSI signal
```

## 🚨 Troubleshooting

### Common Issues

#### Package Installation Fails
```bash
# Update repositories
pkg update && pkg upgrade -y

# Install packages individually
pip install --upgrade pip
pip install numpy
pip install pandas
```

#### Permission Denied
```bash
# Fix permissions
chmod +x *.py *.sh
```

#### Memory Issues
```bash
# Check memory
free -m

# Close other Android apps
# Restart Termux if needed
```

#### Network Connectivity
```bash
# Test connection
ping -c 4 api.binance.com

# Try mobile data instead of WiFi
```

#### Bot Crashes
```bash
# Check logs
cat logs/termux_trading.log

# Run with debug output
python -u run_termux.py
```

## 📱 Mobile Trading Tips

### Best Practices
- ✅ **Use Testnet First**: Always test with testnet mode
- ✅ **Monitor Battery**: Keep phone charged during trading
- ✅ **Stable Connection**: Use reliable WiFi or mobile data
- ✅ **Regular Checks**: Monitor bot status periodically
- ✅ **Small Positions**: Start with small position sizes

### Safety Recommendations
- 🛡️ **Testnet Mode**: Keep `TESTNET_MODE=true` for testing
- 🛡️ **Position Limits**: Use $50 max per trade on mobile
- 🛡️ **Time Limits**: 1-hour maximum hold time
- 🛡️ **Stop Losses**: Always use stop loss protection
- 🛡️ **Regular Monitoring**: Check bot status frequently

## 🔗 Quick Commands

```bash
# Start bot
python run_termux.py

# Stop bot
pkill -f run_termux.py

# Check status
ps | grep python

# View logs
tail -f logs/termux_trading.log

# Update bot
git pull origin master

# Restart bot
pkill -f run_termux.py && python run_termux.py
```

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review logs in `logs/termux_trading.log`
3. Ensure all dependencies are installed
4. Verify API keys are correctly configured
5. Test network connectivity

---

**Your advanced trading bot is now mobile-ready! Trade from anywhere with your Android device.** 📱🚀📊
