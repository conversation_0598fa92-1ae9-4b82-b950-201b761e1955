#!/usr/bin/env python3
"""
Test Market Validation Fix
Quick test to verify the ticker method fix works.
"""

import sys
import os
sys.path.append('src')

def test_market_validation():
    """Test the market validation fix."""
    print("🔧 TESTING MARKET VALIDATION FIX")
    print("=" * 40)
    
    try:
        from binance_client import BinanceClient
        
        # Initialize client
        print("Initializing Binance client...")
        client = BinanceClient(testnet=True)
        print("✅ BinanceClient initialized")
        
        # Test symbols that were failing
        test_symbols = ['XLMUSDT', 'TRXUSDT', 'XRPUSDT']
        
        for symbol in test_symbols:
            print(f"\n🔍 Testing market validation for {symbol}:")
            
            try:
                result = client.validate_market_conditions(symbol)
                if result:
                    print(f"   ✅ Market validation PASSED for {symbol}")
                else:
                    print(f"   ⚠️  Market validation FAILED for {symbol} (but trade should still proceed)")
                    
            except Exception as e:
                print(f"   ❌ Market validation ERROR: {e}")
                print(f"   💡 This should be handled gracefully now")
        
        print(f"\n🎯 TESTING TICKER METHODS:")
        
        # Test different ticker methods
        symbol = 'XLMUSDT'
        client_obj = client.client
        
        methods_to_test = [
            'futures_24hr_ticker',
            'get_24hr_ticker', 
            'get_ticker',
            'futures_ticker'
        ]
        
        working_method = None
        for method_name in methods_to_test:
            try:
                method = getattr(client_obj, method_name, None)
                if method:
                    result = method(symbol=symbol)
                    if result:
                        print(f"   ✅ {method_name}: WORKS")
                        working_method = method_name
                        break
                    else:
                        print(f"   ⚠️  {method_name}: Returns empty")
                else:
                    print(f"   ❌ {method_name}: Method not found")
            except Exception as e:
                print(f"   ❌ {method_name}: Error - {e}")
        
        if working_method:
            print(f"\n🎉 WORKING METHOD FOUND: {working_method}")
            print(f"Market validation should work now!")
        else:
            print(f"\n⚠️  NO WORKING TICKER METHOD FOUND")
            print(f"But trades should still proceed (validation bypassed)")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run the test."""
    print("🤖 MARKET VALIDATION FIX TEST")
    print("Testing the fix for 'get_24hr_ticker' error")
    print("=" * 50)
    
    success = test_market_validation()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ TEST COMPLETED!")
        print("🚀 Your trading bot should now execute trades")
        print("💡 Restart the bot: python main.py")
    else:
        print("❌ TEST FAILED")
        print("There might still be issues with market validation")
    
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test crashed: {e}")
