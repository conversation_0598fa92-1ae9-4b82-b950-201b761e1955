#!/usr/bin/env python3
"""
Mobile Network Connectivity Test for Android/Termux
Tests network connectivity and provides mobile-specific fixes.
"""

import requests
import socket
import subprocess
import sys
import time
from urllib3.exceptions import InsecureRequestWarning
import urllib3

# Disable SSL warnings for testing
urllib3.disable_warnings(InsecureRequestWarning)

def test_basic_connectivity():
    """Test basic internet connectivity."""
    print("🔍 Testing basic internet connectivity...")
    
    try:
        # Test Google DNS
        response = requests.get("http://*******", timeout=5)
        print("✅ Basic internet connectivity: OK")
        return True
    except Exception as e:
        print(f"❌ Basic internet connectivity: FAILED - {e}")
        return False

def test_dns_resolution():
    """Test DNS resolution."""
    print("\n🔍 Testing DNS resolution...")
    
    test_domains = [
        "google.com",
        "api.binance.com",
        "testnet.binancefuture.com"
    ]
    
    for domain in test_domains:
        try:
            ip = socket.gethostbyname(domain)
            print(f"✅ {domain} → {ip}")
        except Exception as e:
            print(f"❌ {domain} → FAILED: {e}")
            return False
    
    return True

def test_binance_api():
    """Test Binance API connectivity (prioritizing testnet for trading bot)."""
    print("\n🔍 Testing Binance API connectivity...")

    # Test testnet endpoints first (what the trading bot actually uses)
    endpoints = [
        ("Testnet Futures Ping", "https://testnet.binancefuture.com/fapi/v1/ping"),
        ("Testnet Futures Time", "https://testnet.binancefuture.com/fapi/v1/time"),
        ("Testnet Spot Ping", "https://testnet.binance.vision/api/v3/ping"),
        ("Mainnet Ping (backup)", "https://api.binance.com/api/v3/ping"),
    ]
    
    for name, url in endpoints:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {name}: OK ({response.status_code})")
            else:
                print(f"⚠️  {name}: HTTP {response.status_code}")
        except requests.exceptions.ConnectionError as e:
            if "Connection refused" in str(e) or "Errno 111" in str(e):
                print(f"❌ {name}: Connection Refused (Common on mobile)")
                return False
            else:
                print(f"❌ {name}: Connection Error - {e}")
                return False
        except requests.exceptions.Timeout:
            print(f"❌ {name}: Timeout (slow connection)")
            return False
        except Exception as e:
            print(f"❌ {name}: {e}")
            return False
    
    return True

def test_mobile_specific():
    """Test mobile-specific network issues."""
    print("\n🔍 Testing mobile-specific network conditions...")
    
    # Test if running on Termux
    try:
        result = subprocess.run(['uname', '-o'], capture_output=True, text=True)
        if 'Android' in result.stdout:
            print("✅ Detected Android/Termux environment")
            
            # Test Termux-specific commands
            try:
                subprocess.run(['termux-info'], capture_output=True, text=True, timeout=5)
                print("✅ Termux commands available")
            except:
                print("⚠️  Termux commands not available")
                
        else:
            print("ℹ️  Not running on Android/Termux")
            
    except Exception as e:
        print(f"⚠️  Could not detect environment: {e}")

def run_network_diagnostics():
    """Run comprehensive network diagnostics."""
    print("=" * 60)
    print("🔧 MOBILE NETWORK DIAGNOSTICS FOR TRADING BOT")
    print("=" * 60)
    
    # Test basic connectivity
    basic_ok = test_basic_connectivity()
    
    # Test DNS
    dns_ok = test_dns_resolution()
    
    # Test mobile-specific
    test_mobile_specific()
    
    # Test Binance API
    binance_ok = test_binance_api()
    
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC RESULTS")
    print("=" * 60)
    
    if basic_ok and dns_ok and binance_ok:
        print("✅ ALL TESTS PASSED - Network connectivity is good!")
        print("🚀 Your trading bot should work fine.")
        return True
    else:
        print("❌ SOME TESTS FAILED - Network issues detected")
        print("\n🔧 ANDROID/TERMUX FIXES TO TRY:")
        
        if not basic_ok:
            print("\n📶 BASIC CONNECTIVITY ISSUES:")
            print("   1. Check if you have internet access")
            print("   2. Try switching from WiFi to Mobile Data")
            print("   3. Move to area with better signal")
            print("   4. Restart your network connection")
        
        if not dns_ok:
            print("\n🌐 DNS RESOLUTION ISSUES:")
            print("   1. Change DNS servers:")
            print("      • Settings → WiFi → Advanced → DNS")
            print("      • Use: *******, 1.1.1.1")
            print("   2. Clear DNS cache:")
            print("      • Run: nslookup api.binance.com")
        
        if not binance_ok:
            print("\n🏦 BINANCE API CONNECTION ISSUES:")
            print("   1. Your carrier might block crypto APIs")
            print("   2. Try using a VPN:")
            print("      • pkg install openvpn")
            print("      • Use free VPN like ProtonVPN")
            print("   3. Switch networks:")
            print("      • Try different WiFi network")
            print("      • Use mobile hotspot from another device")
            print("   4. Update Termux packages:")
            print("      • pkg update && pkg upgrade")
            print("   5. Install required packages:")
            print("      • pkg install curl python")
            print("   6. Test manually:")
            print("      • curl -I https://api.binance.com")
        
        print("\n🔄 TERMUX-SPECIFIC FIXES:")
        print("   1. Restart Termux app completely")
        print("   2. Run: termux-wake-lock (prevent sleep)")
        print("   3. Run: termux-change-repo (change mirror)")
        print("   4. Check storage permissions:")
        print("      • termux-setup-storage")
        
        return False

def main():
    """Main function."""
    try:
        success = run_network_diagnostics()
        
        if success:
            print("\n🎉 Network is ready for trading bot!")
            print("💡 You can now run: python main.py")
        else:
            print("\n⚠️  Please fix network issues before running trading bot")
            print("💡 Try the suggested fixes above")
            
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during network test: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
