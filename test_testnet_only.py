"""
Test only testnet connection without checking main Binance API.
"""

import os
import sys
import time
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_testnet_direct():
    """Test testnet connection directly."""
    print("🧪 Testing Binance Futures Testnet Connection")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check if API keys are loaded
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    
    print(f"API Key loaded: {'✅' if api_key else '❌'}")
    print(f"Secret Key loaded: {'✅' if secret_key else '❌'}")
    
    if not api_key or not secret_key:
        print("\n❌ API keys not found in .env file")
        return False
    
    print(f"API Key length: {len(api_key)}")
    print(f"Secret Key length: {len(secret_key)}")
    
    # Test with python-binance directly
    try:
        from binance.client import Client
        
        print("\n🔄 Creating testnet client...")
        client = Client(
            api_key=api_key,
            api_secret=secret_key,
            testnet=True
        )
        
        print("✅ Client created successfully")
        
        # Test ping
        print("\n🔄 Testing ping...")
        client.ping()
        print("✅ Ping successful")
        
        # Get server time
        print("\n🔄 Getting server time...")
        server_time = client.get_server_time()
        local_time = int(time.time() * 1000)
        time_diff = abs(server_time['serverTime'] - local_time)
        
        print(f"✅ Server time retrieved")
        print(f"Time difference: {time_diff} ms")
        
        if time_diff > 5000:
            print("⚠️  Large time difference detected")
        
        # Test futures account
        print("\n🔄 Getting futures account info...")
        account = client.futures_account()
        
        if account:
            print("✅ Futures account info retrieved")
            
            # Check balance
            assets = account.get('assets', [])
            usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
            
            if usdt_asset:
                balance = float(usdt_asset.get('availableBalance', 0))
                print(f"💰 Available Balance: {balance:.2f} USDT")
                
                if balance < 10:
                    print("⚠️  Low balance - you may need more testnet funds")
                else:
                    print("✅ Sufficient balance for testing")
            else:
                print("❌ No USDT asset found")
        else:
            print("❌ Failed to get account info")
            return False
        
        # Test position info
        print("\n🔄 Getting position info...")
        positions = client.futures_position_information()
        
        if positions:
            print(f"✅ Position info retrieved: {len(positions)} symbols")
            
            # Check for open positions
            open_positions = [pos for pos in positions if float(pos.get('positionAmt', 0)) != 0]
            
            if open_positions:
                print(f"📊 Open positions: {len(open_positions)}")
                for pos in open_positions[:3]:
                    symbol = pos.get('symbol', 'Unknown')
                    size = pos.get('positionAmt', '0')
                    pnl = pos.get('unRealizedProfit', '0')
                    print(f"   {symbol}: {size} (PnL: {pnl})")
            else:
                print("📊 No open positions")
        else:
            print("❌ Failed to get position info")
        
        print("\n✅ All testnet tests passed!")
        print("🚀 Your testnet connection is working correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return False

def main():
    """Main function."""
    success = test_testnet_direct()
    
    if success:
        print("\n🎉 Testnet is ready!")
        print("\nNext steps:")
        print("1. Run 'python main.py' to start the trading bot")
        print("2. The bot should now work in testnet mode")
    else:
        print("\n❌ Testnet connection failed")
        print("\nTroubleshooting:")
        print("1. Check your API keys are correct")
        print("2. Make sure they're from testnet.binancefuture.com")
        print("3. Verify API permissions include futures trading")

if __name__ == "__main__":
    main()
