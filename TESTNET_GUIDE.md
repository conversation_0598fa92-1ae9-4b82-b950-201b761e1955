# 🧪 Binance Futures Testnet Guide

This guide will help you set up and use the Binance Futures Testnet with your trading bot.

## 🔑 Testnet Setup

### 1. Create a Binance Futures Testnet Account

1. Visit [Binance Futures Testnet](https://testnet.binancefuture.com)
2. Click "Log In" and then "Register Now"
3. Enter your email and create a password
4. Complete the verification process

### 2. Get Testnet API Keys

1. Log in to [Binance Futures Testnet](https://testnet.binancefuture.com)
2. Click on your profile icon and select "API Management"
3. Click "Create API" and follow the instructions
4. Save your API Key and Secret Key securely

### 3. Configure Your Bot for Testnet

1. Create or update your `.env` file with your testnet API keys:
   ```
   BINANCE_API_KEY=your_testnet_api_key
   BINANCE_SECRET_KEY=your_testnet_secret_key
   INITIAL_BALANCE=10000
   ```

2. Ensure your `config/config.yaml` has testnet mode enabled:
   ```yaml
   trading:
     mode: "testnet"  # testnet mode for demo trading
   ```

## 💰 Getting Testnet Funds

Binance Futures Testnet provides free USDT for testing:

1. Log in to [Binance Futures Testnet](https://testnet.binancefuture.com)
2. Click on "Assets" > "Futures Account Transfer"
3. Transfer USDT from your spot wallet to your futures wallet
4. If you need more funds, click on "Get Assets" to receive free testnet USDT

## 🚀 Running the Bot on Testnet

1. **Test your setup**:
   ```bash
   python test_live_setup.py
   ```

2. **View available altcoins**:
   ```bash
   python altcoin_summary.py
   ```

3. **Start the bot in testnet mode**:
   ```bash
   python main.py
   ```

4. **Monitor your testnet trades**:
   - Check the bot logs: `tail -f logs/trading_bot.log`
   - View your positions on the [Testnet website](https://testnet.binancefuture.com)
   - Check the trades CSV file: `logs/trades.csv`

## 📊 Testnet vs. Live Trading

### Similarities
- Same API structure and endpoints
- Real market data (prices, volumes, etc.)
- Same order types and position management
- Same leverage and margin requirements

### Differences
- No real money at risk
- Unlimited free testnet USDT available
- Slightly different API base URL
- May have different liquidity/slippage characteristics
- Some advanced features might be limited

## 🔄 Testing Workflow

1. **Develop and test strategies** on testnet
2. **Monitor performance** over several days/weeks
3. **Analyze results** using the bot's logging and reporting
4. **Refine parameters** based on testnet performance
5. **When confident**, consider moving to live trading

## 🛠️ Troubleshooting Testnet Issues

### API Connection Problems
- Ensure you're using testnet API keys (not live keys)
- Check that `trading.mode` is set to "testnet" in config
- Verify your API keys have proper permissions

### Order Placement Issues
- Check minimum order size requirements
- Ensure you have sufficient testnet balance
- Verify the symbol is available on testnet

### Data Retrieval Problems
- Some symbols might not be available on testnet
- Historical data might be limited for some pairs
- Market data might have slight delays

## 🔍 Monitoring Your Testnet Bot

### Via Bot Logs
```bash
tail -f logs/trading_bot.log
```

### Via Testnet Website
1. Log in to [Binance Futures Testnet](https://testnet.binancefuture.com)
2. Go to "Positions" to see your open positions
3. Check "Order History" for your executed orders
4. View "Trade History" for all your trades

### Via Bot's CSV Logs
```bash
cat logs/trades.csv
```

## 🚦 Moving from Testnet to Live

When you're ready to move to live trading:

1. Create new API keys on the real Binance Futures platform
2. Update your `.env` file with the live API keys
3. Change `trading.mode` to "live" in `config/config.yaml`
4. Start with small position sizes (1-2%)
5. Monitor closely during the transition

## 📝 Best Practices

1. **Test thoroughly** on testnet before going live
2. **Document performance** metrics from testnet
3. **Test edge cases** like market volatility and API outages
4. **Compare strategies** side by side on testnet
5. **Practice risk management** even on testnet

---

Remember: Successful testnet trading doesn't guarantee success in live trading, but it's an excellent way to validate your strategy and bot functionality without financial risk.

Happy testing!
