"""
Advanced token scanner for finding profitable trading opportunities.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import time

logger = logging.getLogger(__name__)

class TokenScanner:
    """Advanced token scanner with technical and fundamental analysis."""
    
    def __init__(self, binance_client, config: Dict):
        """
        Initialize token scanner.

        Args:
            binance_client: Binance API client
            config: Scanner configuration
        """
        self.client = binance_client
        self.config = config
        self.base_currency = config.get('base_currency', 'USDT')

        # Scanner settings
        self.min_volume_24h = config.get('min_volume_24h', 500000)   # $500K for altcoins
        self.min_price = config.get('min_price', 0.001)  # Minimum price filter
        self.max_price = config.get('max_price', 500)    # Maximum price filter
        self.top_n_tokens = config.get('top_n_tokens', 30)  # More altcoins to analyze

        # ALTCOIN-SPECIFIC FILTERS
        self.altcoin_focus = config.get('altcoin_focus', True)
        self.excluded_symbols = {
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT',  # Major coins
            'USDCUSDT', 'BUSDUSDT', 'TUSDUSDT', 'DAIUSDT',  # Stablecoins
            'WBTCUSDT', 'STETHUSDT'  # Wrapped/staked versions
        }
        
        # Technical analysis thresholds
        self.rsi_oversold = config.get('rsi_oversold', 30)
        self.rsi_overbought = config.get('rsi_overbought', 70)
        self.volume_spike_threshold = config.get('volume_spike_threshold', 2.0)  # 2x average volume
        
        logger.info("Token Scanner initialized")
    
    def get_all_usdt_pairs(self) -> List[str]:
        """Get all USDT trading pairs from Binance."""
        try:
            exchange_info = self.client.client.get_exchange_info()
            usdt_pairs = []
            
            for symbol_info in exchange_info['symbols']:
                symbol = symbol_info['symbol']
                if (symbol.endswith('USDT') and 
                    symbol_info['status'] == 'TRADING' and
                    symbol_info['quoteAsset'] == 'USDT'):
                    usdt_pairs.append(symbol)
            
            logger.info(f"Found {len(usdt_pairs)} USDT trading pairs")
            return usdt_pairs
            
        except Exception as e:
            logger.error(f"Error getting USDT pairs: {e}")
            return []
    
    def get_24h_ticker_stats(self) -> Dict[str, Dict]:
        """Get 24h ticker statistics for all symbols."""
        try:
            tickers = self.client.client.get_ticker()
            ticker_dict = {}
            
            for ticker in tickers:
                symbol = ticker['symbol']
                if symbol.endswith('USDT'):
                    ticker_dict[symbol] = {
                        'price': float(ticker['lastPrice']),
                        'volume': float(ticker['volume']),
                        'quote_volume': float(ticker['quoteVolume']),
                        'price_change_percent': float(ticker['priceChangePercent']),
                        'high': float(ticker['highPrice']),
                        'low': float(ticker['lowPrice']),
                        'count': int(ticker['count'])
                    }
            
            logger.info(f"Retrieved 24h stats for {len(ticker_dict)} symbols")
            return ticker_dict
            
        except Exception as e:
            logger.error(f"Error getting 24h ticker stats: {e}")
            return {}
    
    def filter_tokens_by_criteria(self, ticker_stats: Dict[str, Dict]) -> List[str]:
        """Filter tokens based on volume, price, and activity criteria."""
        filtered_tokens = []

        for symbol, stats in ticker_stats.items():
            # ALTCOIN FILTER: Exclude major coins and stablecoins
            if self.altcoin_focus and symbol in self.excluded_symbols:
                continue

            # Apply volume, price, and activity filters
            if (stats['quote_volume'] >= self.min_volume_24h and
                self.min_price <= stats['price'] <= self.max_price and
                stats['count'] >= 500):  # Lower trade count for altcoins

                # Additional altcoin criteria
                volatility = abs(stats['price_change_percent'])

                # Prefer altcoins with some volatility (0.5% - 15%)
                if 0.5 <= volatility <= 15.0:
                    filtered_tokens.append({
                        'symbol': symbol,
                        'volume': stats['quote_volume'],
                        'price_change': stats['price_change_percent'],
                        'price': stats['price'],
                        'trades': stats['count'],
                        'volatility': volatility
                    })
        
        # Sort altcoins by combined score (volume + volatility)
        for token in filtered_tokens:
            # Score = volume weight (70%) + volatility weight (30%)
            volume_score = token['volume'] / 1000000  # Normalize by millions
            volatility_score = token['volatility'] * 100  # Scale volatility
            token['altcoin_score'] = (volume_score * 0.7) + (volatility_score * 0.3)

        filtered_tokens.sort(key=lambda x: x['altcoin_score'], reverse=True)

        # Take top N altcoins
        top_tokens = filtered_tokens[:self.top_n_tokens]
        symbols = [token['symbol'] for token in top_tokens]

        logger.info(f"🪙 Filtered to {len(symbols)} promising ALTCOINS")

        # Log top 5 altcoins found
        for i, token in enumerate(top_tokens[:5]):
            logger.info(f"  #{i+1}: {token['symbol']} - Vol: ${token['volume']:,.0f}, "
                       f"Change: {token['price_change']:+.1f}%, Price: ${token['price']:.4f}")

        return symbols
    
    def calculate_technical_score(self, data: pd.DataFrame) -> Dict[str, float]:
        """Calculate technical analysis score for a token."""
        if len(data) < 50:
            return {'score': 0, 'signals': {}}
        
        try:
            # Calculate technical indicators
            close = data['close']
            high = data['high']
            low = data['low']
            volume = data['volume']
            
            # RSI
            rsi = self.calculate_rsi(close, 14)
            current_rsi = rsi.iloc[-1] if not rsi.empty else 50
            
            # Moving averages
            sma_20 = close.rolling(20).mean()
            sma_50 = close.rolling(50).mean()
            ema_12 = close.ewm(span=12).mean()
            ema_26 = close.ewm(span=26).mean()
            
            # MACD
            macd = ema_12 - ema_26
            macd_signal = macd.ewm(span=9).mean()
            macd_histogram = macd - macd_signal
            
            # Bollinger Bands
            bb_middle = close.rolling(20).mean()
            bb_std = close.rolling(20).std()
            bb_upper = bb_middle + (bb_std * 2)
            bb_lower = bb_middle - (bb_std * 2)
            
            # Volume analysis
            volume_sma = volume.rolling(20).mean()
            volume_ratio = volume.iloc[-1] / volume_sma.iloc[-1] if volume_sma.iloc[-1] > 0 else 1
            
            # Current values
            current_price = close.iloc[-1]
            current_sma_20 = sma_20.iloc[-1]
            current_sma_50 = sma_50.iloc[-1]
            current_macd = macd.iloc[-1]
            current_macd_signal = macd_signal.iloc[-1]
            current_bb_upper = bb_upper.iloc[-1]
            current_bb_lower = bb_lower.iloc[-1]
            
            # Calculate signals
            signals = {
                'rsi_oversold': current_rsi < self.rsi_oversold,
                'rsi_overbought': current_rsi > self.rsi_overbought,
                'price_above_sma20': current_price > current_sma_20,
                'price_above_sma50': current_price > current_sma_50,
                'sma20_above_sma50': current_sma_20 > current_sma_50,
                'macd_bullish': current_macd > current_macd_signal,
                'volume_spike': volume_ratio > self.volume_spike_threshold,
                'bb_oversold': current_price < current_bb_lower,
                'bb_overbought': current_price > current_bb_upper
            }
            
            # Calculate composite score (0-100)
            score = 0
            
            # Trend signals (40 points max)
            if signals['price_above_sma20']: score += 10
            if signals['price_above_sma50']: score += 10
            if signals['sma20_above_sma50']: score += 10
            if signals['macd_bullish']: score += 10
            
            # Momentum signals (30 points max)
            if signals['rsi_oversold']: score += 15  # Oversold = buying opportunity
            elif 40 < current_rsi < 60: score += 10   # Neutral RSI
            elif signals['rsi_overbought']: score -= 10  # Overbought = selling signal
            
            if signals['volume_spike']: score += 15
            
            # Mean reversion signals (20 points max)
            if signals['bb_oversold']: score += 10
            elif signals['bb_overbought']: score -= 10
            
            # Volatility bonus (10 points max)
            volatility = close.pct_change().std() * np.sqrt(24)  # Daily volatility
            if 0.02 < volatility < 0.08: score += 10  # Good volatility range
            
            # Normalize score to 0-100
            score = max(0, min(100, score))
            
            return {
                'score': score,
                'signals': signals,
                'indicators': {
                    'rsi': current_rsi,
                    'macd': current_macd,
                    'macd_signal': current_macd_signal,
                    'volume_ratio': volume_ratio,
                    'volatility': volatility,
                    'price': current_price,
                    'sma_20': current_sma_20,
                    'sma_50': current_sma_50
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating technical score: {e}")
            return {'score': 0, 'signals': {}}
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_fundamental_score(self, symbol: str, ticker_stats: Dict) -> Dict[str, float]:
        """Calculate fundamental analysis score."""
        try:
            stats = ticker_stats.get(symbol, {})
            
            # Volume score (0-30 points)
            volume_score = min(30, (stats.get('quote_volume', 0) / 1000000) * 3)  # 3 points per $1M volume
            
            # Price momentum score (0-25 points)
            price_change = stats.get('price_change_percent', 0)
            if price_change > 0:
                momentum_score = min(25, price_change * 2)  # 2 points per 1% gain
            else:
                momentum_score = max(-25, price_change * 2)  # Penalty for losses
            
            # Trading activity score (0-20 points)
            trade_count = stats.get('count', 0)
            activity_score = min(20, trade_count / 1000)  # 1 point per 1000 trades
            
            # Liquidity score (0-25 points)
            price = stats.get('price', 0)
            if 0.1 <= price <= 100:  # Sweet spot for retail trading
                liquidity_score = 25
            elif 0.01 <= price < 0.1 or 100 < price <= 1000:
                liquidity_score = 15
            else:
                liquidity_score = 5
            
            total_score = volume_score + momentum_score + activity_score + liquidity_score
            
            return {
                'score': max(0, min(100, total_score)),
                'volume_score': volume_score,
                'momentum_score': momentum_score,
                'activity_score': activity_score,
                'liquidity_score': liquidity_score
            }
            
        except Exception as e:
            logger.error(f"Error calculating fundamental score for {symbol}: {e}")
            return {'score': 0}
    
    def scan_tokens(self) -> List[Dict]:
        """Scan and rank tokens by trading potential."""
        logger.info("Starting token scan...")
        
        # Get market data
        ticker_stats = self.get_24h_ticker_stats()
        if not ticker_stats:
            logger.error("Failed to get market data")
            return []
        
        # Filter tokens
        candidate_symbols = self.filter_tokens_by_criteria(ticker_stats)
        if not candidate_symbols:
            logger.error("No tokens passed filtering criteria")
            return []
        
        # Analyze each token
        token_scores = []
        
        for i, symbol in enumerate(candidate_symbols):
            try:
                logger.info(f"Analyzing {symbol} ({i+1}/{len(candidate_symbols)})")
                
                # Get historical data
                data = self.client.get_historical_klines(symbol, '1h', 100)
                if data.empty:
                    continue
                
                # Calculate scores
                technical_analysis = self.calculate_technical_score(data)
                fundamental_analysis = self.calculate_fundamental_score(symbol, ticker_stats)
                
                # Combined score (70% technical, 30% fundamental)
                combined_score = (technical_analysis['score'] * 0.7 + 
                                fundamental_analysis['score'] * 0.3)
                
                token_info = {
                    'symbol': symbol,
                    'combined_score': combined_score,
                    'technical_score': technical_analysis['score'],
                    'fundamental_score': fundamental_analysis['score'],
                    'technical_signals': technical_analysis.get('signals', {}),
                    'technical_indicators': technical_analysis.get('indicators', {}),
                    'fundamental_breakdown': fundamental_analysis,
                    'market_data': ticker_stats[symbol],
                    'recommendation': self.get_recommendation(technical_analysis, fundamental_analysis)
                }
                
                token_scores.append(token_info)
                
                # Small delay to avoid rate limits
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
                continue
        
        # Sort by combined score
        token_scores.sort(key=lambda x: x['combined_score'], reverse=True)
        
        logger.info(f"Token scan completed. Analyzed {len(token_scores)} tokens")
        return token_scores
    
    def get_recommendation(self, technical: Dict, fundamental: Dict) -> str:
        """Get trading recommendation based on analysis."""
        tech_score = technical['score']
        fund_score = fundamental['score']
        signals = technical.get('signals', {})
        
        # Strong buy conditions
        if (tech_score >= 70 and fund_score >= 60 and
            signals.get('rsi_oversold') and signals.get('volume_spike')):
            return 'STRONG_BUY'
        
        # Buy conditions
        elif (tech_score >= 60 and fund_score >= 50 and
              (signals.get('macd_bullish') or signals.get('price_above_sma20'))):
            return 'BUY'
        
        # Hold conditions
        elif tech_score >= 40 and fund_score >= 40:
            return 'HOLD'
        
        # Sell conditions
        elif (tech_score <= 30 or fund_score <= 30 or
              signals.get('rsi_overbought')):
            return 'SELL'
        
        else:
            return 'NEUTRAL'
    
    def get_top_tokens(self, n: int = 5) -> List[Dict]:
        """Get top N tokens for trading."""
        all_tokens = self.scan_tokens()
        
        # Filter for buy recommendations
        buy_tokens = [token for token in all_tokens 
                     if token['recommendation'] in ['STRONG_BUY', 'BUY']]
        
        return buy_tokens[:n]
