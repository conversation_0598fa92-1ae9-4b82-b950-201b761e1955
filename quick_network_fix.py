"""
Quick Network Fix for Binance API Connectivity Issues
"""

import subprocess
import sys
import os
import platform
import socket
import requests
import time

def run_command(command, description):
    """Run a system command and show results."""
    print(f"🔧 {description}...")
    try:
        if platform.system().lower() == "windows":
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        else:
            result = subprocess.run(command.split(), capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(f"✅ {description} - Success")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()[:100]}...")
        else:
            print(f"❌ {description} - Failed")
            if result.stderr.strip():
                print(f"   Error: {result.stderr.strip()[:100]}...")
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - Timeout")
    except Exception as e:
        print(f"❌ {description} - Error: {e}")

def fix_dns_issues():
    """Fix DNS resolution issues."""
    print("\n🔍 Fixing DNS Issues")
    print("=" * 40)
    
    if platform.system().lower() == "windows":
        # Flush DNS cache
        run_command("ipconfig /flushdns", "Flushing DNS cache")
        
        # Reset Winsock
        run_command("netsh winsock reset", "Resetting Winsock")
        
        # Reset TCP/IP stack
        run_command("netsh int ip reset", "Resetting TCP/IP stack")
        
        # Set DNS to Google DNS
        print("\n🌐 Setting DNS to Google DNS (*******, *******)")
        run_command('netsh interface ip set dns "Wi-Fi" static *******', "Setting primary DNS")
        run_command('netsh interface ip add dns "Wi-Fi" ******* index=2', "Setting secondary DNS")
        
        # Also try for Ethernet
        run_command('netsh interface ip set dns "Ethernet" static *******', "Setting primary DNS (Ethernet)")
        run_command('netsh interface ip add dns "Ethernet" ******* index=2', "Setting secondary DNS (Ethernet)")
        
    else:
        # Linux/Mac DNS fixes
        run_command("sudo systemctl restart systemd-resolved", "Restarting DNS resolver")
        
        # Flush DNS cache on Mac
        if platform.system().lower() == "darwin":
            run_command("sudo dscacheutil -flushcache", "Flushing DNS cache (Mac)")

def fix_time_sync():
    """Fix system time synchronization."""
    print("\n⏰ Fixing Time Synchronization")
    print("=" * 40)
    
    if platform.system().lower() == "windows":
        # Stop Windows Time service
        run_command("net stop w32time", "Stopping Windows Time service")
        
        # Configure time server
        run_command("w32tm /config /manualpeerlist:time.windows.com /syncfromflags:manual", "Configuring time server")
        
        # Start Windows Time service
        run_command("net start w32time", "Starting Windows Time service")
        
        # Force time sync
        run_command("w32tm /resync", "Forcing time synchronization")
        
    else:
        # Linux time sync
        run_command("sudo ntpdate -s time.nist.gov", "Syncing time with NIST")

def test_connectivity_after_fix():
    """Test connectivity after applying fixes."""
    print("\n🧪 Testing Connectivity After Fixes")
    print("=" * 40)
    
    # Test DNS resolution
    try:
        ip = socket.gethostbyname("api.binance.com")
        print(f"✅ DNS Resolution: api.binance.com → {ip}")
    except Exception as e:
        print(f"❌ DNS Resolution failed: {e}")
    
    # Test HTTP connectivity
    try:
        response = requests.get("https://api.binance.com/api/v3/time", timeout=15)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ HTTP Connectivity: OK (Server time: {data.get('serverTime')})")
        else:
            print(f"❌ HTTP Connectivity: Status {response.status_code}")
    except Exception as e:
        print(f"❌ HTTP Connectivity failed: {e}")

def check_firewall_antivirus():
    """Check and provide guidance on firewall/antivirus."""
    print("\n🛡️ Firewall and Antivirus Check")
    print("=" * 40)
    
    print("⚠️  Manual steps required:")
    print("1. Temporarily disable Windows Firewall:")
    print("   • Go to Control Panel → System and Security → Windows Defender Firewall")
    print("   • Click 'Turn Windows Defender Firewall on or off'")
    print("   • Turn off for both networks temporarily")
    print()
    
    print("2. Add Python to firewall exceptions:")
    print("   • Windows Firewall → Allow an app through firewall")
    print("   • Add Python.exe from your Python installation")
    print()
    
    print("3. Temporarily disable antivirus real-time protection")
    print("4. If on corporate network, contact IT about Binance API access")

def suggest_vpn_solution():
    """Suggest VPN solutions for blocked regions."""
    print("\n🌍 VPN Solution for Blocked Regions")
    print("=" * 40)
    
    print("If Binance is blocked in your region:")
    print("1. 🔒 Use a reliable VPN service:")
    print("   • ExpressVPN, NordVPN, Surfshark")
    print("   • Connect to a server in US, Europe, or Asia")
    print()
    
    print("2. 🐍 Configure Python to use VPN:")
    print("   • Make sure VPN is connected before running bot")
    print("   • Test with: python test_network_connectivity.py")
    print()
    
    print("3. 🏢 Corporate network workaround:")
    print("   • Use mobile hotspot temporarily")
    print("   • Ask IT to whitelist Binance domains")

def create_test_script():
    """Create a simple test script to verify fixes."""
    test_script = '''
import requests
import socket
import time

def test_binance_connectivity():
    print("🧪 Quick Binance Connectivity Test")
    print("=" * 40)
    
    # Test 1: DNS
    try:
        ip = socket.gethostbyname("api.binance.com")
        print(f"✅ DNS: api.binance.com → {ip}")
    except Exception as e:
        print(f"❌ DNS failed: {e}")
        return False
    
    # Test 2: HTTP
    try:
        start_time = time.time()
        response = requests.get("https://api.binance.com/api/v3/time", timeout=15)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ HTTP: OK ({response_time:.2f}s)")
            print(f"   Server time: {data.get('serverTime')}")
            return True
        else:
            print(f"❌ HTTP: Status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ HTTP failed: {e}")
        return False

if __name__ == "__main__":
    success = test_binance_connectivity()
    if success:
        print("\\n🎉 Connectivity is working! You can now run the trading bot.")
    else:
        print("\\n❌ Connectivity issues persist. Try the suggested fixes.")
'''
    
    with open('test_connectivity_quick.py', 'w') as f:
        f.write(test_script)
    
    print("✅ Created test_connectivity_quick.py")

def main():
    """Run quick network fixes."""
    print("🚀 Quick Network Fix for Binance API")
    print("=" * 50)
    
    print("⚠️  This script will attempt to fix common network connectivity issues.")
    print("⚠️  Some commands require administrator privileges.")
    print()
    
    if platform.system().lower() == "windows":
        print("💡 For best results, run this as Administrator:")
        print("   Right-click Command Prompt → Run as Administrator")
        print("   Then run: python quick_network_fix.py")
        print()
    
    response = input("Continue with network fixes? (y/n): ").lower().strip()
    if response != 'y':
        print("Cancelled by user.")
        return
    
    # Apply fixes
    fix_dns_issues()
    fix_time_sync()
    
    print("\n⏳ Waiting 10 seconds for changes to take effect...")
    time.sleep(10)
    
    # Test after fixes
    test_connectivity_after_fix()
    
    # Additional guidance
    check_firewall_antivirus()
    suggest_vpn_solution()
    
    # Create test script
    print("\n📝 Creating Quick Test Script")
    print("=" * 40)
    create_test_script()
    
    print("\n" + "=" * 50)
    print("🎯 NEXT STEPS:")
    print("1. Restart your computer to apply all changes")
    print("2. Run: python test_connectivity_quick.py")
    print("3. If still failing, try VPN or check firewall")
    print("4. Run: python main.py to start trading bot")
    
    print("\n💡 If issues persist:")
    print("• Run full diagnosis: python test_network_connectivity.py")
    print("• Try mobile hotspot to test if it's network-specific")
    print("• Contact your ISP or network administrator")

if __name__ == "__main__":
    main()
