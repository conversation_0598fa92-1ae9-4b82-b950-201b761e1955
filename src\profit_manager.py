"""
Enhanced Profit Management System for Futures Trading Bot.
Implements trailing stops, break-even logic, and dynamic profit targets.
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ProfitManager:
    """
    Enhanced profit management system that implements:
    - Trailing stop functionality
    - Break-even logic
    - Dynamic profit targets based on market conditions
    - Position scaling and partial profit taking
    """
    
    def __init__(self, config: Dict):
        """
        Initialize profit manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        
        # Trailing stop configuration
        self.trailing_stop_enabled = config.get('trailing_stop_enabled', True)
        self.trailing_stop_percentage = config.get('trailing_stop_percentage', 0.005)  # 0.5%
        self.trailing_activation_profit = config.get('trailing_activation_profit', 0.01)  # 1% profit to activate
        
        # Break-even configuration
        self.break_even_enabled = config.get('break_even_enabled', True)
        self.break_even_trigger_profit = config.get('break_even_trigger_profit', 0.015)  # 1.5% profit
        self.break_even_buffer = config.get('break_even_buffer', 0.002)  # 0.2% buffer above entry
        
        # Dynamic profit targets
        self.dynamic_targets_enabled = config.get('dynamic_targets_enabled', True)
        self.base_profit_target = config.get('base_profit_target', 0.02)  # 2% base target
        self.max_profit_target = config.get('max_profit_target', 0.08)    # 8% max target
        
        # Partial profit taking
        self.partial_profit_enabled = config.get('partial_profit_enabled', True)
        self.partial_profit_levels = config.get('partial_profit_levels', [0.02, 0.04, 0.06])  # 2%, 4%, 6%
        self.partial_profit_sizes = config.get('partial_profit_sizes', [0.3, 0.3, 0.4])      # 30%, 30%, 40%
        
        # Position tracking
        self.position_states = {}  # Track trailing stops and profit levels per position
        
        logger.info("Enhanced Profit Manager initialized")
    
    def initialize_position_tracking(self, symbol: str, entry_price: float, side: str, quantity: float):
        """
        Initialize tracking for a new position.
        
        Args:
            symbol: Trading symbol
            entry_price: Entry price
            side: Position side ('BUY' or 'SELL')
            quantity: Position quantity
        """
        self.position_states[symbol] = {
            'entry_price': entry_price,
            'side': side,
            'original_quantity': quantity,
            'remaining_quantity': quantity,
            'highest_profit': 0.0,
            'trailing_stop_price': None,
            'trailing_stop_active': False,
            'break_even_set': False,
            'partial_profits_taken': [],
            'profit_target': self.base_profit_target,
            'last_update': pd.Timestamp.now()
        }
        
        logger.info(f"Position tracking initialized for {symbol}: {side} {quantity} at {entry_price}")
    
    def calculate_current_profit_percentage(self, symbol: str, current_price: float) -> float:
        """
        Calculate current profit percentage for a position.
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
            
        Returns:
            Profit percentage (positive for profit, negative for loss)
        """
        if symbol not in self.position_states:
            return 0.0
        
        state = self.position_states[symbol]
        entry_price = state['entry_price']
        side = state['side']
        
        if side == 'BUY':
            profit_pct = (current_price - entry_price) / entry_price
        else:  # SELL
            profit_pct = (entry_price - current_price) / entry_price
        
        return profit_pct
    
    def update_dynamic_profit_target(self, symbol: str, market_volatility: float = None, trend_strength: float = None):
        """
        Update dynamic profit target based on market conditions.
        
        Args:
            symbol: Trading symbol
            market_volatility: Current market volatility
            trend_strength: Current trend strength
        """
        if symbol not in self.position_states:
            return
        
        state = self.position_states[symbol]
        base_target = self.base_profit_target
        
        # Adjust based on volatility
        if market_volatility:
            if market_volatility > 0.05:  # High volatility
                # Increase target in high volatility
                volatility_adjustment = min(0.02, market_volatility * 0.5)
                base_target += volatility_adjustment
            elif market_volatility < 0.01:  # Low volatility
                # Decrease target in low volatility
                base_target *= 0.8
        
        # Adjust based on trend strength
        if trend_strength and trend_strength > 1.5:
            # Increase target in strong trends
            trend_adjustment = min(0.03, (trend_strength - 1) * 0.02)
            base_target += trend_adjustment
        
        # Cap the target
        state['profit_target'] = min(base_target, self.max_profit_target)
        
        logger.info(f"Dynamic profit target updated for {symbol}: {state['profit_target']:.2%}")
    
    def check_break_even_trigger(self, symbol: str, current_price: float) -> bool:
        """
        Check if break-even should be triggered.
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
            
        Returns:
            True if break-even should be set
        """
        if not self.break_even_enabled or symbol not in self.position_states:
            return False
        
        state = self.position_states[symbol]
        
        if state['break_even_set']:
            return False
        
        profit_pct = self.calculate_current_profit_percentage(symbol, current_price)
        
        if profit_pct >= self.break_even_trigger_profit:
            state['break_even_set'] = True
            logger.info(f"Break-even triggered for {symbol} at {profit_pct:.2%} profit")
            return True
        
        return False
    
    def get_break_even_price(self, symbol: str) -> Optional[float]:
        """
        Get break-even price for a position.
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Break-even price or None
        """
        if symbol not in self.position_states:
            return None
        
        state = self.position_states[symbol]
        entry_price = state['entry_price']
        side = state['side']
        
        if side == 'BUY':
            break_even_price = entry_price * (1 + self.break_even_buffer)
        else:  # SELL
            break_even_price = entry_price * (1 - self.break_even_buffer)
        
        return break_even_price
    
    def update_trailing_stop(self, symbol: str, current_price: float) -> Optional[float]:
        """
        Update trailing stop for a position.
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
            
        Returns:
            New trailing stop price or None
        """
        if not self.trailing_stop_enabled or symbol not in self.position_states:
            return None
        
        state = self.position_states[symbol]
        profit_pct = self.calculate_current_profit_percentage(symbol, current_price)
        
        # Update highest profit
        if profit_pct > state['highest_profit']:
            state['highest_profit'] = profit_pct
        
        # Activate trailing stop if profit threshold reached
        if not state['trailing_stop_active'] and profit_pct >= self.trailing_activation_profit:
            state['trailing_stop_active'] = True
            logger.info(f"Trailing stop activated for {symbol} at {profit_pct:.2%} profit")
        
        # Update trailing stop price if active
        if state['trailing_stop_active']:
            side = state['side']
            
            if side == 'BUY':
                # For long positions, trailing stop moves up
                new_stop = current_price * (1 - self.trailing_stop_percentage)
                if state['trailing_stop_price'] is None or new_stop > state['trailing_stop_price']:
                    state['trailing_stop_price'] = new_stop
                    logger.info(f"Trailing stop updated for {symbol}: {new_stop:.6f}")
            else:  # SELL
                # For short positions, trailing stop moves down
                new_stop = current_price * (1 + self.trailing_stop_percentage)
                if state['trailing_stop_price'] is None or new_stop < state['trailing_stop_price']:
                    state['trailing_stop_price'] = new_stop
                    logger.info(f"Trailing stop updated for {symbol}: {new_stop:.6f}")
        
        return state['trailing_stop_price']
    
    def check_trailing_stop_trigger(self, symbol: str, current_price: float) -> bool:
        """
        Check if trailing stop should be triggered.
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
            
        Returns:
            True if trailing stop should be triggered
        """
        if symbol not in self.position_states:
            return False
        
        state = self.position_states[symbol]
        
        if not state['trailing_stop_active'] or state['trailing_stop_price'] is None:
            return False
        
        side = state['side']
        
        if side == 'BUY':
            # Long position: trigger if price falls below trailing stop
            triggered = current_price <= state['trailing_stop_price']
        else:  # SELL
            # Short position: trigger if price rises above trailing stop
            triggered = current_price >= state['trailing_stop_price']
        
        if triggered:
            logger.info(f"Trailing stop triggered for {symbol} at {current_price:.6f}")
        
        return triggered
    
    def check_partial_profit_opportunities(self, symbol: str, current_price: float) -> Optional[Tuple[float, float]]:
        """
        Check for partial profit taking opportunities.
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
            
        Returns:
            Tuple of (profit_level, quantity_to_close) or None
        """
        if not self.partial_profit_enabled or symbol not in self.position_states:
            return None
        
        state = self.position_states[symbol]
        profit_pct = self.calculate_current_profit_percentage(symbol, current_price)
        
        # Check each profit level
        for i, level in enumerate(self.partial_profit_levels):
            if level not in state['partial_profits_taken'] and profit_pct >= level:
                # Calculate quantity to close
                quantity_pct = self.partial_profit_sizes[i]
                quantity_to_close = state['original_quantity'] * quantity_pct
                
                # Update tracking
                state['partial_profits_taken'].append(level)
                state['remaining_quantity'] -= quantity_to_close
                
                logger.info(f"Partial profit opportunity for {symbol}: {level:.1%} profit, "
                           f"closing {quantity_to_close:.6f} ({quantity_pct:.1%})")

                return level, quantity_to_close

        return None

    def check_profit_target_reached(self, symbol: str, current_price: float) -> bool:
        """
        Check if dynamic profit target has been reached.

        Args:
            symbol: Trading symbol
            current_price: Current market price

        Returns:
            True if profit target reached
        """
        if symbol not in self.position_states:
            return False

        state = self.position_states[symbol]
        profit_pct = self.calculate_current_profit_percentage(symbol, current_price)

        target_reached = profit_pct >= state['profit_target']

        if target_reached:
            logger.info(f"Profit target reached for {symbol}: {profit_pct:.2%} >= {state['profit_target']:.2%}")

        return target_reached

    def get_exit_recommendation(self, symbol: str, current_price: float, market_conditions: Dict = None) -> Dict:
        """
        Get comprehensive exit recommendation for a position.

        Args:
            symbol: Trading symbol
            current_price: Current market price
            market_conditions: Current market conditions

        Returns:
            Exit recommendation dictionary
        """
        if symbol not in self.position_states:
            return {'action': 'HOLD', 'reason': 'No position tracking'}

        state = self.position_states[symbol]
        profit_pct = self.calculate_current_profit_percentage(symbol, current_price)

        # Update dynamic targets if market conditions provided
        if market_conditions:
            volatility = market_conditions.get('volatility', None)
            trend_strength = market_conditions.get('trend_strength', None)
            self.update_dynamic_profit_target(symbol, volatility, trend_strength)

        # Update trailing stop
        self.update_trailing_stop(symbol, current_price)

        # Check exit conditions in priority order

        # 1. Immediate profit exit (context.txt rule: exit as soon as profitable)
        if profit_pct > 0:
            return {
                'action': 'CLOSE_FULL',
                'reason': 'Immediate profit exit (context.txt rule)',
                'profit_pct': profit_pct,
                'priority': 'HIGH'
            }

        # 2. Trailing stop trigger
        if self.check_trailing_stop_trigger(symbol, current_price):
            return {
                'action': 'CLOSE_FULL',
                'reason': 'Trailing stop triggered',
                'stop_price': state['trailing_stop_price'],
                'profit_pct': profit_pct,
                'priority': 'HIGH'
            }

        # 3. Break-even trigger
        if self.check_break_even_trigger(symbol, current_price):
            break_even_price = self.get_break_even_price(symbol)
            return {
                'action': 'SET_BREAK_EVEN',
                'reason': 'Break-even trigger activated',
                'break_even_price': break_even_price,
                'profit_pct': profit_pct,
                'priority': 'MEDIUM'
            }

        # 4. Partial profit opportunities
        partial_opportunity = self.check_partial_profit_opportunities(symbol, current_price)
        if partial_opportunity:
            level, quantity = partial_opportunity
            return {
                'action': 'CLOSE_PARTIAL',
                'reason': f'Partial profit at {level:.1%}',
                'quantity': quantity,
                'profit_level': level,
                'profit_pct': profit_pct,
                'priority': 'MEDIUM'
            }

        # 5. Profit target reached
        if self.check_profit_target_reached(symbol, current_price):
            return {
                'action': 'CLOSE_FULL',
                'reason': 'Dynamic profit target reached',
                'target': state['profit_target'],
                'profit_pct': profit_pct,
                'priority': 'MEDIUM'
            }

        # No exit conditions met
        return {
            'action': 'HOLD',
            'reason': 'No exit conditions met',
            'profit_pct': profit_pct,
            'trailing_stop_active': state['trailing_stop_active'],
            'trailing_stop_price': state['trailing_stop_price'],
            'profit_target': state['profit_target'],
            'priority': 'LOW'
        }

    def remove_position_tracking(self, symbol: str):
        """
        Remove position tracking when position is closed.

        Args:
            symbol: Trading symbol
        """
        if symbol in self.position_states:
            del self.position_states[symbol]
            logger.info(f"Position tracking removed for {symbol}")

    def get_position_summary(self, symbol: str) -> Dict:
        """
        Get summary of position state.

        Args:
            symbol: Trading symbol

        Returns:
            Position summary dictionary
        """
        if symbol not in self.position_states:
            return {}

        state = self.position_states[symbol]

        return {
            'symbol': symbol,
            'entry_price': state['entry_price'],
            'side': state['side'],
            'original_quantity': state['original_quantity'],
            'remaining_quantity': state['remaining_quantity'],
            'highest_profit': state['highest_profit'],
            'trailing_stop_active': state['trailing_stop_active'],
            'trailing_stop_price': state['trailing_stop_price'],
            'break_even_set': state['break_even_set'],
            'partial_profits_taken': state['partial_profits_taken'],
            'profit_target': state['profit_target'],
            'last_update': state['last_update']
        }

    def get_all_positions_summary(self) -> Dict:
        """Get summary of all tracked positions."""
        return {
            symbol: self.get_position_summary(symbol)
            for symbol in self.position_states.keys()
        }
