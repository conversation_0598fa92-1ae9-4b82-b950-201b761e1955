"""
Quick testnet status checker.
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient

def main():
    """Quick testnet status check."""
    print("🧪 Quick Testnet Status Check")
    print("=" * 40)
    
    # Load environment variables
    load_dotenv()
    
    try:
        # Initialize client
        client = BinanceClient(testnet=True)
        
        # Get account info
        account = client.futures_get_account_info()
        
        if not account:
            print("❌ Cannot connect to testnet")
            return
        
        # Check balance
        assets = account.get('assets', [])
        usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
        
        if usdt_asset:
            balance = float(usdt_asset.get('availableBalance', 0))
            print(f"💰 Testnet Balance: {balance:.2f} USDT")
            
            if balance < 10:
                print("⚠️  Low balance - get more testnet funds")
            else:
                print("✅ Sufficient balance for testing")
        else:
            print("❌ No USDT found in account")
        
        # Check positions
        positions = client.futures_get_position_info()
        if positions:
            open_positions = [pos for pos in positions if float(pos.get('positionAmt', 0)) != 0]
            
            if open_positions:
                print(f"📊 Open Positions: {len(open_positions)}")
                for pos in open_positions[:3]:  # Show first 3
                    symbol = pos.get('symbol', 'Unknown')
                    size = pos.get('positionAmt', '0')
                    pnl = pos.get('unRealizedProfit', '0')
                    print(f"   {symbol}: {size} (PnL: {pnl})")
            else:
                print("📊 No open positions")
        
        print("\n🚀 Ready for testnet trading!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
