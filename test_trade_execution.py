"""
Test script to verify why trades are not being executed despite valid context.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from strategies.enhanced_futures_strategy import EnhancedFuturesStrategy
from utils import load_config

def test_full_signal_flow():
    """Test the complete signal generation flow."""
    print("🔍 Testing Complete Signal Generation Flow")
    print("=" * 60)
    
    try:
        # Initialize client
        client = BinanceClient(testnet=True)
        
        # Load config
        config = load_config('config/config.yaml')
        strategy_config = config.get('strategies', {}).get('enhanced_futures', {})
        
        print("📊 Current Strategy Configuration:")
        print(f"   min_ema_spread: {strategy_config.get('min_ema_spread', 'NOT FOUND')}")
        print(f"   min_atr_pct: {strategy_config.get('min_atr_pct', 'NOT FOUND')}")
        print(f"   volume_spike_threshold: {strategy_config.get('volume_spike_threshold', 'NOT FOUND')}")
        print(f"   require_macd_confirmation: {strategy_config.get('require_macd_confirmation', 'NOT FOUND')}")
        
        # Initialize strategy
        strategy = EnhancedFuturesStrategy(client, strategy_config)
        
        # Test symbols that were passing context but not trading
        test_symbols = ['INITUSDT', 'DOGEUSDT', 'BTCUSDT']
        
        for symbol in test_symbols:
            print(f"\n🔍 FULL ANALYSIS FOR {symbol}")
            print("=" * 40)
            
            # Get market data
            data = client.get_historical_klines(symbol, '5m', 100)
            if data.empty:
                print(f"❌ No data for {symbol}")
                continue
            
            print(f"✅ Retrieved {len(data)} candles for {symbol}")
            
            # Step 1: Test market context
            print("\n1️⃣ MARKET CONTEXT CHECK:")
            data_with_indicators = strategy.calculate_indicators(data)
            context = strategy.check_market_trend_context(data_with_indicators, symbol)
            
            if not context['valid']:
                print(f"❌ Context failed: {context['reason']}")
                continue
            
            print(f"✅ Context passed")
            
            # Step 2: Test volume confirmation
            print("\n2️⃣ VOLUME CONFIRMATION CHECK:")
            volume_ok = strategy.check_volume_confirmation(data_with_indicators)
            
            if not volume_ok:
                print(f"❌ Volume confirmation failed")
                continue
            
            print(f"✅ Volume confirmation passed")
            
            # Step 3: Test signal conditions
            print("\n3️⃣ SIGNAL CONDITIONS CHECK:")
            latest = data_with_indicators.iloc[-1]
            
            print(f"   EMA Fast: {latest['EMA_fast']:.6f}")
            print(f"   EMA Slow: {latest['EMA_slow']:.6f}")
            print(f"   RSI: {latest['RSI']:.2f}")
            
            # Check EMA conditions
            ema_bullish = latest['EMA_fast'] > latest['EMA_slow']
            ema_bearish = latest['EMA_fast'] < latest['EMA_slow']
            
            # Check RSI conditions
            rsi_oversold = latest['RSI'] < 30
            rsi_overbought = latest['RSI'] > 70
            
            print(f"   EMA Bullish (Fast > Slow): {ema_bullish}")
            print(f"   EMA Bearish (Fast < Slow): {ema_bearish}")
            print(f"   RSI Oversold (<30): {rsi_oversold}")
            print(f"   RSI Overbought (>70): {rsi_overbought}")
            
            # Check signal conditions
            buy_condition = ema_bullish and rsi_oversold
            sell_condition = ema_bearish and rsi_overbought
            
            print(f"\n   🎯 BUY Signal (EMA Bullish + RSI Oversold): {buy_condition}")
            print(f"   🎯 SELL Signal (EMA Bearish + RSI Overbought): {sell_condition}")
            
            if buy_condition:
                print(f"   ✅ {symbol} should generate BUY signal!")
            elif sell_condition:
                print(f"   ✅ {symbol} should generate SELL signal!")
            else:
                print(f"   ❌ {symbol} - No signal conditions met")
                print(f"      Need: (EMA Bullish + RSI Oversold) OR (EMA Bearish + RSI Overbought)")
            
            # Step 4: Test actual signal generation
            print("\n4️⃣ ACTUAL SIGNAL GENERATION:")
            signal = strategy.generate_signal(data, symbol)
            print(f"   Generated signal: {signal}")
            
            if signal != 'HOLD':
                print(f"   🎉 SUCCESS! {symbol} generated {signal} signal")
            else:
                print(f"   ❌ No signal generated despite analysis")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing signal flow: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_improvements():
    """Suggest improvements to increase trading activity."""
    print("\n💡 SUGGESTIONS TO INCREASE TRADING ACTIVITY")
    print("=" * 60)
    
    print("🔧 Current Issues & Solutions:")
    print()
    print("1️⃣ RSI CONDITIONS TOO STRICT:")
    print("   Current: RSI < 30 (oversold) or RSI > 70 (overbought)")
    print("   Solution: Relax to RSI < 40 or RSI > 60")
    print()
    print("2️⃣ VOLUME THRESHOLD TOO HIGH:")
    print("   Current: 1.1x average volume required")
    print("   Solution: Reduce to 1.05x or disable volume check")
    print()
    print("3️⃣ ENABLE TEST MODE:")
    print("   Set ENABLE_TEST_MODE = True in strategy")
    print("   Add test symbols to force trades")
    print()
    print("4️⃣ CONSIDER TREND-FOLLOWING INSTEAD:")
    print("   Current: Counter-trend (buy oversold, sell overbought)")
    print("   Alternative: Trend-following (buy breakouts, sell breakdowns)")

def main():
    """Run trade execution analysis."""
    print("🧪 Trade Execution Analysis")
    print("=" * 70)
    
    if test_full_signal_flow():
        print("\n✅ Signal flow analysis completed!")
        suggest_improvements()
        
        print("\n🚀 Next Steps:")
        print("1. Check the detailed logs above")
        print("2. Identify which step is failing")
        print("3. Consider relaxing RSI thresholds")
        print("4. Or enable test mode for forced trades")
        print("5. Restart bot: python main.py")
    else:
        print("\n❌ Signal flow analysis failed")

if __name__ == "__main__":
    main()
