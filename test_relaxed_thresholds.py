"""
Quick test to verify the new extremely permissive thresholds.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from strategies.enhanced_futures_strategy import EnhancedFuturesStrategy
from utils import load_config

def test_new_thresholds():
    """Test the new extremely permissive thresholds."""
    print("🚀 Testing New Extremely Permissive Thresholds")
    print("=" * 60)
    
    try:
        # Initialize client
        client = BinanceClient(testnet=True)
        
        # Load config
        config = load_config('config/config.yaml')
        strategy_config = config.get('strategies', {}).get('enhanced_futures', {})
        
        print("📊 Config values loaded:")
        print(f"   min_ema_spread: {strategy_config.get('min_ema_spread', 'NOT FOUND')}")
        print(f"   min_atr_pct: {strategy_config.get('min_atr_pct', 'NOT FOUND')}")
        print(f"   volatility_threshold: {strategy_config.get('volatility_threshold', 'NOT FOUND')}")
        
        # Initialize strategy (this should show debug logging)
        print("\n🔧 Initializing strategy...")
        strategy = EnhancedFuturesStrategy(client, strategy_config)
        
        print(f"\n✅ Strategy initialized with:")
        print(f"   min_ema_spread: {strategy.min_ema_spread:.4f}")
        print(f"   min_atr_pct: {strategy.min_atr_pct:.4f}")
        print(f"   volatility_threshold: {strategy.volatility_threshold:.4f}")
        
        # Test with the symbols that were failing
        failing_symbols = ['INITUSDT', 'DOGEUSDT']
        
        for symbol in failing_symbols:
            print(f"\n📊 Testing {symbol} with new thresholds...")
            
            # Get market data
            data = client.get_historical_klines(symbol, '5m', 100)
            if data.empty:
                print(f"❌ No data for {symbol}")
                continue
            
            # Calculate indicators
            data_with_indicators = strategy.calculate_indicators(data)
            
            # Test market context (should now pass with relaxed thresholds)
            context = strategy.check_market_trend_context(data_with_indicators, symbol)
            
            if context['valid']:
                print(f"   ✅ {symbol} NOW PASSES market context check!")
                print(f"      EMA spread: {context['ema_spread']:.4f} (min: {strategy.min_ema_spread:.4f})")
                print(f"      ATR: {context['atr_pct']:.4f} (min: {strategy.min_atr_pct:.4f})")
                print(f"      Volatility: {context['volatility']:.4f} (max: {strategy.volatility_threshold:.4f})")
            else:
                print(f"   ⚠️  {symbol} still fails: {context['reason']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing new thresholds: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_thresholds():
    """Compare old vs new thresholds."""
    print("\n📊 Threshold Comparison")
    print("=" * 60)
    
    print("🔄 Threshold Evolution:")
    print("   EMA Spread:")
    print("     Original: 0.0030 (0.3%)")
    print("     Previous: 0.0015 (0.15%)")
    print("     NEW:      0.0005 (0.05%) ← 83% MORE PERMISSIVE")
    
    print("   ATR Threshold:")
    print("     Original: 0.0080 (0.8%)")
    print("     Previous: 0.0020 (0.2%)")
    print("     NEW:      0.0010 (0.1%) ← 50% MORE PERMISSIVE")
    
    print("   Volatility:")
    print("     Original: 0.0500 (5%)")
    print("     NEW:      0.1000 (10%) ← 100% MORE PERMISSIVE")
    
    print("\n🎯 Expected Impact:")
    print("✅ INITUSDT should now pass (ATR: 0.0025 > 0.0010)")
    print("✅ DOGEUSDT should now pass (EMA: 0.0006 > 0.0005)")
    print("✅ Most symbols should now pass the filters")
    print("✅ MUCH MORE TRADING ACTIVITY expected!")

def main():
    """Run threshold relaxation test."""
    print("🧪 Extremely Permissive Thresholds Test")
    print("=" * 70)
    
    compare_thresholds()
    
    if test_new_thresholds():
        print("\n🎉 New thresholds working correctly!")
        print("\n🚀 Next Steps:")
        print("1. Restart the trading bot: python main.py")
        print("2. Watch for much more trading activity")
        print("3. Symbols that were previously skipped should now trade")
        print("4. Look for logs showing successful context checks")
    else:
        print("\n❌ Threshold test failed. Check the errors above.")

if __name__ == "__main__":
    main()
