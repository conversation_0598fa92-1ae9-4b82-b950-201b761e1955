"""
Comprehensive Trade Logging and Monitoring System.
Implements CSV/DB logging, performance tracking, and alert system.
"""

import pandas as pd
import numpy as np
import csv
import json
import sqlite3
from typing import Dict, List, Optional
import logging
from datetime import datetime, timedelta
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class TradeLogger:
    """
    Comprehensive trade logging system that tracks:
    - Individual trade details
    - Performance metrics
    - Risk metrics
    - Market conditions at trade time
    """
    
    def __init__(self, config: Dict):
        """
        Initialize trade logger.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        
        # File paths
        self.log_dir = Path(config.get('log_directory', 'logs'))
        self.log_dir.mkdir(exist_ok=True)
        
        self.csv_file = self.log_dir / config.get('csv_filename', 'trades.csv')
        self.db_file = self.log_dir / config.get('db_filename', 'trading_bot.db')
        self.performance_file = self.log_dir / config.get('performance_filename', 'performance.json')
        
        # Logging options
        self.log_to_csv = config.get('log_to_csv', True)
        self.log_to_db = config.get('log_to_db', True)
        self.log_market_conditions = config.get('log_market_conditions', True)
        
        # Initialize storage
        self._initialize_csv()
        self._initialize_db()
        
        # Performance tracking
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0.0,
            'total_pnl': 0.0,
            'total_fees': 0.0,
            'max_drawdown': 0.0,
            'max_profit': 0.0,
            'consecutive_wins': 0,
            'consecutive_losses': 0,
            'current_streak': 0,
            'best_trade': 0.0,
            'worst_trade': 0.0,
            'average_win': 0.0,
            'average_loss': 0.0,
            'profit_factor': 0.0,
            'sharpe_ratio': 0.0,
            'start_time': datetime.now(),
            'last_update': datetime.now()
        }
        
        self._load_performance_metrics()
        
        logger.info("Trade Logger initialized")
    
    def _initialize_csv(self):
        """Initialize CSV file with headers."""
        if not self.csv_file.exists():
            headers = [
                'timestamp', 'symbol', 'side', 'action', 'quantity', 'price',
                'pnl', 'pnl_percentage', 'fees', 'leverage', 'entry_price',
                'exit_price', 'duration_minutes', 'strategy', 'reason',
                'market_volatility', 'market_volume_ratio', 'rsi', 'ema_fast',
                'ema_slow', 'macd', 'portfolio_value', 'position_size_pct',
                'risk_reward_ratio', 'max_profit_during_trade', 'max_loss_during_trade'
            ]
            
            with open(self.csv_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(headers)
            
            logger.info(f"CSV trade log initialized: {self.csv_file}")
    
    def _initialize_db(self):
        """Initialize SQLite database."""
        if not self.log_to_db:
            return
        
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # Create trades table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                action TEXT NOT NULL,
                quantity REAL NOT NULL,
                price REAL NOT NULL,
                pnl REAL,
                pnl_percentage REAL,
                fees REAL,
                leverage INTEGER,
                entry_price REAL,
                exit_price REAL,
                duration_minutes REAL,
                strategy TEXT,
                reason TEXT,
                market_conditions TEXT,
                technical_indicators TEXT,
                portfolio_value REAL,
                position_size_pct REAL,
                risk_metrics TEXT
            )
        ''')
        
        # Create performance table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_snapshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                total_trades INTEGER,
                winning_trades INTEGER,
                losing_trades INTEGER,
                total_pnl REAL,
                win_rate REAL,
                profit_factor REAL,
                sharpe_ratio REAL,
                max_drawdown REAL,
                portfolio_value REAL,
                metrics_json TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info(f"Database initialized: {self.db_file}")
    
    def log_trade(self, trade_data: Dict):
        """
        Log a trade with comprehensive details.
        
        Args:
            trade_data: Dictionary containing trade information
        """
        # Ensure required fields
        required_fields = ['timestamp', 'symbol', 'side', 'action', 'quantity', 'price']
        for field in required_fields:
            if field not in trade_data:
                logger.error(f"Missing required field in trade data: {field}")
                return
        
        # Add calculated fields
        trade_data['timestamp'] = trade_data.get('timestamp', datetime.now().isoformat())
        
        # Log to CSV
        if self.log_to_csv:
            self._log_to_csv(trade_data)
        
        # Log to database
        if self.log_to_db:
            self._log_to_db(trade_data)
        
        # Update performance metrics
        if trade_data['action'] in ['CLOSE', 'CLOSE_FULL', 'CLOSE_PARTIAL']:
            self._update_performance_metrics(trade_data)
        
        logger.info(f"Trade logged: {trade_data['action']} {trade_data['symbol']} "
                   f"{trade_data['quantity']:.6f} at {trade_data['price']:.6f}")
    
    def _log_to_csv(self, trade_data: Dict):
        """Log trade to CSV file."""
        try:
            with open(self.csv_file, 'a', newline='') as f:
                writer = csv.writer(f)
                
                row = [
                    trade_data.get('timestamp', ''),
                    trade_data.get('symbol', ''),
                    trade_data.get('side', ''),
                    trade_data.get('action', ''),
                    trade_data.get('quantity', 0),
                    trade_data.get('price', 0),
                    trade_data.get('pnl', 0),
                    trade_data.get('pnl_percentage', 0),
                    trade_data.get('fees', 0),
                    trade_data.get('leverage', 1),
                    trade_data.get('entry_price', 0),
                    trade_data.get('exit_price', 0),
                    trade_data.get('duration_minutes', 0),
                    trade_data.get('strategy', ''),
                    trade_data.get('reason', ''),
                    trade_data.get('market_volatility', 0),
                    trade_data.get('market_volume_ratio', 0),
                    trade_data.get('rsi', 0),
                    trade_data.get('ema_fast', 0),
                    trade_data.get('ema_slow', 0),
                    trade_data.get('macd', 0),
                    trade_data.get('portfolio_value', 0),
                    trade_data.get('position_size_pct', 0),
                    trade_data.get('risk_reward_ratio', 0),
                    trade_data.get('max_profit_during_trade', 0),
                    trade_data.get('max_loss_during_trade', 0)
                ]
                
                writer.writerow(row)
                
        except Exception as e:
            logger.error(f"Error logging to CSV: {e}")
    
    def _log_to_db(self, trade_data: Dict):
        """Log trade to SQLite database."""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Serialize complex data
            market_conditions = json.dumps(trade_data.get('market_conditions', {}))
            technical_indicators = json.dumps(trade_data.get('technical_indicators', {}))
            risk_metrics = json.dumps(trade_data.get('risk_metrics', {}))
            
            cursor.execute('''
                INSERT INTO trades (
                    timestamp, symbol, side, action, quantity, price, pnl, pnl_percentage,
                    fees, leverage, entry_price, exit_price, duration_minutes, strategy,
                    reason, market_conditions, technical_indicators, portfolio_value,
                    position_size_pct, risk_metrics
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_data.get('timestamp'),
                trade_data.get('symbol'),
                trade_data.get('side'),
                trade_data.get('action'),
                trade_data.get('quantity', 0),
                trade_data.get('price', 0),
                trade_data.get('pnl', 0),
                trade_data.get('pnl_percentage', 0),
                trade_data.get('fees', 0),
                trade_data.get('leverage', 1),
                trade_data.get('entry_price', 0),
                trade_data.get('exit_price', 0),
                trade_data.get('duration_minutes', 0),
                trade_data.get('strategy', ''),
                trade_data.get('reason', ''),
                market_conditions,
                technical_indicators,
                trade_data.get('portfolio_value', 0),
                trade_data.get('position_size_pct', 0),
                risk_metrics
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error logging to database: {e}")
    
    def _update_performance_metrics(self, trade_data: Dict):
        """Update performance metrics based on completed trade."""
        pnl = trade_data.get('pnl', 0)
        
        # Update basic counts
        self.performance_metrics['total_trades'] += 1
        self.performance_metrics['total_pnl'] += pnl
        self.performance_metrics['total_fees'] += trade_data.get('fees', 0)
        
        # Update win/loss tracking
        if pnl > 0:
            self.performance_metrics['winning_trades'] += 1
            self.performance_metrics['consecutive_wins'] += 1
            self.performance_metrics['consecutive_losses'] = 0
            self.performance_metrics['current_streak'] = self.performance_metrics['consecutive_wins']
            
            if pnl > self.performance_metrics['best_trade']:
                self.performance_metrics['best_trade'] = pnl
        else:
            self.performance_metrics['losing_trades'] += 1
            self.performance_metrics['consecutive_losses'] += 1
            self.performance_metrics['consecutive_wins'] = 0
            self.performance_metrics['current_streak'] = -self.performance_metrics['consecutive_losses']
            
            if pnl < self.performance_metrics['worst_trade']:
                self.performance_metrics['worst_trade'] = pnl
        
        # Update max profit/drawdown
        if self.performance_metrics['total_pnl'] > self.performance_metrics['max_profit']:
            self.performance_metrics['max_profit'] = self.performance_metrics['total_pnl']
        
        drawdown = self.performance_metrics['max_profit'] - self.performance_metrics['total_pnl']
        if drawdown > self.performance_metrics['max_drawdown']:
            self.performance_metrics['max_drawdown'] = drawdown
        
        # Calculate derived metrics
        self._calculate_derived_metrics()
        
        # Save metrics
        self._save_performance_metrics()
        
        self.performance_metrics['last_update'] = datetime.now()
    
    def _calculate_derived_metrics(self):
        """Calculate derived performance metrics."""
        total_trades = self.performance_metrics['total_trades']
        winning_trades = self.performance_metrics['winning_trades']
        losing_trades = self.performance_metrics['losing_trades']

        if total_trades > 0:
            # Win rate
            self.performance_metrics['win_rate'] = winning_trades / total_trades

    def _load_performance_metrics(self):
        """Load performance metrics from file."""
        if self.performance_file.exists():
            try:
                with open(self.performance_file, 'r') as f:
                    metrics = json.load(f)
                    # Convert datetime strings back to datetime objects
                    if 'start_time' in metrics and isinstance(metrics['start_time'], str):
                        metrics['start_time'] = datetime.fromisoformat(metrics['start_time'])
                    if 'last_update' in metrics and isinstance(metrics['last_update'], str):
                        metrics['last_update'] = datetime.fromisoformat(metrics['last_update'])
                    self.performance_metrics.update(metrics)
                logger.info("Performance metrics loaded from file")
            except Exception as e:
                logger.error(f"Error loading performance metrics: {e}")

    def _save_performance_metrics(self):
        """Save performance metrics to file."""
        try:
            # Convert datetime objects to strings for JSON serialization
            metrics_to_save = self.performance_metrics.copy()
            if isinstance(metrics_to_save.get('start_time'), datetime):
                metrics_to_save['start_time'] = metrics_to_save['start_time'].isoformat()
            if isinstance(metrics_to_save.get('last_update'), datetime):
                metrics_to_save['last_update'] = metrics_to_save['last_update'].isoformat()

            with open(self.performance_file, 'w') as f:
                json.dump(metrics_to_save, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving performance metrics: {e}")

    def send_telegram_alert(self, message: str):
        """Send alert via Telegram."""
        if not self.telegram_alerts or not self.telegram_token or not self.telegram_chat_id:
            return

        try:
            url = f"https://api.telegram.org/bot{self.telegram_token}/sendMessage"
            data = {
                'chat_id': self.telegram_chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }

            response = requests.post(url, data=data, timeout=10)
            if response.status_code == 200:
                logger.info("Telegram alert sent successfully")
            else:
                logger.error(f"Failed to send Telegram alert: {response.status_code}")

        except Exception as e:
            logger.error(f"Error sending Telegram alert: {e}")

    def _send_trade_alert(self, trade_data: Dict):
        """Send trade alert notifications."""
        if not (self.telegram_alerts or self.email_alerts):
            return

        # Format alert message
        action = trade_data['action']
        symbol = trade_data['symbol']
        side = trade_data['side']
        quantity = trade_data['quantity']
        price = trade_data['price']
        pnl = trade_data.get('pnl', 0)
        reason = trade_data.get('reason', '')

        if action == 'OPEN':
            message = f"🔄 <b>Position Opened</b>\n"
            message += f"Symbol: {symbol}\n"
            message += f"Side: {side}\n"
            message += f"Quantity: {quantity:.6f}\n"
            message += f"Price: {price:.6f}\n"
            message += f"Reason: {reason}"

        elif action in ['CLOSE', 'CLOSE_FULL']:
            pnl_emoji = "💰" if pnl > 0 else "📉"
            message = f"{pnl_emoji} <b>Position Closed</b>\n"
            message += f"Symbol: {symbol}\n"
            message += f"P&L: {pnl:.4f} ({(pnl/trade_data.get('entry_price', price)*100):.2f}%)\n"
            message += f"Price: {price:.6f}\n"
            message += f"Reason: {reason}"

        else:
            return  # Don't send alerts for other actions

        # Add performance summary
        message += f"\n\n📊 <b>Performance</b>\n"
        message += f"Total Trades: {self.performance_metrics['total_trades']}\n"
        message += f"Win Rate: {self.performance_metrics['win_rate']:.1%}\n"
        message += f"Total P&L: {self.performance_metrics['total_pnl']:.4f}"

        # Send via Telegram
        if self.telegram_alerts:
            self.send_telegram_alert(message)

    def get_performance_summary(self) -> Dict:
        """Get comprehensive performance summary."""
        return {
            'total_trades': self.performance_metrics['total_trades'],
            'winning_trades': self.performance_metrics['winning_trades'],
            'losing_trades': self.performance_metrics['losing_trades'],
            'win_rate': self.performance_metrics['win_rate'],
            'total_pnl': self.performance_metrics['total_pnl'],
            'total_fees': self.performance_metrics['total_fees'],
            'net_pnl': self.performance_metrics['total_pnl'] - self.performance_metrics['total_fees'],
            'max_drawdown': self.performance_metrics['max_drawdown'],
            'max_profit': self.performance_metrics['max_profit'],
            'current_streak': self.performance_metrics['current_streak'],
            'best_trade': self.performance_metrics['best_trade'],
            'worst_trade': self.performance_metrics['worst_trade'],
            'consecutive_wins': self.performance_metrics['consecutive_wins'],
            'consecutive_losses': self.performance_metrics['consecutive_losses'],
            'start_time': self.performance_metrics['start_time'],
            'last_update': self.performance_metrics['last_update']
        }
