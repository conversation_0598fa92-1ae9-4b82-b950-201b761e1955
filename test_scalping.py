"""
Test scalping strategy functionality.
"""

import os
import sys
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from strategies.scalping_strategy import ScalpingStrategy
from utils import load_config

def test_scalping_strategy():
    """Test the scalping strategy."""
    print("🎯 Testing Scalping Strategy")
    print("=" * 50)

    try:
        # Initialize client
        client = BinanceClient(testnet=True)

        # Load config
        config = load_config('config/config.yaml')
        scalp_config = config.get('strategies', {}).get('scalping', {})

        # Initialize scalping strategy
        scalping = ScalpingStrategy(client, scalp_config)
        print("✅ Scalping strategy initialized")

        # Test with a symbol
        symbol = 'BTCUSDT'
        print(f"\n🔍 Testing scalping analysis for {symbol}...")

        # Get market data
        data = client.get_historical_klines(symbol, '5m', 100)
        if data.empty:
            print("❌ No market data retrieved")
            return False

        print(f"✅ Retrieved {len(data)} candles")

        # Test abstract methods implementation
        print("\n🔧 Testing abstract methods...")

        # Test calculate_indicators
        data_with_indicators = scalping.calculate_indicators(data.copy())
        expected_indicators = ['EMA_fast', 'EMA_slow', 'RSI', 'BB_upper', 'BB_lower', 'Volume_ratio']

        for indicator in expected_indicators:
            if indicator in data_with_indicators.columns:
                print(f"✅ {indicator} calculated successfully")
            else:
                print(f"❌ {indicator} missing")

        # Test generate_signal
        signal = scalping.generate_signal(data, symbol)
        print(f"✅ generate_signal returned: {signal}")

        # Test market condition assessment
        mock_market_analysis = {
            'healthy': False,
            'reason': 'Sideways market with unclear direction'
        }

        mock_symbol_conditions = {
            'healthy': False,
            'reason': 'Sideways market',
            'sideways_market': True
        }

        # Test if scalping should activate
        should_activate = scalping.should_activate(mock_market_analysis, mock_symbol_conditions)
        print(f"✅ Scalping activation test: {should_activate}")

        if should_activate:
            print("🎯 Scalping would activate due to unclear market conditions")

            # Test scalping analysis
            scalp_data = client.get_historical_klines(symbol, '1m', 50)
            if not scalp_data.empty:
                analysis = scalping.analyze_scalp_opportunity(symbol, scalp_data)
                print(f"✅ Scalping analysis completed:")
                print(f"   Signal: {analysis['signal']}")
                print(f"   Confidence: {analysis['confidence']:.2f}")
                print(f"   Reason: {analysis['reason']}")

                # Test signal generation
                signals = scalping.generate_signals(symbol, data)
                print(f"✅ Generated {len(signals)} scalping signals")

                for signal in signals:
                    print(f"   📊 {signal['action']} {signal['symbol']} @ {signal['price']:.6f}")
                    print(f"      TP: {signal['take_profit']:.6f}, SL: {signal['stop_loss']:.6f}")
                    print(f"      Confidence: {signal['confidence']:.2f}")
            else:
                print("❌ No 1m data for scalping analysis")
        else:
            print("ℹ️  Scalping would not activate - market conditions are clear")

        print("\n✅ Scalping strategy test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Error testing scalping strategy: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scalping_targets():
    """Test scalping target calculations."""
    print("\n🎯 Testing Scalping Target Calculations")
    print("=" * 50)
    
    try:
        config = {'min_profit_pct': 0.3, 'max_loss_pct': 0.15}
        client = BinanceClient(testnet=True)
        scalping = ScalpingStrategy(client, config)
        
        # Test BUY targets
        entry_price = 50000.0
        tp_buy, sl_buy = scalping.calculate_scalp_targets(entry_price, 'BUY')
        
        print(f"BUY @ {entry_price:.2f}:")
        print(f"  Take Profit: {tp_buy:.2f} (+{((tp_buy/entry_price-1)*100):.2f}%)")
        print(f"  Stop Loss: {sl_buy:.2f} ({((sl_buy/entry_price-1)*100):.2f}%)")
        
        # Test SELL targets
        tp_sell, sl_sell = scalping.calculate_scalp_targets(entry_price, 'SELL')
        
        print(f"\nSELL @ {entry_price:.2f}:")
        print(f"  Take Profit: {tp_sell:.2f} ({((tp_sell/entry_price-1)*100):.2f}%)")
        print(f"  Stop Loss: {sl_sell:.2f} (+{((sl_sell/entry_price-1)*100):.2f}%)")
        
        # Verify targets are correct
        buy_profit = (tp_buy / entry_price - 1) * 100
        buy_loss = (1 - sl_buy / entry_price) * 100
        
        if abs(buy_profit - 0.3) < 0.01 and abs(buy_loss - 0.15) < 0.01:
            print("\n✅ Target calculations are correct!")
            return True
        else:
            print(f"\n❌ Target calculations incorrect: profit={buy_profit:.3f}%, loss={buy_loss:.3f}%")
            return False
            
    except Exception as e:
        print(f"❌ Error testing scalping targets: {e}")
        return False

def main():
    """Run all scalping tests."""
    print("🧪 Scalping Strategy Test Suite")
    print("=" * 60)
    
    tests = [
        ("Scalping Strategy", test_scalping_strategy),
        ("Scalping Targets", test_scalping_targets)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All scalping tests passed!")
        print("\n🚀 Scalping Strategy Features:")
        print("✅ Activates during unclear/sideways market conditions")
        print("✅ Uses 1-minute timeframe for quick entries/exits")
        print("✅ Targets 0.3% profit with 0.15% stop loss")
        print("✅ Maximum 3 scalp positions at once")
        print("✅ 2% position size per scalp trade")
        print("✅ Automatic exit after 5 minutes maximum")
        print("✅ EMA crossover + RSI + Bollinger Band signals")
        print("✅ Volume spike confirmation required")
    else:
        print("⚠️  Some scalping tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
