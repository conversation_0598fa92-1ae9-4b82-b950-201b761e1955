"""
Dynamic Altcoin Scanner for Binance Futures.
Automatically fetches all available altcoin pairs excluding major coins as per context.txt.
"""

import logging
from typing import List, Dict, Set
import pandas as pd
from binance_client import BinanceClient

logger = logging.getLogger(__name__)

class AltcoinScanner:
    """
    Scanner that dynamically fetches all available altcoin pairs from Binance Futures.
    Excludes major coins (BTC, ETH, BNB) and stablecoins as per context.txt guidelines.
    """
    
    def __init__(self, binance_client: BinanceClient, config: Dict = None):
        """
        Initialize altcoin scanner.
        
        Args:
            binance_client: Binance API client
            config: Scanner configuration
        """
        self.client = binance_client
        self.config = config or {}
        
        # MINIMAL EXCLUSIONS - INCLUDE MORE ALTCOINS
        self.excluded_base_assets = {
            # Only exclude stablecoins and fiat currencies
            'USDT', 'USDC', 'BUSD', 'TUSD', 'DAI', 'FDUSD',  # Stablecoins
            'EUR', 'GBP', 'AUD', 'TRY', 'BRL', 'RUB'  # Fiat currencies
            # Removed BTC, ETH, BNB to include ALL altcoins including major ones
        }
        
        # MINIMAL SYMBOL EXCLUSIONS - INCLUDE MORE ALTCOINS
        self.excluded_symbols = set(self.config.get('excluded_symbols', [
            # Only exclude stablecoin pairs
            'USDCUSDT', 'BUSDUSDT', 'TUSDUSDT'  # Stablecoin pairs
            # Removed BTCUSDT, ETHUSDT, BNBUSDT to include ALL altcoins
        ]))

        # Add known invalid testnet symbols
        self.excluded_symbols.update([
            'MERLUSDT', 'CUSDT', 'VELVETUSDT', 'ZKJUSDT', 'BANANAS31USDT'
        ])
        
        # MINIMAL REQUIREMENTS - SCAN ALL ALTCOINS
        self.min_volume_24h = self.config.get('min_volume_24h', 10000)   # Reduced to $10K (was $100K)
        self.min_price = self.config.get('min_price', 0.000001)          # Reduced to $0.000001 (was $0.0001)
        self.max_price = self.config.get('max_price', 100000)            # Increased to $100K (was $10K)
        self.quote_asset = self.config.get('quote_asset', 'USDT')        # Only USDT pairs
        
        # Cache
        self.cached_symbols = []
        self.last_scan_time = None
        self.scan_interval_hours = self.config.get('scan_interval_hours', 24)  # Rescan daily
        
        logger.info("Altcoin Scanner initialized")
    
    def get_all_futures_symbols(self) -> List[Dict]:
        """
        Get all available futures symbols from Binance.
        
        Returns:
            List of symbol information dictionaries
        """
        try:
            # Get exchange info for futures
            exchange_info = self.client.client.futures_exchange_info()
            
            if not exchange_info or 'symbols' not in exchange_info:
                logger.error("Failed to get futures exchange info")
                return []
            
            symbols = exchange_info['symbols']
            logger.info(f"Retrieved {len(symbols)} futures symbols from Binance")
            
            return symbols
            
        except Exception as e:
            logger.error(f"Error getting futures symbols: {e}")
            return []
    
    def get_24h_ticker_stats(self) -> Dict[str, Dict]:
        """
        Get 24h ticker statistics for all symbols.

        Returns:
            Dictionary mapping symbol to ticker stats
        """
        try:
            # Use the correct method name for futures 24hr ticker
            tickers = self.client.client.futures_ticker()

            if not tickers:
                logger.error("Failed to get 24h ticker stats")
                return {}

            # Convert to dictionary for easy lookup
            ticker_dict = {ticker['symbol']: ticker for ticker in tickers}

            logger.info(f"Retrieved 24h stats for {len(ticker_dict)} symbols")
            return ticker_dict

        except Exception as e:
            logger.error(f"Error getting 24h ticker stats: {e}")
            return {}
    
    def filter_altcoins(self, symbols: List[Dict], ticker_stats: Dict[str, Dict]) -> List[str]:
        """
        Filter symbols to get only qualifying altcoins.
        
        Args:
            symbols: List of symbol info from exchange
            ticker_stats: 24h ticker statistics
            
        Returns:
            List of qualifying altcoin symbols
        """
        qualifying_altcoins = []
        
        for symbol_info in symbols:
            symbol = symbol_info['symbol']

            # Extract base and quote assets from symbol name
            if symbol.endswith('USDT'):
                base_asset = symbol[:-4]  # Remove 'USDT' suffix
                quote_asset = 'USDT'
            else:
                # Skip non-USDT pairs
                continue

            status = symbol_info.get('status', 'TRADING')
            
            # Skip if not active
            if status != 'TRADING':
                continue
            
            # Only USDT pairs (as per context.txt focus)
            if quote_asset != self.quote_asset:
                continue
            
            # Exclude major coins and stablecoins
            if base_asset in self.excluded_base_assets:
                continue
            
            # Exclude specific symbols
            if symbol in self.excluded_symbols:
                continue
            
            # Get ticker stats for volume and price filtering
            ticker = ticker_stats.get(symbol)
            if not ticker:
                continue
            
            try:
                # Volume filter (minimum 24h volume)
                volume_24h = float(ticker.get('quoteVolume', 0))
                if volume_24h < self.min_volume_24h:
                    continue
                
                # Price filter
                price = float(ticker.get('lastPrice', 0))
                if price < self.min_price or price > self.max_price:
                    continue
                
                # Additional filters - RELAXED FOR MORE ALTCOINS
                price_change_percent = float(ticker.get('priceChangePercent', 0))

                # Skip only if price change is extremely extreme (might be delisted)
                if abs(price_change_percent) > 90:  # Increased from 50% to 90% - include volatile altcoins
                    continue
                
                qualifying_altcoins.append(symbol)
                
            except (ValueError, TypeError) as e:
                logger.warning(f"Error processing ticker data for {symbol}: {e}")
                continue
        
        return qualifying_altcoins
    
    def scan_altcoins(self, force_rescan: bool = False) -> List[str]:
        """
        Scan for all qualifying altcoins.
        
        Args:
            force_rescan: Force a new scan even if cache is valid
            
        Returns:
            List of qualifying altcoin symbols
        """
        # Check if we need to rescan
        if not force_rescan and self.cached_symbols and self.last_scan_time:
            time_since_scan = pd.Timestamp.now() - self.last_scan_time
            if time_since_scan.total_seconds() < (self.scan_interval_hours * 3600):
                logger.info(f"Using cached altcoin list ({len(self.cached_symbols)} symbols)")
                return self.cached_symbols
        
        logger.info("Scanning for available altcoins...")
        
        # Get valid symbols from Binance client first
        try:
            valid_symbols = self.client.get_valid_symbols()
            logger.info(f"📊 Found {len(valid_symbols)} valid symbols from Binance")

            # Convert to symbol info format for compatibility
            all_symbols = []
            for symbol in valid_symbols:
                if symbol.endswith('USDT'):
                    all_symbols.append({
                        'symbol': symbol,
                        'status': 'TRADING',
                        'contractType': 'PERPETUAL'
                    })

            if not all_symbols:
                logger.error("No valid USDT symbols found")
                return self.cached_symbols

        except Exception as e:
            logger.error(f"Error getting valid symbols: {e}")
            # Fallback to original method
            all_symbols = self.get_all_futures_symbols()
            if not all_symbols:
                logger.error("No symbols retrieved from exchange")
                return self.cached_symbols  # Return cached if available
        
        # Get 24h ticker stats
        ticker_stats = self.get_24h_ticker_stats()
        if not ticker_stats:
            logger.error("No ticker stats retrieved")
            return self.cached_symbols  # Return cached if available
        
        # Filter for qualifying altcoins
        altcoins = self.filter_altcoins(all_symbols, ticker_stats)
        
        # Sort by 24h volume (descending)
        altcoins_with_volume = []
        for symbol in altcoins:
            ticker = ticker_stats.get(symbol, {})
            volume = float(ticker.get('quoteVolume', 0))
            altcoins_with_volume.append((symbol, volume))
        
        # Sort by volume and extract symbols
        altcoins_with_volume.sort(key=lambda x: x[1], reverse=True)
        sorted_altcoins = [symbol for symbol, volume in altcoins_with_volume]
        
        # Update cache
        self.cached_symbols = sorted_altcoins
        self.last_scan_time = pd.Timestamp.now()
        
        logger.info(f"Found {len(sorted_altcoins)} qualifying altcoins")
        logger.info(f"Top 10 by volume: {sorted_altcoins[:10]}")
        
        return sorted_altcoins
    
    def get_altcoin_info(self, symbol: str, ticker_stats: Dict[str, Dict] = None) -> Dict:
        """
        Get detailed information about a specific altcoin.
        
        Args:
            symbol: Altcoin symbol
            ticker_stats: Optional ticker stats (will fetch if not provided)
            
        Returns:
            Dictionary with altcoin information
        """
        if not ticker_stats:
            ticker_stats = self.get_24h_ticker_stats()
        
        ticker = ticker_stats.get(symbol, {})
        
        return {
            'symbol': symbol,
            'price': float(ticker.get('lastPrice', 0)),
            'volume_24h': float(ticker.get('quoteVolume', 0)),
            'price_change_24h': float(ticker.get('priceChangePercent', 0)),
            'high_24h': float(ticker.get('highPrice', 0)),
            'low_24h': float(ticker.get('lowPrice', 0)),
            'trades_24h': int(ticker.get('count', 0))
        }
    
    def get_top_altcoins(self, limit: int = None) -> List[str]:
        """
        Get ALL altcoins or top altcoins by volume.

        Args:
            limit: Maximum number of altcoins to return (None = ALL altcoins)

        Returns:
            List of top altcoin symbols
        """
        all_altcoins = self.scan_altcoins()

        # Return ALL altcoins if limit is None, otherwise return limited list
        if limit is None:
            logger.info(f"🪙 Returning ALL {len(all_altcoins)} altcoins for trading")
            return all_altcoins
        else:
            logger.info(f"🪙 Returning top {limit} altcoins out of {len(all_altcoins)} available")
            return all_altcoins[:limit]
    
    def get_altcoin_summary(self) -> Dict:
        """
        Get summary of available altcoins.
        
        Returns:
            Summary dictionary
        """
        altcoins = self.scan_altcoins()
        ticker_stats = self.get_24h_ticker_stats()
        
        if not altcoins:
            return {'error': 'No altcoins found'}
        
        # Calculate summary statistics
        total_volume = 0
        price_changes = []
        
        for symbol in altcoins:
            ticker = ticker_stats.get(symbol, {})
            volume = float(ticker.get('quoteVolume', 0))
            price_change = float(ticker.get('priceChangePercent', 0))
            
            total_volume += volume
            price_changes.append(price_change)
        
        return {
            'total_altcoins': len(altcoins),
            'total_volume_24h': total_volume,
            'avg_price_change_24h': sum(price_changes) / len(price_changes) if price_changes else 0,
            'top_10_symbols': altcoins[:10],
            'last_scan': self.last_scan_time.isoformat() if self.last_scan_time else None,
            'excluded_assets': list(self.excluded_base_assets),
            'min_volume_filter': self.min_volume_24h,
            'price_range': f"{self.min_price} - {self.max_price}"
        }
    
    def export_altcoin_list(self, filename: str = 'altcoins.txt') -> bool:
        """
        Export altcoin list to file.
        
        Args:
            filename: Output filename
            
        Returns:
            True if successful
        """
        try:
            altcoins = self.scan_altcoins()
            
            with open(filename, 'w') as f:
                f.write(f"# Binance Futures Altcoins - Generated {pd.Timestamp.now()}\n")
                f.write(f"# Total: {len(altcoins)} symbols\n")
                f.write(f"# Excluded: {', '.join(self.excluded_base_assets)}\n")
                f.write(f"# Min Volume: ${self.min_volume_24h:,}\n\n")
                
                for symbol in altcoins:
                    f.write(f"{symbol}\n")
            
            logger.info(f"Exported {len(altcoins)} altcoins to {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting altcoin list: {e}")
            return False
