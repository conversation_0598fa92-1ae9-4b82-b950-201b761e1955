"""
Verification script to ensure the bot is configured for TESTNET ONLY.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from utils import load_config

def verify_testnet_configuration():
    """Verify that the bot is configured for testnet only."""
    print("🔍 Verifying TESTNET-ONLY Configuration")
    print("=" * 60)
    
    try:
        # Check config file
        config = load_config('config/config.yaml')
        trading_mode = config.get('trading', {}).get('mode', 'unknown')
        
        print("📊 Configuration Check:")
        print(f"   Trading mode: {trading_mode}")
        
        if trading_mode == 'testnet':
            print("   ✅ Config set to TESTNET mode")
        else:
            print(f"   ❌ Config NOT set to testnet (found: {trading_mode})")
            return False
        
        # Check client initialization
        print("\n🔧 Client Initialization Check:")
        client = BinanceClient(testnet=True)
        
        # Verify testnet connection
        try:
            account = client.futures_get_account_info()
            if account:
                print("   ✅ Successfully connected to TESTNET")
                
                # Check if this is actually testnet
                assets = account.get('assets', [])
                usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
                
                if usdt_asset:
                    balance = float(usdt_asset.get('availableBalance', 0))
                    print(f"   💰 TESTNET Balance: {balance:.2f} USDT")
                    
                    # Testnet usually has high balances (like 10,000+ USDT)
                    if balance > 1000:
                        print("   ✅ High balance confirms this is TESTNET")
                    else:
                        print("   ⚠️  Low balance - verify this is testnet")
                else:
                    print("   ⚠️  No USDT asset found")
            else:
                print("   ❌ Could not connect to account")
                return False
                
        except Exception as e:
            print(f"   ❌ Connection error: {e}")
            return False
        
        # Check API endpoints
        print("\n🌐 API Endpoint Check:")
        if hasattr(client.client, 'API_URL'):
            api_url = client.client.API_URL
            print(f"   API URL: {api_url}")
            
            if 'testnet' in api_url.lower():
                print("   ✅ Using TESTNET API endpoint")
            else:
                print("   ❌ NOT using testnet endpoint!")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying testnet configuration: {e}")
        return False

def verify_no_paper_trading():
    """Verify that paper trading logic has been removed."""
    print("\n🔍 Verifying No Paper Trading Logic")
    print("=" * 60)
    
    try:
        # Check main.py for paper trading references
        with open('main.py', 'r') as f:
            content = f.read()
        
        paper_references = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            if 'paper' in line.lower() and 'trade' in line.lower():
                if not line.strip().startswith('#'):  # Ignore comments
                    paper_references.append(f"Line {i}: {line.strip()}")
        
        if paper_references:
            print("❌ Found paper trading references:")
            for ref in paper_references:
                print(f"   {ref}")
            return False
        else:
            print("✅ No paper trading logic found")
            return True
            
    except Exception as e:
        print(f"❌ Error checking for paper trading: {e}")
        return False

def show_testnet_benefits():
    """Show the benefits of using testnet."""
    print("\n🎯 TESTNET Benefits")
    print("=" * 60)
    
    print("✅ SAFETY:")
    print("   • No real money at risk")
    print("   • Test strategies without financial loss")
    print("   • Perfect for learning and development")
    print()
    
    print("✅ REALISM:")
    print("   • Real Binance API responses")
    print("   • Actual order execution simulation")
    print("   • Real market data and prices")
    print()
    
    print("✅ FEATURES:")
    print("   • Full futures trading simulation")
    print("   • Leverage testing (up to 125x)")
    print("   • Position management")
    print("   • PnL tracking")
    print()
    
    print("✅ DEVELOPMENT:")
    print("   • Test new strategies safely")
    print("   • Debug trading logic")
    print("   • Optimize parameters")
    print("   • Validate performance")

def main():
    """Run testnet verification."""
    print("🧪 TESTNET-ONLY Verification Suite")
    print("=" * 70)
    
    config_ok = verify_testnet_configuration()
    no_paper = verify_no_paper_trading()
    
    print("\n" + "=" * 70)
    
    if config_ok and no_paper:
        print("🎉 VERIFICATION PASSED!")
        print("✅ Bot is configured for TESTNET ONLY")
        print("✅ No paper trading logic found")
        print("✅ Safe to run without real money risk")
        
        show_testnet_benefits()
        
        print("\n🚀 Ready to Start:")
        print("   python main.py")
        
    else:
        print("❌ VERIFICATION FAILED!")
        if not config_ok:
            print("   • Configuration issues found")
        if not no_paper:
            print("   • Paper trading logic still present")
        
        print("\n🔧 Fix the issues above before running the bot")
    
    return config_ok and no_paper

if __name__ == "__main__":
    main()
