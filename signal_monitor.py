#!/usr/bin/env python3
"""
Real-time signal monitoring for the trading bot.
"""

import sys
import os
import time
from datetime import datetime

# Add src to path
sys.path.append('src')

def test_strategy_signals():
    """Test what signals the strategy would generate right now."""
    print("🔍 REAL-TIME SIGNAL ANALYSIS")
    print("=" * 50)
    
    try:
        from binance_client import BinanceClient
        from strategies.advanced_ai_strategy import AdvancedAIStrategy
        from utils import load_config
        
        # Load config
        config = load_config('config/config.yaml')
        
        # Initialize client and strategy
        client = BinanceClient()
        ai_config = config.get('strategies', {}).get('advanced_ai', {})
        ai_config['scanner'] = {
            'base_currency': 'USDT',
            'min_volume_24h': 1000000,
            'min_confidence': 70,
            'top_n_tokens': 20
        }
        ai_config['initial_symbols'] = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        
        strategy = AdvancedAIStrategy(client, ai_config)
        
        # Test symbols
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'SOLUSDT']
        
        print(f"📊 Analyzing {len(test_symbols)} symbols...")
        print()
        
        for symbol in test_symbols:
            try:
                print(f"🔍 {symbol}:")
                print("-" * 20)
                
                # Get market data
                data = client.get_historical_klines(symbol, '1h', 100)
                if data.empty:
                    print("  ❌ No data available")
                    continue
                
                # Generate signal
                signal = strategy.generate_signal(data, symbol)
                current_price = client.get_current_price(symbol)
                
                print(f"  💰 Price: ${current_price:,.4f}")
                print(f"  📈 Signal: {signal}")
                
                # Get additional analysis
                if hasattr(strategy, 'mtf'):
                    try:
                        analysis = strategy.mtf.analyze_symbol_all_timeframes(symbol)
                        if analysis and 'overall_recommendation' in analysis:
                            rec = analysis['overall_recommendation']
                            confidence = rec.get('confidence', 0)
                            action = rec.get('action', 'UNKNOWN')
                            print(f"  🎯 AI Recommendation: {action}")
                            print(f"  📊 Confidence: {confidence:.1f}%")
                            
                            if confidence >= 70:
                                print(f"  ✅ HIGH CONFIDENCE - Would trade!")
                            else:
                                print(f"  ⚠️  Low confidence - Waiting...")
                    except Exception as e:
                        print(f"  ⚠️  Analysis error: {str(e)[:50]}...")
                
                print()
                
            except Exception as e:
                print(f"  ❌ Error: {str(e)[:50]}...")
                print()
        
        print("🤖 TRADING CRITERIA:")
        print("-" * 30)
        print("✓ Confidence must be ≥ 70%")
        print("✓ Multi-timeframe alignment required")
        print("✓ Volume confirmation needed")
        print("✓ Risk management checks passed")
        print()
        print("💡 The bot will automatically trade when ALL criteria are met!")
        
    except Exception as e:
        print(f"❌ Error running signal analysis: {e}")
        print()
        print("🔧 Troubleshooting:")
        print("- Make sure the bot is running")
        print("- Check API connection")
        print("- Verify configuration files")

def monitor_live_signals():
    """Monitor signals in real-time."""
    print("🔄 LIVE SIGNAL MONITORING")
    print("=" * 50)
    print("Monitoring every 30 seconds... Press Ctrl+C to stop")
    print()
    
    try:
        while True:
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"⏰ {current_time} - Checking signals...")
            
            test_strategy_signals()
            
            print("⏳ Waiting 30 seconds for next check...")
            print("=" * 50)
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n👋 Signal monitoring stopped.")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--live":
        monitor_live_signals()
    else:
        test_strategy_signals()
