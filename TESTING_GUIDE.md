# 🧪 Complete Testing Guide for Crypto Trading Bot

This guide will walk you through testing the trading bot safely and thoroughly.

## Prerequisites

### 1. Install Python
- Download Python 3.8+ from [python.org](https://python.org)
- During installation, check "Add Python to PATH"
- Verify installation: `python --version`

### 2. Install Git (if needed)
- Download from [git-scm.com](https://git-scm.com)

## Step-by-Step Testing Process

### Phase 1: Environment Setup Test

1. **Navigate to the project directory**:
   ```bash
   cd "c:\Users\<USER>\OneDrive\Pictures\Desktop\trading bot"
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run setup script**:
   ```bash
   python setup.py
   ```

4. **Copy environment file**:
   ```bash
   copy .env.example .env
   ```

### Phase 2: Unit Tests

Run the automated tests to verify core functionality:

```bash
# Install pytest if not already installed
pip install pytest

# Run all tests
pytest tests/ -v

# Run specific test file
pytest tests/test_strategies.py -v
```

**Expected Output**: All tests should pass ✅

### Phase 3: Strategy Testing (No API Required)

Test the trading strategies with sample data:

```bash
# Test SMA strategy
python -c "
import sys, os
sys.path.append('src')
from strategies.sma_crossover import SMACrossoverStrategy
import pandas as pd
import numpy as np

# Create sample data
dates = pd.date_range('2023-01-01', periods=100, freq='1H')
prices = 100 + np.cumsum(np.random.randn(100) * 0.5)
data = pd.DataFrame({
    'open': prices, 'high': prices*1.01, 'low': prices*0.99, 
    'close': prices, 'volume': np.random.randint(1000, 10000, 100)
}, index=dates)

# Test strategy
config = {'short_window': 10, 'long_window': 30}
strategy = SMACrossoverStrategy(config)
signal = strategy.generate_signal(data, 'BTCUSDT')
print(f'Strategy test successful! Signal: {signal}')
"
```

### Phase 4: Configuration Test

Test the configuration system:

```bash
# Test configuration loading
python -c "
import sys, os
sys.path.append('src')
from utils import load_config
config = load_config('config/config.yaml')
print('Configuration loaded successfully!')
print(f'Trading mode: {config.get(\"trading\", {}).get(\"mode\", \"unknown\")}')
print(f'Symbols: {config.get(\"trading\", {}).get(\"symbols\", [])}')
"
```

### Phase 5: Paper Trading Test (Safe Mode)

Before testing with real API, set up paper trading:

1. **Edit .env file** (set these values):
   ```
   TRADING_MODE=paper
   INITIAL_BALANCE=10000
   BINANCE_API_KEY=test_key
   BINANCE_SECRET_KEY=test_secret
   ```

2. **Test paper trading mode**:
   ```bash
   python -c "
   import sys, os
   sys.path.append('src')
   from portfolio import Portfolio
   from risk_management import RiskManager
   
   # Test portfolio
   portfolio = Portfolio(10000, 'USDT')
   portfolio.add_position('BTCUSDT', 'BUY', 0.1, 50000)
   print('Portfolio test successful!')
   print(f'Positions: {len(portfolio.positions)}')
   
   # Test risk management
   risk_config = {'max_position_size': 0.1, 'stop_loss_percentage': 0.02}
   risk_manager = RiskManager(risk_config)
   position_size = risk_manager.calculate_position_size(10000, 50000)
   print(f'Risk management test successful! Position size: {position_size}')
   "
   ```

### Phase 6: API Connection Test (Read-Only)

Test Binance API connection without trading:

1. **Get Binance API keys** (if you don't have them):
   - Go to [Binance](https://binance.com) → Account → API Management
   - Create new API key
   - **Important**: Only enable "Enable Reading" permission for testing
   - Copy API Key and Secret Key

2. **Update .env file** with real API keys:
   ```
   BINANCE_API_KEY=your_real_api_key_here
   BINANCE_SECRET_KEY=your_real_secret_key_here
   TRADING_MODE=paper
   ```

3. **Test API connection**:
   ```bash
   python -c "
   import sys, os
   sys.path.append('src')
   from binance_client import BinanceClient
   
   client = BinanceClient()
   
   # Test getting current price (read-only)
   try:
       price = client.get_current_price('BTCUSDT')
       print(f'API connection successful! BTC price: ${price:,.2f}')
   except Exception as e:
       print(f'API connection failed: {e}')
   
   # Test getting historical data
   try:
       data = client.get_historical_klines('BTCUSDT', '1h', 10)
       print(f'Historical data test successful! Got {len(data)} data points')
   except Exception as e:
       print(f'Historical data test failed: {e}')
   "
   ```

### Phase 7: Backtesting Test

Test the backtesting system with historical data:

```bash
# Run a simple backtest
python -c "
from backtest import Backtester
import sys, os

try:
    backtester = Backtester()
    print('Starting backtest...')
    
    # Run backtest for last 30 days
    from datetime import datetime, timedelta
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    results = backtester.run_backtest(
        strategy_name='sma_crossover',
        symbol='BTCUSDT',
        start_date=start_date.strftime('%Y-%m-%d'),
        end_date=end_date.strftime('%Y-%m-%d'),
        initial_balance=10000
    )
    
    if results:
        print('Backtest completed successfully!')
        print(f'Total Return: {results[\"total_return\"]:.2f}%')
        print(f'Total Trades: {results[\"total_trades\"]}')
    else:
        print('Backtest failed - check API connection')
        
except Exception as e:
    print(f'Backtest error: {e}')
"
```

### Phase 8: Paper Trading Full Test

Run the bot in paper trading mode for a short period:

```bash
# Make sure .env has TRADING_MODE=paper
python main.py
```

**What to expect**:
- Bot starts and loads configuration
- Connects to Binance API (read-only)
- Monitors price movements
- Generates trading signals
- Simulates trades (no real money)
- Logs all activity

**Let it run for 10-15 minutes**, then stop with `Ctrl+C`.

### Phase 9: Monitoring Test

Test the monitoring dashboard:

```bash
# In a separate terminal, run the monitor
python monitor.py
```

This should show:
- Portfolio summary
- Open positions (if any)
- Recent trades
- Current market prices

### Phase 10: CLI Tools Test

Test the command-line interface:

```bash
# Test CLI help
python cli.py --help

# Test configuration
python cli.py config

# Test backtest command
python cli.py backtest sma_crossover BTCUSDT 2023-12-01 --end-date 2023-12-31
```

## 🔍 What to Look For During Testing

### ✅ Success Indicators:
- All unit tests pass
- Configuration loads without errors
- API connection successful
- Strategies generate signals
- Paper trades execute
- Monitoring dashboard displays data
- No critical errors in logs

### ❌ Failure Indicators:
- Import errors (missing dependencies)
- API connection failures
- Configuration errors
- Strategy calculation errors
- File permission errors

## 🚨 Troubleshooting Common Issues

### 1. Python Not Found
```bash
# Windows: Add Python to PATH or use full path
C:\Python39\python.exe main.py
```

### 2. Module Import Errors
```bash
# Install missing packages
pip install python-binance pandas numpy pyyaml python-dotenv
```

### 3. API Connection Errors
- Check API keys in .env file
- Verify internet connection
- Check Binance API status
- Ensure API permissions are correct

### 4. Permission Errors
```bash
# Windows: Run as administrator if needed
# Or check file permissions
```

### 5. Configuration Errors
- Verify config/config.yaml syntax
- Check .env file format
- Ensure all required fields are present

## 📊 Testing Checklist

- [ ] Python installed and working
- [ ] Dependencies installed
- [ ] Unit tests pass
- [ ] Configuration loads
- [ ] Strategies work with sample data
- [ ] API connection successful (read-only)
- [ ] Paper trading mode works
- [ ] Backtesting completes
- [ ] Monitoring dashboard displays
- [ ] CLI tools respond
- [ ] No critical errors in logs

## 🎯 Next Steps After Testing

Once all tests pass:

1. **Run paper trading for 24-48 hours**
2. **Analyze the results and logs**
3. **Adjust strategy parameters if needed**
4. **Only then consider live trading with small amounts**

## ⚠️ Safety Reminders

- **Never start with live trading**
- **Always test in paper mode first**
- **Use small amounts for initial live trading**
- **Monitor the bot closely**
- **Have stop-loss mechanisms in place**

Remember: The goal is to thoroughly test everything before risking real money!
