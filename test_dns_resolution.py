#!/usr/bin/env python3
"""
DNS Resolution Test for Binance Testnet
Tests if your network can resolve Binance testnet domains.
"""

import socket
import requests
import sys

def test_dns_resolution():
    """Test DNS resolution for Binance domains."""
    print("🔍 TESTING DNS RESOLUTION")
    print("=" * 50)
    
    domains = [
        ("Testnet Futures", "testnet.binancefuture.com"),
        ("Testnet Spot", "testnet.binance.vision"),
        ("Mainnet (backup)", "api.binance.com"),
        ("Google DNS Test", "google.com")
    ]
    
    results = {}
    
    for name, domain in domains:
        print(f"\n🔗 Testing: {domain}")
        
        try:
            # Test DNS resolution
            ip = socket.gethostbyname(domain)
            print(f"   ✅ DNS Resolution: {ip}")
            results[domain] = {'dns': True, 'ip': ip}
            
            # Test HTTP connection
            try:
                if 'binance' in domain:
                    if 'testnet.binancefuture.com' in domain:
                        url = f"https://{domain}/fapi/v1/ping"
                    else:
                        url = f"https://{domain}/api/v3/ping"
                else:
                    url = f"https://{domain}"
                
                response = requests.get(url, timeout=10)
                print(f"   ✅ HTTP Connection: {response.status_code}")
                results[domain]['http'] = True
                
            except Exception as e:
                print(f"   ❌ HTTP Connection: {e}")
                results[domain]['http'] = False
                
        except socket.gaierror as e:
            print(f"   ❌ DNS Resolution Failed: {e}")
            results[domain] = {'dns': False, 'ip': None, 'http': False}
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results[domain] = {'dns': False, 'ip': None, 'http': False}
    
    return results

def analyze_results(results):
    """Analyze DNS test results and provide solutions."""
    print("\n" + "=" * 50)
    print("📊 DNS RESOLUTION ANALYSIS")
    print("=" * 50)
    
    testnet_futures_ok = results.get('testnet.binancefuture.com', {}).get('dns', False)
    testnet_spot_ok = results.get('testnet.binance.vision', {}).get('dns', False)
    mainnet_ok = results.get('api.binance.com', {}).get('dns', False)
    google_ok = results.get('google.com', {}).get('dns', False)
    
    if not google_ok:
        print("❌ CRITICAL: Cannot resolve google.com")
        print("💡 Your internet connection or DNS is completely broken")
        print("🔧 FIXES:")
        print("   1. Check internet connection")
        print("   2. Restart network interface")
        print("   3. Change DNS to *******, *******")
        return "no_internet"
    
    if testnet_futures_ok and testnet_spot_ok:
        print("✅ PERFECT: All testnet domains resolve")
        print("🚀 Your trading bot should work fine!")
        return "all_good"
    
    elif mainnet_ok and not (testnet_futures_ok or testnet_spot_ok):
        print("⚠️  TESTNET BLOCKED: Mainnet works, testnet doesn't")
        print("💡 Your ISP/network blocks Binance testnet domains")
        print("🔧 SOLUTIONS:")
        print("   1. Use VPN to access testnet")
        print("   2. Switch to mainnet (CAREFUL: real money!)")
        print("   3. Use different network (mobile hotspot)")
        print("   4. Change DNS servers")
        return "testnet_blocked"
    
    elif not mainnet_ok and not (testnet_futures_ok or testnet_spot_ok):
        print("❌ ALL BINANCE BLOCKED: No Binance domains resolve")
        print("💡 Your ISP/network blocks all Binance domains")
        print("🔧 SOLUTIONS:")
        print("   1. Use VPN (required)")
        print("   2. Use different network")
        print("   3. Contact ISP about crypto restrictions")
        return "all_blocked"
    
    else:
        print("⚠️  PARTIAL ACCESS: Some domains work, some don't")
        return "partial"

def provide_solutions(issue_type):
    """Provide specific solutions based on the issue type."""
    print("\n" + "=" * 50)
    print("🔧 RECOMMENDED SOLUTIONS")
    print("=" * 50)
    
    if issue_type == "testnet_blocked":
        print("\n🎯 OPTION 1: Use VPN (Recommended)")
        print("   • Install VPN app on your device")
        print("   • Free options: ProtonVPN, Windscribe, Cloudflare WARP")
        print("   • Connect to VPN, then run trading bot")
        
        print("\n🎯 OPTION 2: Switch to Mainnet (CAREFUL!)")
        print("   • Edit .env file: TESTNET_MODE=false")
        print("   • ⚠️  WARNING: Uses real money!")
        print("   • Only do this if you understand the risks")
        
        print("\n🎯 OPTION 3: Use Different Network")
        print("   • Try mobile hotspot from different carrier")
        print("   • Use public WiFi (coffee shop, library)")
        print("   • Ask friend to run bot on their network")
        
        print("\n🎯 OPTION 4: Change DNS Servers")
        print("   • Set DNS to: *******, *******")
        print("   • Or try: 1.0.0.1, 9.9.9.9")
        print("   • Restart network after changing")
    
    elif issue_type == "all_blocked":
        print("\n🎯 VPN REQUIRED")
        print("   • Your ISP blocks all Binance domains")
        print("   • VPN is the only solution")
        print("   • Recommended: ProtonVPN (free), NordVPN (paid)")
    
    elif issue_type == "no_internet":
        print("\n🎯 INTERNET CONNECTION ISSUES")
        print("   • Check WiFi/mobile data connection")
        print("   • Restart router/modem")
        print("   • Contact ISP if problem persists")
    
    elif issue_type == "all_good":
        print("\n🎉 NO ACTION NEEDED")
        print("   • Your network can access testnet")
        print("   • Run: python main.py")

def main():
    """Main function."""
    print("🌐 BINANCE TESTNET DNS RESOLUTION TEST")
    print("Testing if your network can access Binance testnet domains")
    print("=" * 60)
    
    # Test DNS resolution
    results = test_dns_resolution()
    
    # Analyze results
    issue_type = analyze_results(results)
    
    # Provide solutions
    provide_solutions(issue_type)
    
    print("\n" + "=" * 60)
    if issue_type == "all_good":
        print("🎉 READY FOR TRADING!")
        print("Your network can access Binance testnet.")
    else:
        print("⚠️  NETWORK ISSUES DETECTED")
        print("Follow the solutions above to fix DNS resolution.")
    
    print("=" * 60)
    
    return issue_type == "all_good"

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
