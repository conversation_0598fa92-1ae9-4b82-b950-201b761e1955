"""
RSI (Relative Strength Index) Strategy.
"""

import pandas as pd
import numpy as np
from typing import Dict
from .base_strategy import BaseStrategy
import logging

logger = logging.getLogger(__name__)

class RSIStrategy(BaseStrategy):
    """
    RSI Strategy:
    - Buy when RSI is oversold (< oversold_threshold)
    - Sell when RSI is overbought (> overbought_threshold)
    """
    
    def __init__(self, config: Dict):
        """
        Initialize RSI strategy.
        
        Args:
            config: Strategy configuration containing:
                - rsi_period: RSI calculation period
                - oversold_threshold: RSI oversold level
                - overbought_threshold: RSI overbought level
        """
        super().__init__("RSI_Strategy", config)
        self.rsi_period = config.get('rsi_period', 14)
        self.oversold_threshold = config.get('oversold_threshold', 30)
        self.overbought_threshold = config.get('overbought_threshold', 70)
        
        logger.info(f"RSI Strategy initialized: period={self.rsi_period}, "
                   f"oversold={self.oversold_threshold}, overbought={self.overbought_threshold}")
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate RSI (Relative Strength Index).
        
        Args:
            prices: Price series
            period: RSI period
            
        Returns:
            RSI values
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate RSI indicator.
        
        Args:
            data: OHLCV data
            
        Returns:
            DataFrame with RSI indicator added
        """
        df = data.copy()
        
        # Calculate RSI
        df['RSI'] = self.calculate_rsi(df['close'], self.rsi_period)
        
        # Calculate RSI signals
        df['RSI_oversold'] = df['RSI'] < self.oversold_threshold
        df['RSI_overbought'] = df['RSI'] > self.overbought_threshold
        
        # Identify signal changes
        df['RSI_oversold_prev'] = df['RSI_oversold'].shift(1)
        df['RSI_overbought_prev'] = df['RSI_overbought'].shift(1)
        
        # Buy signal: RSI crosses above oversold threshold
        df['RSI_buy_signal'] = (~df['RSI_oversold']) & (df['RSI_oversold_prev'])
        
        # Sell signal: RSI crosses above overbought threshold
        df['RSI_sell_signal'] = (df['RSI_overbought']) & (~df['RSI_overbought_prev'])
        
        return df
    
    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """
        Generate trading signal based on RSI.
        
        Args:
            data: OHLCV data
            symbol: Trading symbol
            
        Returns:
            Signal: 'BUY', 'SELL', or 'HOLD'
        """
        if len(data) < self.rsi_period + 1:
            return 'HOLD'
        
        # Calculate indicators
        df = self.calculate_indicators(data)
        
        # Get latest values
        latest = df.iloc[-1]
        current_price = latest['close']
        current_rsi = latest['RSI']
        
        # Generate signals
        if latest['RSI_buy_signal']:
            signal = 'BUY'
        elif latest['RSI_sell_signal']:
            signal = 'SELL'
        else:
            signal = 'HOLD'
        
        # Log signal with indicators
        indicators = {
            'RSI': current_rsi,
            'RSI_oversold': latest['RSI_oversold'],
            'RSI_overbought': latest['RSI_overbought']
        }
        
        if signal != 'HOLD':
            self.log_signal(symbol, signal, current_price, indicators)
        
        return signal
    
    def get_strategy_info(self) -> Dict:
        """Get strategy information."""
        return {
            'name': self.name,
            'type': 'Mean Reversion',
            'rsi_period': self.rsi_period,
            'oversold_threshold': self.oversold_threshold,
            'overbought_threshold': self.overbought_threshold,
            'description': f'RSI Strategy (period={self.rsi_period}, {self.oversold_threshold}/{self.overbought_threshold})'
        }
