# Sensitive Files - DO NOT COMMIT
.env
*.env
.env.local
.env.production

# API Keys and Secrets
config/secrets.yaml
api_keys.txt
credentials.json

# Trading Data and Logs
logs/
*.log
trading_bot.log

# Portfolio and Trading State
portfolio_state.json
trading_history.json
backtest_results/
data/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Jupyter Notebooks
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
