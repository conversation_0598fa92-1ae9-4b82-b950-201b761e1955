"""
Scalping strategy for unclear market conditions.
Focuses on quick profits from small price movements.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class ScalpingStrategy(BaseStrategy):
    """
    Scalping strategy that activates during unclear market conditions.
    Uses short-term indicators to capture quick profits.
    """
    
    def __init__(self, client, config: Dict):
        """
        Initialize scalping strategy.

        Args:
            client: Binance client instance
            config: Strategy configuration
        """
        super().__init__("ScalpingStrategy", config)
        self.client = client
        
        # Scalping-specific parameters
        self.scalp_timeframe = config.get('scalp_timeframe', '1m')  # 1-minute scalping
        self.quick_ema_period = config.get('quick_ema_period', 5)   # Very fast EMA
        self.slow_ema_period = config.get('slow_ema_period', 13)    # Slower EMA
        self.rsi_period = config.get('rsi_period', 7)               # Fast RSI
        self.bb_period = config.get('bb_period', 10)                # Bollinger Bands
        self.bb_std = config.get('bb_std', 1.5)                     # BB standard deviation
        
        # Scalping thresholds
        self.min_profit_pct = config.get('min_profit_pct', 0.3)     # 0.3% minimum profit
        self.max_loss_pct = config.get('max_loss_pct', 0.15)       # 0.15% max loss
        self.volume_spike_threshold = config.get('volume_spike_threshold', 1.2)  # 20% volume spike
        
        # Position management
        self.max_scalp_positions = config.get('max_scalp_positions', 3)
        self.scalp_position_size = config.get('scalp_position_size', 0.02)  # 2% per scalp
        
        # Timing
        self.min_hold_seconds = config.get('min_hold_seconds', 30)   # Minimum 30 seconds
        self.max_hold_seconds = config.get('max_hold_seconds', 300)  # Maximum 5 minutes
        
        # Track scalping positions
        self.scalp_positions = {}
        
        logger.info("Scalping Strategy initialized for unclear market conditions")
    
    def should_activate(self, market_analysis: Dict, symbol_conditions: Dict) -> bool:
        """
        Determine if scalping should activate based on market conditions.
        
        Args:
            market_analysis: Overall market analysis
            symbol_conditions: Symbol-specific conditions
            
        Returns:
            True if scalping should activate
        """
        # Activate scalping when:
        # 1. Market is sideways/unclear
        # 2. Low volatility but some movement
        # 3. Normal volume (not too low, not too high)
        
        unclear_conditions = [
            'sideways' in symbol_conditions.get('reason', '').lower(),
            'unclear' in market_analysis.get('reason', '').lower(),
            not market_analysis.get('healthy', True),
            symbol_conditions.get('sideways_market', False)
        ]
        
        return any(unclear_conditions)
    
    def analyze_scalp_opportunity(self, symbol: str, data: pd.DataFrame) -> Dict:
        """
        Analyze scalping opportunity for a symbol.
        
        Args:
            symbol: Trading symbol
            data: Market data
            
        Returns:
            Scalping analysis result
        """
        if len(data) < max(self.slow_ema_period, self.bb_period, self.rsi_period):
            return {'signal': 'HOLD', 'confidence': 0.0, 'reason': 'Insufficient data'}
        
        # Calculate indicators
        close = data['close']
        volume = data['volume']
        
        # EMAs for trend detection
        ema_fast = close.ewm(span=self.quick_ema_period).mean()
        ema_slow = close.ewm(span=self.slow_ema_period).mean()
        
        # RSI for momentum
        rsi = self.calculate_rsi(close, self.rsi_period)
        
        # Bollinger Bands for volatility
        bb_middle = close.rolling(window=self.bb_period).mean()
        bb_std = close.rolling(window=self.bb_period).std()
        bb_upper = bb_middle + (bb_std * self.bb_std)
        bb_lower = bb_middle - (bb_std * self.bb_std)
        
        # Current values
        current_price = close.iloc[-1]
        current_ema_fast = ema_fast.iloc[-1]
        current_ema_slow = ema_slow.iloc[-1]
        current_rsi = rsi.iloc[-1]
        current_bb_upper = bb_upper.iloc[-1]
        current_bb_lower = bb_lower.iloc[-1]
        
        # Volume analysis
        avg_volume = volume.tail(20).mean()
        current_volume = volume.iloc[-1]
        volume_ratio = current_volume / avg_volume
        
        # Scalping signals
        signals = []
        confidence = 0.0
        
        # EMA crossover signals
        if current_ema_fast > current_ema_slow:
            if ema_fast.iloc[-2] <= ema_slow.iloc[-2]:  # Fresh crossover
                signals.append('BUY')
                confidence += 0.3
        elif current_ema_fast < current_ema_slow:
            if ema_fast.iloc[-2] >= ema_slow.iloc[-2]:  # Fresh crossover
                signals.append('SELL')
                confidence += 0.3
        
        # RSI signals
        if current_rsi < 30 and current_rsi > rsi.iloc[-2]:  # RSI bouncing from oversold
            signals.append('BUY')
            confidence += 0.2
        elif current_rsi > 70 and current_rsi < rsi.iloc[-2]:  # RSI falling from overbought
            signals.append('SELL')
            confidence += 0.2
        
        # Bollinger Band signals
        if current_price <= current_bb_lower:  # Price at lower band
            signals.append('BUY')
            confidence += 0.2
        elif current_price >= current_bb_upper:  # Price at upper band
            signals.append('SELL')
            confidence += 0.2
        
        # Volume confirmation
        if volume_ratio >= self.volume_spike_threshold:
            confidence += 0.3
        
        # Determine final signal
        buy_signals = signals.count('BUY')
        sell_signals = signals.count('SELL')
        
        if buy_signals > sell_signals and confidence >= 0.4:
            signal = 'BUY'
        elif sell_signals > buy_signals and confidence >= 0.4:
            signal = 'SELL'
        else:
            signal = 'HOLD'
            confidence = 0.0
        
        return {
            'signal': signal,
            'confidence': confidence,
            'current_price': current_price,
            'ema_fast': current_ema_fast,
            'ema_slow': current_ema_slow,
            'rsi': current_rsi,
            'bb_upper': current_bb_upper,
            'bb_lower': current_bb_lower,
            'volume_ratio': volume_ratio,
            'reason': f"EMA: {current_ema_fast:.6f}/{current_ema_slow:.6f}, RSI: {current_rsi:.1f}, Vol: {volume_ratio:.2f}x"
        }
    
    def calculate_scalp_targets(self, entry_price: float, signal: str) -> Tuple[float, float]:
        """
        Calculate take profit and stop loss for scalping.
        
        Args:
            entry_price: Entry price
            signal: BUY or SELL
            
        Returns:
            Tuple of (take_profit, stop_loss)
        """
        if signal == 'BUY':
            take_profit = entry_price * (1 + self.min_profit_pct / 100)
            stop_loss = entry_price * (1 - self.max_loss_pct / 100)
        else:  # SELL
            take_profit = entry_price * (1 - self.min_profit_pct / 100)
            stop_loss = entry_price * (1 + self.max_loss_pct / 100)
        
        return take_profit, stop_loss
    
    def generate_signals(self, symbol: str, data: pd.DataFrame) -> List[Dict]:
        """
        Generate scalping signals for a symbol.
        
        Args:
            symbol: Trading symbol
            data: Market data
            
        Returns:
            List of trading signals
        """
        signals = []
        
        # Check if we have too many scalp positions
        if len(self.scalp_positions) >= self.max_scalp_positions:
            return signals
        
        # Get 1-minute data for scalping
        scalp_data = self.client.get_historical_klines(symbol, self.scalp_timeframe, 50)
        if scalp_data.empty:
            return signals
        
        # Analyze scalping opportunity
        analysis = self.analyze_scalp_opportunity(symbol, scalp_data)
        
        if analysis['signal'] in ['BUY', 'SELL'] and analysis['confidence'] >= 0.4:
            # Calculate targets
            entry_price = analysis['current_price']
            take_profit, stop_loss = self.calculate_scalp_targets(entry_price, analysis['signal'])
            
            signal = {
                'symbol': symbol,
                'action': analysis['signal'],
                'price': entry_price,
                'quantity': self.scalp_position_size,  # 2% position size
                'take_profit': take_profit,
                'stop_loss': stop_loss,
                'confidence': analysis['confidence'],
                'strategy': 'SCALPING',
                'timeframe': self.scalp_timeframe,
                'reason': f"Scalp {analysis['signal']}: {analysis['reason']}",
                'max_hold_time': self.max_hold_seconds
            }
            
            signals.append(signal)
            logger.info(f"Scalping signal generated: {signal['action']} {symbol} @ {entry_price:.6f} "
                       f"(TP: {take_profit:.6f}, SL: {stop_loss:.6f})")
        
        return signals
    
    def calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate RSI indicator."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def update_scalp_positions(self, symbol: str, action: str, price: float):
        """Update scalping position tracking."""
        if action in ['BUY', 'SELL']:
            self.scalp_positions[symbol] = {
                'action': action,
                'entry_price': price,
                'entry_time': pd.Timestamp.now(),
                'take_profit': None,
                'stop_loss': None
            }
        elif action in ['CLOSE', 'EXIT']:
            if symbol in self.scalp_positions:
                del self.scalp_positions[symbol]
    
    def check_scalp_exits(self) -> List[Dict]:
        """Check if any scalp positions should be closed."""
        exit_signals = []
        current_time = pd.Timestamp.now()
        
        for symbol, position in list(self.scalp_positions.items()):
            # Check time-based exit
            hold_time = (current_time - position['entry_time']).total_seconds()
            
            if hold_time >= self.max_hold_seconds:
                exit_signals.append({
                    'symbol': symbol,
                    'action': 'CLOSE',
                    'reason': 'Max hold time reached',
                    'strategy': 'SCALPING'
                })
                
        return exit_signals

    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate technical indicators for scalping strategy.

        Args:
            data: Market data DataFrame

        Returns:
            DataFrame with calculated indicators
        """
        if len(data) < max(self.slow_ema_period, self.bb_period, self.rsi_period):
            return data

        # Calculate EMAs for trend detection
        data['EMA_fast'] = data['close'].ewm(span=self.quick_ema_period).mean()
        data['EMA_slow'] = data['close'].ewm(span=self.slow_ema_period).mean()

        # Calculate RSI for momentum
        data['RSI'] = self.calculate_rsi(data['close'], self.rsi_period)

        # Calculate Bollinger Bands for volatility
        data['BB_middle'] = data['close'].rolling(window=self.bb_period).mean()
        bb_std = data['close'].rolling(window=self.bb_period).std()
        data['BB_upper'] = data['BB_middle'] + (bb_std * self.bb_std)
        data['BB_lower'] = data['BB_middle'] - (bb_std * self.bb_std)

        # Calculate volume indicators
        data['Volume_MA'] = data['volume'].rolling(window=20).mean()
        data['Volume_ratio'] = data['volume'] / data['Volume_MA']

        return data

    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """
        Generate scalping signal based on market data.

        Args:
            data: Market data DataFrame
            symbol: Trading symbol

        Returns:
            Signal: 'BUY', 'SELL', or 'HOLD'
        """
        # Check if we have enough data
        if len(data) < max(self.slow_ema_period, self.bb_period, self.rsi_period):
            return 'HOLD'

        # Check if we have too many scalp positions
        if len(self.scalp_positions) >= self.max_scalp_positions:
            return 'HOLD'

        # Get 1-minute data for scalping analysis
        try:
            scalp_data = self.client.get_historical_klines(symbol, self.scalp_timeframe, 50)
            if scalp_data.empty:
                return 'HOLD'
        except Exception:
            return 'HOLD'

        # Calculate indicators for scalp data
        scalp_data = self.calculate_indicators(scalp_data)

        # Analyze scalping opportunity
        analysis = self.analyze_scalp_opportunity(symbol, scalp_data)

        # Return signal based on analysis
        if analysis['signal'] in ['BUY', 'SELL'] and analysis['confidence'] >= 0.4:
            return analysis['signal']

        return 'HOLD'
