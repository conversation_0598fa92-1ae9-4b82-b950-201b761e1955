"""
Tests for trading strategies.
"""

import pytest
import pandas as pd
import numpy as np
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from strategies.sma_crossover import SMACrossoverStrategy
from strategies.rsi_strategy import RSIStrategy

class TestSMACrossoverStrategy:
    """Test SMA Crossover Strategy."""
    
    def setup_method(self):
        """Setup test data."""
        self.config = {
            'short_window': 5,
            'long_window': 10
        }
        self.strategy = SMACrossoverStrategy(self.config)
        
        # Create test data
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=50, freq='1H')
        prices = 100 + np.cumsum(np.random.randn(50) * 0.5)
        
        self.test_data = pd.DataFrame({
            'open': prices,
            'high': prices * 1.01,
            'low': prices * 0.99,
            'close': prices,
            'volume': np.random.randint(1000, 10000, 50)
        }, index=dates)
    
    def test_calculate_indicators(self):
        """Test indicator calculation."""
        result = self.strategy.calculate_indicators(self.test_data)
        
        assert 'SMA_5' in result.columns
        assert 'SMA_10' in result.columns
        assert 'SMA_diff' in result.columns
        assert 'golden_cross' in result.columns
        assert 'death_cross' in result.columns
    
    def test_generate_signal(self):
        """Test signal generation."""
        signal = self.strategy.generate_signal(self.test_data, 'BTCUSDT')
        assert signal in ['BUY', 'SELL', 'HOLD']
    
    def test_insufficient_data(self):
        """Test with insufficient data."""
        short_data = self.test_data.head(5)
        signal = self.strategy.generate_signal(short_data, 'BTCUSDT')
        assert signal == 'HOLD'

class TestRSIStrategy:
    """Test RSI Strategy."""
    
    def setup_method(self):
        """Setup test data."""
        self.config = {
            'rsi_period': 14,
            'oversold_threshold': 30,
            'overbought_threshold': 70
        }
        self.strategy = RSIStrategy(self.config)
        
        # Create test data with trend
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=50, freq='1H')
        
        # Create trending price data
        trend = np.linspace(100, 120, 50)
        noise = np.random.randn(50) * 2
        prices = trend + noise
        
        self.test_data = pd.DataFrame({
            'open': prices,
            'high': prices * 1.01,
            'low': prices * 0.99,
            'close': prices,
            'volume': np.random.randint(1000, 10000, 50)
        }, index=dates)
    
    def test_calculate_rsi(self):
        """Test RSI calculation."""
        rsi = self.strategy.calculate_rsi(self.test_data['close'])
        
        # RSI should be between 0 and 100
        assert (rsi >= 0).all()
        assert (rsi <= 100).all()
    
    def test_calculate_indicators(self):
        """Test indicator calculation."""
        result = self.strategy.calculate_indicators(self.test_data)
        
        assert 'RSI' in result.columns
        assert 'RSI_oversold' in result.columns
        assert 'RSI_overbought' in result.columns
        assert 'RSI_buy_signal' in result.columns
        assert 'RSI_sell_signal' in result.columns
    
    def test_generate_signal(self):
        """Test signal generation."""
        signal = self.strategy.generate_signal(self.test_data, 'BTCUSDT')
        assert signal in ['BUY', 'SELL', 'HOLD']
    
    def test_insufficient_data(self):
        """Test with insufficient data."""
        short_data = self.test_data.head(10)
        signal = self.strategy.generate_signal(short_data, 'BTCUSDT')
        assert signal == 'HOLD'

if __name__ == "__main__":
    pytest.main([__file__])
