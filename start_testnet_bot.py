"""
Testnet-only startup script for the trading bot.
This script ensures the bot runs ONLY on Binance Futures Testnet.
"""

import os
import sys
import subprocess
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_testnet_setup():
    """Check if testnet is properly set up."""
    print("🔍 Checking Testnet Setup...")
    
    # Check API keys
    api_key = os.getenv('BINANCE_API_KEY')
    api_secret = os.getenv('BINANCE_API_SECRET')
    
    if not api_key or not api_secret:
        print("❌ Binance API keys not found in .env file")
        print("   Please set BINANCE_API_KEY and BINANCE_API_SECRET")
        print("   Get testnet keys from: https://testnet.binancefuture.com")
        return False
    
    print("✅ API keys found")
    
    # Check if keys look like testnet keys (they often have specific patterns)
    if len(api_key) < 50:
        print("⚠️  API key seems short - make sure it's a valid testnet key")
    
    return True

def show_testnet_info():
    """Show testnet information."""
    print("\n" + "=" * 60)
    print("🧪 BINANCE FUTURES TESTNET TRADING BOT")
    print("=" * 60)
    print()
    print("🔒 SAFETY FEATURES:")
    print("   • Uses TESTNET ONLY - No real money at risk")
    print("   • All trades are simulated on Binance testnet")
    print("   • Perfect for testing strategies safely")
    print()
    print("🌐 TESTNET DETAILS:")
    print("   • URL: https://testnet.binancefuture.com")
    print("   • Free testnet funds available")
    print("   • Real market data, simulated trading")
    print()
    print("📊 CURRENT CONFIGURATION:")
    print("   • Mode: TESTNET ONLY")
    print("   • Paper trading: REMOVED")
    print("   • Real API calls: To testnet only")
    print()

def run_verification():
    """Run the testnet verification script."""
    print("🔧 Running testnet verification...")
    
    try:
        result = subprocess.run([sys.executable, 'verify_testnet_only.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Testnet verification passed")
            return True
        else:
            print("❌ Testnet verification failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running verification: {e}")
        return False

def start_bot():
    """Start the trading bot."""
    print("\n🚀 Starting Testnet Trading Bot...")
    print("=" * 60)
    
    try:
        # Run the main bot
        subprocess.run([sys.executable, 'main.py'])
    except KeyboardInterrupt:
        print("\n⏹️  Bot stopped by user")
    except Exception as e:
        print(f"❌ Error starting bot: {e}")

def main():
    """Main startup function."""
    print("🤖 Testnet Trading Bot Startup")
    print("=" * 70)
    
    # Show testnet info
    show_testnet_info()
    
    # Check setup
    if not check_testnet_setup():
        print("\n❌ Setup check failed. Please fix the issues above.")
        return
    
    # Run verification
    if not run_verification():
        print("\n❌ Verification failed. Bot may not be safe to run.")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Startup cancelled.")
            return
    
    # Final confirmation
    print("\n" + "=" * 60)
    print("⚠️  FINAL CONFIRMATION")
    print("This bot will trade on BINANCE FUTURES TESTNET ONLY")
    print("No real money will be used - this is 100% safe simulation")
    print("=" * 60)
    
    response = input("\nPress Enter to start testnet trading or 'exit' to cancel: ")
    if response.lower() == 'exit':
        print("Startup cancelled by user.")
        return
    
    # Start the bot
    start_bot()

if __name__ == "__main__":
    main()
