"""
Check balance synchronization between bot and testnet account.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from portfolio import Portfolio

def main():
    """Check balance synchronization."""
    print("🔍 Balance Synchronization Check")
    print("=" * 50)
    
    try:
        # Initialize client
        client = BinanceClient(testnet=True)
        
        # Get actual testnet balance
        print("📡 Fetching actual testnet balance...")
        account_info = client.futures_get_account_info()
        
        if not account_info:
            print("❌ Could not fetch account info")
            return
        
        assets = account_info.get('assets', [])
        usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
        
        if not usdt_asset:
            print("❌ No USDT asset found in account")
            return
        
        actual_balance = float(usdt_asset.get('availableBalance', 0))
        wallet_balance = float(usdt_asset.get('walletBalance', 0))
        
        print(f"✅ Actual Testnet Balance:")
        print(f"   Available: {actual_balance:.2f} USDT")
        print(f"   Wallet: {wallet_balance:.2f} USDT")
        
        # Check what the bot would use as default
        default_balance = float(os.getenv('INITIAL_BALANCE', 10000))
        print(f"\n📋 Bot Default Balance: {default_balance:.2f} USDT")
        
        # Show the difference
        difference = actual_balance - default_balance
        print(f"\n📊 Balance Comparison:")
        print(f"   Testnet: {actual_balance:.2f} USDT")
        print(f"   Bot Default: {default_balance:.2f} USDT")
        print(f"   Difference: {difference:+.2f} USDT")
        
        if abs(difference) > 0.01:
            print(f"\n⚠️  MISMATCH DETECTED!")
            print(f"   The bot's internal balance doesn't match your testnet balance")
            print(f"   This has been fixed - the bot will now sync automatically")
        else:
            print(f"\n✅ Balances match!")
        
        # Test portfolio sync
        print(f"\n🧪 Testing Portfolio Sync...")
        portfolio = Portfolio(default_balance, 'USDT')
        print(f"   Before sync: {portfolio.available_balance:.2f} USDT")
        
        portfolio.sync_balance(actual_balance)
        print(f"   After sync: {portfolio.available_balance:.2f} USDT")
        
        print(f"\n✅ Balance sync test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
