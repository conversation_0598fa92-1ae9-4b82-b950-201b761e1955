"""
Advanced Signal Filter for Enhanced Trading Algorithm
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SignalQuality:
    """Signal quality assessment."""
    strength: float  # 0-100
    confidence: float  # 0-100
    risk_score: float  # 0-100 (lower is better)
    market_alignment: float  # 0-100
    volume_confirmation: bool
    trend_alignment: bool
    overall_score: float  # 0-100

class AdvancedSignalFilter:
    """
    Advanced signal filtering system for enhanced trading performance.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Signal quality thresholds
        self.min_signal_strength = config.get('min_signal_strength', 60)
        self.min_confidence = config.get('min_confidence', 65)
        self.max_risk_score = config.get('max_risk_score', 40)
        self.min_overall_score = config.get('min_overall_score', 70)
        
        # Market condition filters
        self.volume_multiplier_threshold = config.get('volume_multiplier_threshold', 1.5)
        self.trend_strength_threshold = config.get('trend_strength_threshold', 0.6)
        self.volatility_threshold = config.get('volatility_threshold', 0.05)
        
        logger.info(f"Advanced Signal Filter initialized with quality thresholds: {self.min_overall_score}%")
    
    def calculate_signal_strength(self, data: pd.DataFrame, signal: str, strategy_name: str) -> float:
        """Calculate signal strength based on technical indicators."""
        try:
            if len(data) < 20:
                return 0.0
            
            latest = data.iloc[-1]
            strength = 50.0  # Base strength
            
            # RSI-based strength
            if 'RSI' in data.columns:
                rsi = latest['RSI']
                if signal == 'BUY' and rsi < 30:
                    strength += (30 - rsi) * 2  # More oversold = stronger signal
                elif signal == 'SELL' and rsi > 70:
                    strength += (rsi - 70) * 2  # More overbought = stronger signal
            
            # Volume confirmation
            if 'volume' in data.columns:
                recent_volume = data['volume'].tail(5).mean()
                avg_volume = data['volume'].tail(20).mean()
                if recent_volume > avg_volume * 1.5:
                    strength += 15  # Volume surge adds strength
            
            # Price momentum
            if len(data) >= 5:
                price_change_5 = (latest['close'] - data.iloc[-5]['close']) / data.iloc[-5]['close']
                if signal == 'BUY' and price_change_5 > 0:
                    strength += min(price_change_5 * 100, 20)
                elif signal == 'SELL' and price_change_5 < 0:
                    strength += min(abs(price_change_5) * 100, 20)
            
            # Strategy-specific adjustments
            if strategy_name == 'MovingAverageCrossover':
                # MA crossover strength based on separation
                if 'short_ma' in data.columns and 'long_ma' in data.columns:
                    ma_separation = abs(latest['short_ma'] - latest['long_ma']) / latest['close']
                    strength += min(ma_separation * 1000, 15)
            
            elif strategy_name == 'BollingerBands':
                # BB strength based on band position
                if all(col in data.columns for col in ['upper_band', 'lower_band', 'rolling_mean']):
                    band_width = (latest['upper_band'] - latest['lower_band']) / latest['rolling_mean']
                    if band_width > 0.04:  # Wide bands = strong signal
                        strength += 10
            
            return min(strength, 100.0)
            
        except Exception as e:
            logger.error(f"Error calculating signal strength: {e}")
            return 0.0
    
    def calculate_confidence(self, data: pd.DataFrame, signal: str) -> float:
        """Calculate signal confidence based on multiple confirmations."""
        try:
            if len(data) < 10:
                return 0.0
            
            confidence = 50.0  # Base confidence
            confirmations = 0
            
            latest = data.iloc[-1]
            
            # Trend confirmation
            if len(data) >= 10:
                trend_slope = np.polyfit(range(10), data['close'].tail(10), 1)[0]
                if (signal == 'BUY' and trend_slope > 0) or (signal == 'SELL' and trend_slope < 0):
                    confirmations += 1
                    confidence += 15
            
            # Volume trend confirmation
            if 'volume' in data.columns and len(data) >= 5:
                volume_trend = data['volume'].tail(5).mean() / data['volume'].tail(10).mean()
                if volume_trend > 1.2:  # Increasing volume
                    confirmations += 1
                    confidence += 10
            
            # Multiple timeframe alignment (simulated with different periods)
            if len(data) >= 20:
                short_trend = data['close'].tail(5).mean() / data['close'].tail(10).mean()
                medium_trend = data['close'].tail(10).mean() / data['close'].tail(20).mean()
                
                if signal == 'BUY' and short_trend > 1.0 and medium_trend > 1.0:
                    confirmations += 1
                    confidence += 15
                elif signal == 'SELL' and short_trend < 1.0 and medium_trend < 1.0:
                    confirmations += 1
                    confidence += 15
            
            # Volatility confirmation
            if len(data) >= 20:
                volatility = data['close'].pct_change().tail(20).std()
                if 0.02 < volatility < 0.08:  # Optimal volatility range
                    confirmations += 1
                    confidence += 10
            
            # Bonus for multiple confirmations
            if confirmations >= 3:
                confidence += 10
            
            return min(confidence, 100.0)
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.0
    
    def calculate_risk_score(self, data: pd.DataFrame, symbol: str) -> float:
        """Calculate risk score (lower is better)."""
        try:
            if len(data) < 20:
                return 100.0  # High risk for insufficient data
            
            risk_score = 20.0  # Base risk
            
            # Volatility risk
            volatility = data['close'].pct_change().tail(20).std()
            if volatility > 0.1:  # High volatility
                risk_score += min((volatility - 0.1) * 500, 30)
            
            # Volume risk (low volume = higher risk)
            if 'volume' in data.columns:
                recent_volume = data['volume'].tail(5).mean()
                if recent_volume < data['volume'].tail(50).mean() * 0.5:
                    risk_score += 20
            
            # Price stability risk
            price_range = (data['high'].tail(10).max() - data['low'].tail(10).min()) / data['close'].tail(10).mean()
            if price_range > 0.15:  # High price swings
                risk_score += min(price_range * 100, 25)
            
            # Gap risk (sudden price jumps)
            price_changes = data['close'].pct_change().tail(10)
            max_change = abs(price_changes).max()
            if max_change > 0.05:  # Large single-period change
                risk_score += min(max_change * 200, 20)
            
            return min(risk_score, 100.0)
            
        except Exception as e:
            logger.error(f"Error calculating risk score: {e}")
            return 100.0
    
    def calculate_market_alignment(self, data: pd.DataFrame, signal: str) -> float:
        """Calculate how well signal aligns with overall market conditions."""
        try:
            if len(data) < 30:
                return 50.0
            
            alignment = 50.0  # Base alignment
            
            # Overall trend alignment
            long_term_trend = (data['close'].iloc[-1] - data['close'].iloc[-30]) / data['close'].iloc[-30]
            medium_term_trend = (data['close'].iloc[-1] - data['close'].iloc[-10]) / data['close'].iloc[-10]
            
            if signal == 'BUY':
                if long_term_trend > 0 and medium_term_trend > 0:
                    alignment += 25  # Strong uptrend alignment
                elif long_term_trend > 0 or medium_term_trend > 0:
                    alignment += 15  # Partial alignment
            else:  # SELL
                if long_term_trend < 0 and medium_term_trend < 0:
                    alignment += 25  # Strong downtrend alignment
                elif long_term_trend < 0 or medium_term_trend < 0:
                    alignment += 15  # Partial alignment
            
            # Market momentum alignment
            momentum = data['close'].pct_change().tail(5).mean()
            if (signal == 'BUY' and momentum > 0) or (signal == 'SELL' and momentum < 0):
                alignment += 15
            
            return min(alignment, 100.0)
            
        except Exception as e:
            logger.error(f"Error calculating market alignment: {e}")
            return 50.0
    
    def assess_signal_quality(self, data: pd.DataFrame, signal: str, symbol: str, strategy_name: str) -> SignalQuality:
        """Comprehensive signal quality assessment."""
        try:
            # Calculate individual components
            strength = self.calculate_signal_strength(data, signal, strategy_name)
            confidence = self.calculate_confidence(data, signal)
            risk_score = self.calculate_risk_score(data, symbol)
            market_alignment = self.calculate_market_alignment(data, signal)
            
            # Volume confirmation
            volume_confirmation = False
            if 'volume' in data.columns and len(data) >= 10:
                recent_volume = data['volume'].tail(3).mean()
                avg_volume = data['volume'].tail(20).mean()
                volume_confirmation = recent_volume > avg_volume * self.volume_multiplier_threshold
            
            # Trend alignment
            trend_alignment = False
            if len(data) >= 10:
                trend_slope = np.polyfit(range(10), data['close'].tail(10), 1)[0]
                trend_strength = abs(trend_slope) / data['close'].iloc[-1]
                trend_alignment = trend_strength > self.trend_strength_threshold / 1000
            
            # Calculate overall score
            overall_score = (
                strength * 0.3 +
                confidence * 0.25 +
                (100 - risk_score) * 0.2 +
                market_alignment * 0.25
            )
            
            # Bonuses for confirmations
            if volume_confirmation:
                overall_score += 5
            if trend_alignment:
                overall_score += 5
            
            overall_score = min(overall_score, 100.0)
            
            return SignalQuality(
                strength=strength,
                confidence=confidence,
                risk_score=risk_score,
                market_alignment=market_alignment,
                volume_confirmation=volume_confirmation,
                trend_alignment=trend_alignment,
                overall_score=overall_score
            )
            
        except Exception as e:
            logger.error(f"Error assessing signal quality: {e}")
            return SignalQuality(0, 0, 100, 0, False, False, 0)
    
    def should_trade_signal(self, quality: SignalQuality, symbol: str, signal: str) -> Tuple[bool, str]:
        """Determine if signal meets quality thresholds for trading."""
        try:
            reasons = []
            
            # Check individual thresholds
            if quality.strength < self.min_signal_strength:
                reasons.append(f"Low strength: {quality.strength:.1f} < {self.min_signal_strength}")
            
            if quality.confidence < self.min_confidence:
                reasons.append(f"Low confidence: {quality.confidence:.1f} < {self.min_confidence}")
            
            if quality.risk_score > self.max_risk_score:
                reasons.append(f"High risk: {quality.risk_score:.1f} > {self.max_risk_score}")
            
            if quality.overall_score < self.min_overall_score:
                reasons.append(f"Low overall score: {quality.overall_score:.1f} < {self.min_overall_score}")
            
            # Check confirmations
            if not quality.volume_confirmation:
                reasons.append("No volume confirmation")
            
            should_trade = len(reasons) == 0
            reason = "All quality checks passed" if should_trade else "; ".join(reasons)
            
            return should_trade, reason
            
        except Exception as e:
            logger.error(f"Error checking signal quality: {e}")
            return False, f"Error: {e}"
