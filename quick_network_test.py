#!/usr/bin/env python3
"""
Quick Network Test for Binance Testnet
Fast diagnosis of DNS resolution issues.
"""

import socket
import requests
import sys

def quick_test():
    """Quick test of network connectivity."""
    print("🔍 QUICK NETWORK TEST")
    print("=" * 30)
    
    # Test basic internet
    try:
        socket.gethostbyname('google.com')
        print("✅ Internet: OK")
    except:
        print("❌ Internet: FAILED")
        return False
    
    # Test Binance testnet
    testnet_ok = False
    try:
        socket.gethostbyname('testnet.binancefuture.com')
        print("✅ Testnet Futures: OK")
        testnet_ok = True
    except:
        print("❌ Testnet Futures: DNS FAILED")
    
    try:
        socket.gethostbyname('testnet.binance.vision')
        print("✅ Testnet Spot: OK")
        testnet_ok = True
    except:
        print("❌ Testnet Spot: DNS FAILED")
    
    # Test mainnet
    mainnet_ok = False
    try:
        socket.gethostbyname('api.binance.com')
        print("✅ Mainnet: OK")
        mainnet_ok = True
    except:
        print("❌ Mainnet: DNS FAILED")
    
    print("\n" + "=" * 30)
    
    if testnet_ok:
        print("🎉 TESTNET WORKS!")
        print("Your trading bot should work fine.")
        return True
    elif mainnet_ok:
        print("⚠️  TESTNET BLOCKED")
        print("Solutions:")
        print("1. Use VPN")
        print("2. Switch to mainnet (edit .env)")
        print("3. Change DNS servers")
        return False
    else:
        print("❌ ALL BINANCE BLOCKED")
        print("You need VPN to access Binance.")
        return False

if __name__ == "__main__":
    quick_test()
