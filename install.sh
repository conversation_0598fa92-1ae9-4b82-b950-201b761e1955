#!/bin/bash

echo "========================================"
echo " Crypto Trading Bot - Installation"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed"
    echo "Please install Python 3.8+ first"
    echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "macOS: brew install python3"
    exit 1
fi

echo "Python found!"
python3 --version

echo
echo "Installing required packages..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Failed to install packages"
    echo "Please check your internet connection and try again"
    exit 1
fi

echo
echo "Setting up environment file..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Created .env file from template"
else
    echo ".env file already exists"
fi

echo
echo "Creating necessary directories..."
mkdir -p logs data backtest_results

echo
echo "Running comprehensive tests..."
python3 test_bot.py

echo
echo "========================================"
echo " Installation Complete!"
echo "========================================"
echo
echo "Next steps:"
echo "1. Edit .env file with your Binance API keys"
echo "2. Run the bot: python3 main.py"
echo "3. Monitor: python3 monitor.py"
echo
echo "For paper trading (recommended):"
echo "  Set TRADING_MODE=paper in .env file"
echo
