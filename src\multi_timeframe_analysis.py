"""
Multi-timeframe analysis for comprehensive market view.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging
from advanced_technical_analysis import AdvancedTechnicalAnalysis

logger = logging.getLogger(__name__)

class MultiTimeframeAnalysis:
    """Multi-timeframe analysis system."""
    
    def __init__(self, binance_client):
        """Initialize multi-timeframe analysis."""
        self.client = binance_client
        self.ta = AdvancedTechnicalAnalysis()
        
        # Timeframes for chart-based analysis (optimized for chart patterns)
        self.timeframes = {
            '5m': {'weight': 0.20, 'periods': 100},   # Short-term entry signals
            '15m': {'weight': 0.35, 'periods': 200},  # Primary analysis timeframe
            '1h': {'weight': 0.45, 'periods': 200}    # Trend confirmation
        }

    def get_chart_analysis_data(self, symbol: str) -> Dict[str, pd.DataFrame]:
        """
        Get multi-timeframe data specifically for chart analysis.
        Returns data for 5m, 15m, and 1h timeframes.
        """
        chart_data = {}

        for timeframe, config in self.timeframes.items():
            max_retries = 2
            retry_count = 0

            while retry_count <= max_retries:
                try:
                    logger.debug(f"Fetching {timeframe} data for {symbol} (attempt {retry_count + 1})")

                    # Get data for this timeframe with retry logic
                    data = self.client.get_historical_klines(
                        symbol, timeframe, config['periods']
                    )

                    if data is not None and not data.empty and len(data) >= 20:
                        # Add technical indicators for chart analysis
                        try:
                            data = self._add_chart_indicators(data)
                            chart_data[timeframe] = data
                            logger.debug(f"✅ Successfully retrieved {len(data)} {timeframe} candles for {symbol}")
                            break  # Success, exit retry loop
                        except Exception as indicator_error:
                            logger.warning(f"⚠️  Failed to add indicators to {timeframe} data for {symbol}: {indicator_error}")
                            # Use data without indicators as fallback
                            chart_data[timeframe] = data
                            break
                    else:
                        data_len = len(data) if data is not None and not data.empty else 0
                        logger.warning(f"⚠️  Insufficient {timeframe} data for {symbol}: {data_len} candles (need 20+)")

                        # Try with reduced period requirement for fallback
                        if retry_count == max_retries and data_len >= 10:
                            logger.info(f"🔄 Using reduced {timeframe} data for {symbol}: {data_len} candles")
                            try:
                                data = self._add_chart_indicators(data)
                                chart_data[timeframe] = data
                                break
                            except:
                                chart_data[timeframe] = data
                                break
                        else:
                            chart_data[timeframe] = None
                            if retry_count < max_retries:
                                retry_count += 1
                                import time
                                time.sleep(0.5)  # Brief pause before retry
                                continue
                            else:
                                break

                except Exception as e:
                    logger.error(f"❌ Error fetching {timeframe} data for {symbol} (attempt {retry_count + 1}): {e}")
                    chart_data[timeframe] = None

                    if retry_count < max_retries:
                        retry_count += 1
                        import time
                        time.sleep(0.5)  # Brief pause before retry
                    else:
                        break

        return chart_data

    def _add_chart_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators specifically for chart analysis."""
        try:
            if len(data) < 20:
                return data

            # Moving averages for trend analysis
            data['MA_10'] = data['close'].rolling(window=10).mean()
            data['MA_20'] = data['close'].rolling(window=20).mean()
            data['MA_50'] = data['close'].rolling(window=min(50, len(data))).mean()

            # RSI for momentum
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['RSI'] = 100 - (100 / (1 + rs))

            # Bollinger Bands for volatility
            bb_period = min(20, len(data))
            data['BB_middle'] = data['close'].rolling(window=bb_period).mean()
            bb_std = data['close'].rolling(window=bb_period).std()
            data['BB_upper'] = data['BB_middle'] + (bb_std * 2)
            data['BB_lower'] = data['BB_middle'] - (bb_std * 2)

            # Volume indicators
            data['Volume_MA'] = data['volume'].rolling(window=min(20, len(data))).mean()
            data['Volume_ratio'] = data['volume'] / data['Volume_MA']

            # Price change for momentum
            data['price_change_1'] = data['close'].pct_change(1)
            data['price_change_5'] = data['close'].pct_change(5)

            # Support and resistance levels (simplified)
            rolling_period = min(20, len(data))
            data['resistance'] = data['high'].rolling(window=rolling_period).max()
            data['support'] = data['low'].rolling(window=rolling_period).min()

            return data

        except Exception as e:
            logger.error(f"Error adding chart indicators: {e}")
            return data
        
    def analyze_symbol_all_timeframes(self, symbol: str) -> Dict:
        """Analyze a symbol across all timeframes."""
        logger.info(f"Multi-timeframe analysis for {symbol}")
        
        timeframe_results = {}
        overall_signals = []
        confidence_scores = []
        
        for timeframe, config in self.timeframes.items():
            try:
                # Get data for this timeframe
                data = self.client.get_historical_klines(
                    symbol, timeframe, config['periods']
                )
                
                if data.empty or len(data) < 20:  # Reduced minimum requirement
                    logger.warning(f"Insufficient data for {symbol} on {timeframe} - got {len(data) if not data.empty else 0} candles")
                    # Try with reduced periods if insufficient data
                    if config['periods'] > 100:
                        logger.info(f"Retrying {symbol} on {timeframe} with reduced periods...")
                        data = self.client.get_historical_klines(symbol, timeframe, 100)
                        if data.empty or len(data) < 20:
                            continue
                    else:
                        continue
                
                # Calculate technical indicators
                data_with_indicators = self.ta.calculate_all_indicators(data)
                
                # Generate signals
                signals = self.ta.generate_signals(data_with_indicators)
                
                # Calculate trend strength
                trend_analysis = self.analyze_trend(data_with_indicators)
                
                # Calculate support/resistance levels
                sr_levels = self.calculate_support_resistance_levels(data_with_indicators)
                
                timeframe_results[timeframe] = {
                    'signals': signals,
                    'trend': trend_analysis,
                    'support_resistance': sr_levels,
                    'weight': config['weight'],
                    'current_price': data['close'].iloc[-1],
                    'volume_profile': self.analyze_volume_profile(data)
                }
                
                # Collect weighted signals
                signal_weight = config['weight']
                if signals['signal'] == 'BUY':
                    overall_signals.append(signal_weight)
                elif signals['signal'] == 'SELL':
                    overall_signals.append(-signal_weight)
                else:
                    overall_signals.append(0)
                
                confidence_scores.append(signals['confidence'] * signal_weight)
                
            except Exception as e:
                logger.error(f"Error analyzing {symbol} on {timeframe}: {e}")
                continue
        
        # Calculate overall recommendation
        overall_recommendation = self.calculate_overall_recommendation(
            timeframe_results, overall_signals, confidence_scores
        )
        
        return {
            'symbol': symbol,
            'timeframe_analysis': timeframe_results,
            'overall_recommendation': overall_recommendation,
            'analysis_timestamp': pd.Timestamp.now()
        }
    
    def analyze_trend(self, data: pd.DataFrame) -> Dict:
        """Analyze trend strength and direction."""
        if len(data) < 50:
            return {'direction': 'NEUTRAL', 'strength': 0}
        
        close = data['close']
        
        # Calculate trend using multiple methods
        
        # 1. Moving Average Trend
        sma_20 = data['SMA_20'].iloc[-1]
        sma_50 = data['SMA_50'].iloc[-1]
        current_price = close.iloc[-1]
        
        ma_trend_score = 0
        if current_price > sma_20 > sma_50:
            ma_trend_score = 2  # Strong uptrend
        elif current_price > sma_20:
            ma_trend_score = 1  # Weak uptrend
        elif current_price < sma_20 < sma_50:
            ma_trend_score = -2  # Strong downtrend
        elif current_price < sma_20:
            ma_trend_score = -1  # Weak downtrend
        
        # 2. Price momentum
        price_change_20 = (current_price - close.iloc[-21]) / close.iloc[-21] if len(close) > 21 else 0
        momentum_score = np.clip(price_change_20 * 10, -2, 2)
        
        # 3. MACD trend
        macd = data['MACD'].iloc[-1]
        macd_signal = data['MACD_Signal'].iloc[-1]
        macd_score = 1 if macd > macd_signal else -1
        
        # 4. ADX-like trend strength (simplified)
        high = data['high']
        low = data['low']
        
        plus_dm = (high.diff().where(high.diff() > low.diff().abs(), 0)).rolling(14).mean()
        minus_dm = (low.diff().abs().where(low.diff().abs() > high.diff(), 0)).rolling(14).mean()
        
        if len(plus_dm.dropna()) > 0 and len(minus_dm.dropna()) > 0:
            plus_di = plus_dm.iloc[-1]
            minus_di = minus_dm.iloc[-1]
            
            if plus_di > minus_di:
                adx_score = 1
            elif minus_di > plus_di:
                adx_score = -1
            else:
                adx_score = 0
        else:
            adx_score = 0
        
        # Combine scores
        total_score = ma_trend_score + momentum_score + macd_score + adx_score
        
        # Determine direction and strength
        if total_score >= 3:
            direction = 'STRONG_UP'
            strength = min(100, abs(total_score) * 15)
        elif total_score >= 1:
            direction = 'UP'
            strength = min(100, abs(total_score) * 20)
        elif total_score <= -3:
            direction = 'STRONG_DOWN'
            strength = min(100, abs(total_score) * 15)
        elif total_score <= -1:
            direction = 'DOWN'
            strength = min(100, abs(total_score) * 20)
        else:
            direction = 'NEUTRAL'
            strength = 0
        
        return {
            'direction': direction,
            'strength': strength,
            'score': total_score,
            'components': {
                'ma_trend': ma_trend_score,
                'momentum': momentum_score,
                'macd': macd_score,
                'directional': adx_score
            }
        }
    
    def calculate_support_resistance_levels(self, data: pd.DataFrame) -> Dict:
        """Calculate dynamic support and resistance levels."""
        high = data['high']
        low = data['low']
        close = data['close']
        current_price = close.iloc[-1]
        
        # Method 1: Pivot Points
        pivot_levels = []
        for i in range(2, len(data) - 2):
            # Local highs (resistance)
            if (high.iloc[i] > high.iloc[i-1] and high.iloc[i] > high.iloc[i-2] and
                high.iloc[i] > high.iloc[i+1] and high.iloc[i] > high.iloc[i+2]):
                pivot_levels.append(('resistance', high.iloc[i]))
            
            # Local lows (support)
            if (low.iloc[i] < low.iloc[i-1] and low.iloc[i] < low.iloc[i-2] and
                low.iloc[i] < low.iloc[i+1] and low.iloc[i] < low.iloc[i+2]):
                pivot_levels.append(('support', low.iloc[i]))
        
        # Method 2: Volume-weighted levels
        volume = data['volume']
        price_volume = close * volume
        
        # Find high-volume price levels
        volume_levels = []
        for i in range(10, len(data)):
            if volume.iloc[i] > volume.rolling(20).mean().iloc[i] * 1.5:
                volume_levels.append(('volume_level', close.iloc[i]))
        
        # Method 3: Moving average levels
        ma_levels = [
            ('ma_support', data['SMA_20'].iloc[-1]),
            ('ma_support', data['SMA_50'].iloc[-1]),
            ('ma_resistance', data['SMA_20'].iloc[-1]),
            ('ma_resistance', data['SMA_50'].iloc[-1])
        ]
        
        # Combine and filter levels
        all_levels = pivot_levels + volume_levels + ma_levels
        
        # Filter levels near current price (within 10%)
        relevant_levels = []
        for level_type, price in all_levels:
            if abs(price - current_price) / current_price <= 0.1:
                relevant_levels.append((level_type, price))
        
        # Separate support and resistance
        support_levels = [price for level_type, price in relevant_levels 
                         if 'support' in level_type and price < current_price]
        resistance_levels = [price for level_type, price in relevant_levels 
                           if 'resistance' in level_type and price > current_price]
        
        # Get closest levels
        nearest_support = max(support_levels) if support_levels else None
        nearest_resistance = min(resistance_levels) if resistance_levels else None
        
        return {
            'nearest_support': nearest_support,
            'nearest_resistance': nearest_resistance,
            'all_support_levels': sorted(support_levels, reverse=True)[:3],
            'all_resistance_levels': sorted(resistance_levels)[:3],
            'current_price': current_price
        }
    
    def analyze_volume_profile(self, data: pd.DataFrame) -> Dict:
        """Analyze volume profile and patterns."""
        volume = data['volume']
        close = data['close']
        
        # Volume trend
        volume_sma = volume.rolling(20).mean()
        current_volume = volume.iloc[-1]
        avg_volume = volume_sma.iloc[-1]
        
        volume_trend = 'INCREASING' if current_volume > avg_volume * 1.2 else \
                      'DECREASING' if current_volume < avg_volume * 0.8 else 'NORMAL'
        
        # Price-volume relationship
        price_change = close.pct_change()
        volume_change = volume.pct_change()
        
        # Calculate correlation with error handling
        try:
            correlation = price_change.corr(volume_change)
            if pd.isna(correlation):
                correlation = 0.0
        except Exception:
            correlation = 0.0
        
        return {
            'volume_trend': volume_trend,
            'volume_ratio': current_volume / avg_volume,
            'price_volume_correlation': correlation,
            'average_volume': avg_volume
        }
    
    def calculate_overall_recommendation(self, timeframe_results: Dict, 
                                       signals: List[float], 
                                       confidence_scores: List[float]) -> Dict:
        """Calculate overall trading recommendation."""
        if not signals:
            return {
                'action': 'HOLD',
                'confidence': 0,
                'reasoning': 'Insufficient data'
            }
        
        # Weighted signal score
        overall_signal = sum(signals)
        overall_confidence = sum(confidence_scores) / sum([tf['weight'] for tf in timeframe_results.values()])
        
        # Trend alignment check
        trend_alignment = self.check_trend_alignment(timeframe_results)
        
        # Volume confirmation
        volume_confirmation = self.check_volume_confirmation(timeframe_results)
        
        # Determine action
        if overall_signal >= 0.6 and trend_alignment >= 0.7:
            action = 'STRONG_BUY'
        elif overall_signal >= 0.3:
            action = 'BUY'
        elif overall_signal <= -0.6 and trend_alignment <= -0.7:
            action = 'STRONG_SELL'
        elif overall_signal <= -0.3:
            action = 'SELL'
        else:
            action = 'HOLD'
        
        # Adjust confidence based on alignment
        final_confidence = overall_confidence * (0.5 + abs(trend_alignment) * 0.5)
        
        return {
            'action': action,
            'confidence': min(100, final_confidence),
            'signal_score': overall_signal,
            'trend_alignment': trend_alignment,
            'volume_confirmation': volume_confirmation,
            'reasoning': self.generate_reasoning(timeframe_results, action)
        }
    
    def check_trend_alignment(self, timeframe_results: Dict) -> float:
        """Check if trends are aligned across timeframes."""
        trend_scores = []
        
        for tf, results in timeframe_results.items():
            trend = results['trend']['direction']
            weight = results['weight']
            
            if trend in ['STRONG_UP', 'UP']:
                trend_scores.append(weight)
            elif trend in ['STRONG_DOWN', 'DOWN']:
                trend_scores.append(-weight)
            else:
                trend_scores.append(0)
        
        return sum(trend_scores) if trend_scores else 0
    
    def check_volume_confirmation(self, timeframe_results: Dict) -> bool:
        """Check if volume confirms the price movement."""
        volume_confirmations = []
        
        for tf, results in timeframe_results.items():
            volume_profile = results['volume_profile']
            if volume_profile['volume_ratio'] > 1.2:
                volume_confirmations.append(True)
            else:
                volume_confirmations.append(False)
        
        return sum(volume_confirmations) >= len(volume_confirmations) * 0.6
    
    def generate_reasoning(self, timeframe_results: Dict, action: str) -> List[str]:
        """Generate human-readable reasoning for the recommendation."""
        reasons = []
        
        for tf, results in timeframe_results.items():
            signal = results['signals']['signal']
            trend = results['trend']['direction']
            
            if signal != 'HOLD':
                reasons.append(f"{tf}: {signal} signal with {trend} trend")
        
        if not reasons:
            reasons.append("No clear signals across timeframes")
        
        return reasons
