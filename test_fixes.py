"""
Test script to verify all fixes are working.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from portfolio import Portfolio
from altcoin_scanner import AltcoinScanner
from utils import load_config

def test_portfolio_sync():
    """Test portfolio balance synchronization."""
    print("🔧 Testing Portfolio Balance Sync...")
    
    try:
        # Initialize client
        client = BinanceClient(testnet=True)
        
        # Get actual balance
        account_info = client.futures_get_account_info()
        if account_info:
            assets = account_info.get('assets', [])
            usdt_asset = next((asset for asset in assets if asset['asset'] == 'USDT'), None)
            
            if usdt_asset:
                actual_balance = float(usdt_asset.get('availableBalance', 0))
                print(f"✅ Actual testnet balance: {actual_balance:.2f} USDT")
                
                # Test portfolio sync
                portfolio = Portfolio(10000.0, 'USDT')
                print(f"   Before sync: {portfolio.balances['USDT']:.2f} USDT")
                
                portfolio.sync_balance(actual_balance)
                print(f"   After sync: {portfolio.balances['USDT']:.2f} USDT")
                
                if portfolio.balances['USDT'] == actual_balance:
                    print("✅ Portfolio sync working correctly")
                    return True
                else:
                    print("❌ Portfolio sync failed")
                    return False
            else:
                print("❌ No USDT asset found")
                return False
        else:
            print("❌ Could not get account info")
            return False
            
    except Exception as e:
        print(f"❌ Error testing portfolio sync: {e}")
        return False

def test_altcoin_scanner():
    """Test altcoin scanner with invalid symbol filtering."""
    print("\n🔧 Testing Altcoin Scanner...")
    
    try:
        config = load_config('config/config.yaml')
        scanner = AltcoinScanner(config.get('altcoin_scanner', {}))
        
        # Test scanning
        altcoins = scanner.scan_altcoins()
        
        if altcoins:
            print(f"✅ Found {len(altcoins)} valid altcoins")
            
            # Check for invalid symbols
            invalid_symbols = ['MERLUSDT', 'CUSDT', 'VELVETUSDT', 'ZKJUSDT', 'BANANAS31USDT']
            found_invalid = [symbol for symbol in altcoins if symbol in invalid_symbols]
            
            if found_invalid:
                print(f"❌ Found invalid symbols: {found_invalid}")
                return False
            else:
                print("✅ No invalid symbols found")
                print(f"   Top 5: {altcoins[:5]}")
                return True
        else:
            print("❌ No altcoins found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing altcoin scanner: {e}")
        return False

def test_binance_connection():
    """Test Binance testnet connection."""
    print("\n🔧 Testing Binance Connection...")
    
    try:
        client = BinanceClient(testnet=True)
        
        # Test ping
        client.client.ping()
        print("✅ Ping successful")
        
        # Test getting a valid symbol
        data = client.get_historical_klines('BTCUSDT', '5m', 10)
        if not data.empty:
            print(f"✅ Market data retrieved: {len(data)} candles")
            return True
        else:
            print("❌ No market data retrieved")
            return False
            
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing All Fixes")
    print("=" * 50)
    
    tests = [
        ("Portfolio Sync", test_portfolio_sync),
        ("Altcoin Scanner", test_altcoin_scanner),
        ("Binance Connection", test_binance_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Bot should work correctly now.")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
