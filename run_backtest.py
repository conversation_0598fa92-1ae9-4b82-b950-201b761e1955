"""
Run enhanced backtesting for the futures trading bot.
Tests the strategy with real historical altcoin data.
"""

import os
import sys
import logging
import json
from datetime import datetime
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from pathlib import Path

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from enhanced_backtest import EnhancedBacktester
from utils import load_config, setup_logging

def main():
    """Run enhanced backtesting."""
    # Setup logging
    setup_logging(log_level="INFO", log_file="logs/backtest.log")
    logger = logging.getLogger(__name__)
    
    logger.info("Starting enhanced backtesting")
    
    # Load configuration
    config = load_config('config/config.yaml')
    if not config:
        logger.error("Failed to load configuration")
        return
    
    # Create backtest directory
    backtest_dir = Path('backtest_results')
    backtest_dir.mkdir(exist_ok=True)
    
    # Initialize backtester
    backtest_config = config.get('backtesting', {})
    backtester = EnhancedBacktester(backtest_config)
    
    # Run backtest
    results = backtester.run_backtest()
    
    if not results:
        logger.error("Backtesting failed")
        return
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = backtest_dir / f"backtest_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Backtest results saved to {results_file}")
    
    # Print summary
    print("\n" + "="*50)
    print("ENHANCED FUTURES STRATEGY BACKTEST RESULTS")
    print("="*50)
    print(f"Period: {results['backtest_period']}")
    print(f"Initial Balance: ${results['initial_balance']:.2f}")
    print(f"Final Balance: ${results['final_balance']:.2f}")
    print(f"Total Return: {results['total_return_pct']:.2f}%")
    print(f"Total Trades: {results['total_trades']}")
    print(f"Win Rate: {results['win_rate_pct']:.2f}%")
    print(f"Profit Factor: {results['profit_factor']:.2f}")
    print(f"Max Drawdown: {results['max_drawdown_pct']:.2f}%")
    print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    print(f"Best Trade: ${results['best_trade']:.2f}")
    print(f"Worst Trade: ${results['worst_trade']:.2f}")
    print(f"Avg Trade Duration: {results['avg_trade_duration_minutes']:.1f} minutes")
    print("="*50)
    print("Context.txt Rules Validated:")
    for rule in results['context_txt_rules_validated']:
        print(f"✓ {rule}")
    print("="*50)
    
    logger.info("Backtesting completed successfully")

if __name__ == "__main__":
    main()
