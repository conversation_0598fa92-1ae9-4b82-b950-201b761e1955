"""
Performance Analytics and Strategy Backtesting System
Real-time performance tracking and strategy optimization.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import os

logger = logging.getLogger(__name__)

@dataclass
class TradeResult:
    """Individual trade result."""
    timestamp: datetime
    symbol: str
    strategy: str
    signal: str
    entry_price: float
    exit_price: float
    quantity: float
    pnl: float
    pnl_percentage: float
    hold_time_minutes: int
    exit_reason: str
    confidence: float
    risk_reward_ratio: float

@dataclass
class StrategyPerformance:
    """Strategy performance metrics."""
    strategy_name: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pnl: float
    avg_pnl: float
    max_win: float
    max_loss: float
    avg_hold_time: float
    sharpe_ratio: float
    max_drawdown: float
    profit_factor: float

class PerformanceAnalytics:
    """
    Comprehensive performance analytics system for strategy optimization.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Data storage
        self.trade_history: List[TradeResult] = []
        self.strategy_metrics: Dict[str, StrategyPerformance] = {}
        
        # Performance tracking
        self.daily_pnl = {}
        self.running_balance = config.get('initial_balance', 10000)
        self.peak_balance = self.running_balance
        self.max_drawdown = 0.0
        
        # Analytics settings
        self.min_trades_for_analysis = config.get('min_trades_for_analysis', 10)
        self.performance_window_days = config.get('performance_window_days', 30)
        
        # File paths
        self.data_dir = config.get('data_dir', 'data')
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Load existing data
        self._load_trade_history()
        
        logger.info("Performance Analytics initialized")
    
    def record_trade(self, trade_data: Dict):
        """Record a completed trade."""
        try:
            trade_result = TradeResult(
                timestamp=datetime.now(),
                symbol=trade_data['symbol'],
                strategy=trade_data.get('strategy', 'unknown'),
                signal=trade_data['signal'],
                entry_price=trade_data['entry_price'],
                exit_price=trade_data['exit_price'],
                quantity=trade_data['quantity'],
                pnl=trade_data['pnl'],
                pnl_percentage=trade_data.get('pnl_percentage', 0),
                hold_time_minutes=trade_data.get('hold_time_minutes', 0),
                exit_reason=trade_data.get('exit_reason', 'unknown'),
                confidence=trade_data.get('confidence', 0),
                risk_reward_ratio=trade_data.get('risk_reward_ratio', 0)
            )
            
            self.trade_history.append(trade_result)
            
            # Update running balance
            self.running_balance += trade_result.pnl
            
            # Update peak balance and drawdown
            if self.running_balance > self.peak_balance:
                self.peak_balance = self.running_balance
            
            current_drawdown = (self.peak_balance - self.running_balance) / self.peak_balance
            if current_drawdown > self.max_drawdown:
                self.max_drawdown = current_drawdown
            
            # Update daily PnL
            today = datetime.now().date()
            if today not in self.daily_pnl:
                self.daily_pnl[today] = 0
            self.daily_pnl[today] += trade_result.pnl
            
            # Update strategy metrics
            self._update_strategy_metrics(trade_result)
            
            # Save to file
            self._save_trade_history()
            
            logger.info(f"Trade recorded: {trade_result.symbol} {trade_result.signal} "
                       f"PnL: ${trade_result.pnl:.2f} ({trade_result.pnl_percentage:.2f}%)")
            
        except Exception as e:
            logger.error(f"Error recording trade: {e}")
    
    def _update_strategy_metrics(self, trade: TradeResult):
        """Update performance metrics for a strategy."""
        try:
            strategy_name = trade.strategy
            
            if strategy_name not in self.strategy_metrics:
                self.strategy_metrics[strategy_name] = StrategyPerformance(
                    strategy_name=strategy_name,
                    total_trades=0,
                    winning_trades=0,
                    losing_trades=0,
                    win_rate=0.0,
                    total_pnl=0.0,
                    avg_pnl=0.0,
                    max_win=0.0,
                    max_loss=0.0,
                    avg_hold_time=0.0,
                    sharpe_ratio=0.0,
                    max_drawdown=0.0,
                    profit_factor=0.0
                )
            
            metrics = self.strategy_metrics[strategy_name]
            
            # Update basic metrics
            metrics.total_trades += 1
            metrics.total_pnl += trade.pnl
            
            if trade.pnl > 0:
                metrics.winning_trades += 1
                metrics.max_win = max(metrics.max_win, trade.pnl)
            else:
                metrics.losing_trades += 1
                metrics.max_loss = min(metrics.max_loss, trade.pnl)
            
            # Calculate derived metrics
            metrics.win_rate = metrics.winning_trades / metrics.total_trades
            metrics.avg_pnl = metrics.total_pnl / metrics.total_trades
            
            # Calculate average hold time
            strategy_trades = [t for t in self.trade_history if t.strategy == strategy_name]
            metrics.avg_hold_time = np.mean([t.hold_time_minutes for t in strategy_trades])
            
            # Calculate profit factor
            gross_profit = sum(t.pnl for t in strategy_trades if t.pnl > 0)
            gross_loss = abs(sum(t.pnl for t in strategy_trades if t.pnl < 0))
            metrics.profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # Calculate Sharpe ratio (simplified)
            if len(strategy_trades) >= 10:
                returns = [t.pnl_percentage for t in strategy_trades]
                metrics.sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            
        except Exception as e:
            logger.error(f"Error updating strategy metrics: {e}")
    
    def get_overall_performance(self) -> Dict:
        """Get overall trading performance summary."""
        try:
            if not self.trade_history:
                return {'status': 'No trades recorded'}
            
            total_trades = len(self.trade_history)
            winning_trades = sum(1 for t in self.trade_history if t.pnl > 0)
            losing_trades = total_trades - winning_trades
            
            total_pnl = sum(t.pnl for t in self.trade_history)
            win_rate = winning_trades / total_trades * 100
            
            avg_win = np.mean([t.pnl for t in self.trade_history if t.pnl > 0]) if winning_trades > 0 else 0
            avg_loss = np.mean([t.pnl for t in self.trade_history if t.pnl < 0]) if losing_trades > 0 else 0
            
            max_win = max(t.pnl for t in self.trade_history)
            max_loss = min(t.pnl for t in self.trade_history)
            
            avg_hold_time = np.mean([t.hold_time_minutes for t in self.trade_history])
            
            # Recent performance (last 10 trades)
            recent_trades = self.trade_history[-10:] if len(self.trade_history) >= 10 else self.trade_history
            recent_win_rate = sum(1 for t in recent_trades if t.pnl > 0) / len(recent_trades) * 100
            recent_pnl = sum(t.pnl for t in recent_trades)
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'total_pnl': total_pnl,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'max_win': max_win,
                'max_loss': max_loss,
                'avg_hold_time_minutes': avg_hold_time,
                'current_balance': self.running_balance,
                'max_drawdown': self.max_drawdown * 100,
                'recent_win_rate': recent_win_rate,
                'recent_pnl': recent_pnl,
                'profit_factor': abs(avg_win / avg_loss) if avg_loss < 0 else float('inf')
            }
            
        except Exception as e:
            logger.error(f"Error getting overall performance: {e}")
            return {'error': str(e)}
    
    def get_strategy_performance(self, strategy_name: str = None) -> Dict:
        """Get performance metrics for specific strategy or all strategies."""
        try:
            if strategy_name:
                if strategy_name in self.strategy_metrics:
                    metrics = self.strategy_metrics[strategy_name]
                    return {
                        'strategy': metrics.strategy_name,
                        'total_trades': metrics.total_trades,
                        'win_rate': metrics.win_rate * 100,
                        'total_pnl': metrics.total_pnl,
                        'avg_pnl': metrics.avg_pnl,
                        'max_win': metrics.max_win,
                        'max_loss': metrics.max_loss,
                        'avg_hold_time': metrics.avg_hold_time,
                        'profit_factor': metrics.profit_factor,
                        'sharpe_ratio': metrics.sharpe_ratio
                    }
                else:
                    return {'error': f'Strategy {strategy_name} not found'}
            else:
                # Return all strategies
                return {
                    strategy: {
                        'total_trades': metrics.total_trades,
                        'win_rate': metrics.win_rate * 100,
                        'total_pnl': metrics.total_pnl,
                        'avg_pnl': metrics.avg_pnl,
                        'profit_factor': metrics.profit_factor
                    }
                    for strategy, metrics in self.strategy_metrics.items()
                }
                
        except Exception as e:
            logger.error(f"Error getting strategy performance: {e}")
            return {'error': str(e)}
    
    def identify_best_strategies(self, min_trades: int = 5) -> List[Dict]:
        """Identify best performing strategies."""
        try:
            qualified_strategies = []
            
            for strategy_name, metrics in self.strategy_metrics.items():
                if metrics.total_trades >= min_trades:
                    # Calculate composite score
                    win_rate_score = metrics.win_rate * 100
                    pnl_score = max(0, metrics.avg_pnl * 10)  # Scale PnL
                    profit_factor_score = min(metrics.profit_factor * 10, 50)  # Cap at 50
                    
                    composite_score = (win_rate_score * 0.4 + 
                                     pnl_score * 0.3 + 
                                     profit_factor_score * 0.3)
                    
                    qualified_strategies.append({
                        'strategy': strategy_name,
                        'composite_score': composite_score,
                        'win_rate': metrics.win_rate * 100,
                        'total_pnl': metrics.total_pnl,
                        'total_trades': metrics.total_trades,
                        'profit_factor': metrics.profit_factor
                    })
            
            # Sort by composite score
            qualified_strategies.sort(key=lambda x: x['composite_score'], reverse=True)
            
            return qualified_strategies
            
        except Exception as e:
            logger.error(f"Error identifying best strategies: {e}")
            return []
    
    def identify_worst_strategies(self, min_trades: int = 5) -> List[Dict]:
        """Identify worst performing strategies that should be disabled."""
        try:
            worst_strategies = []
            
            for strategy_name, metrics in self.strategy_metrics.items():
                if metrics.total_trades >= min_trades:
                    # Identify problematic strategies
                    if (metrics.win_rate < 0.3 or  # Less than 30% win rate
                        metrics.avg_pnl < -1.0 or  # Average loss > $1
                        metrics.profit_factor < 0.8):  # Poor profit factor
                        
                        worst_strategies.append({
                            'strategy': strategy_name,
                            'win_rate': metrics.win_rate * 100,
                            'avg_pnl': metrics.avg_pnl,
                            'total_pnl': metrics.total_pnl,
                            'total_trades': metrics.total_trades,
                            'profit_factor': metrics.profit_factor,
                            'recommendation': 'DISABLE'
                        })
            
            # Sort by worst performance
            worst_strategies.sort(key=lambda x: x['avg_pnl'])
            
            return worst_strategies
            
        except Exception as e:
            logger.error(f"Error identifying worst strategies: {e}")
            return []
    
    def get_daily_performance(self, days: int = 7) -> Dict:
        """Get daily performance for the last N days."""
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)
            
            daily_performance = {}
            
            for i in range(days):
                date = start_date + timedelta(days=i)
                daily_pnl = self.daily_pnl.get(date, 0)
                
                # Count trades for this day
                day_trades = [t for t in self.trade_history 
                             if t.timestamp.date() == date]
                
                daily_performance[date.isoformat()] = {
                    'pnl': daily_pnl,
                    'trades': len(day_trades),
                    'wins': sum(1 for t in day_trades if t.pnl > 0),
                    'losses': sum(1 for t in day_trades if t.pnl <= 0)
                }
            
            return daily_performance
            
        except Exception as e:
            logger.error(f"Error getting daily performance: {e}")
            return {}
    
    def _save_trade_history(self):
        """Save trade history to file."""
        try:
            file_path = os.path.join(self.data_dir, 'trade_history.json')
            
            trade_data = []
            for trade in self.trade_history:
                trade_data.append({
                    'timestamp': trade.timestamp.isoformat(),
                    'symbol': trade.symbol,
                    'strategy': trade.strategy,
                    'signal': trade.signal,
                    'entry_price': trade.entry_price,
                    'exit_price': trade.exit_price,
                    'quantity': trade.quantity,
                    'pnl': trade.pnl,
                    'pnl_percentage': trade.pnl_percentage,
                    'hold_time_minutes': trade.hold_time_minutes,
                    'exit_reason': trade.exit_reason,
                    'confidence': trade.confidence,
                    'risk_reward_ratio': trade.risk_reward_ratio
                })
            
            with open(file_path, 'w') as f:
                json.dump(trade_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving trade history: {e}")
    
    def _load_trade_history(self):
        """Load trade history from file."""
        try:
            file_path = os.path.join(self.data_dir, 'trade_history.json')
            
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    trade_data = json.load(f)
                
                for trade_dict in trade_data:
                    trade = TradeResult(
                        timestamp=datetime.fromisoformat(trade_dict['timestamp']),
                        symbol=trade_dict['symbol'],
                        strategy=trade_dict['strategy'],
                        signal=trade_dict['signal'],
                        entry_price=trade_dict['entry_price'],
                        exit_price=trade_dict['exit_price'],
                        quantity=trade_dict['quantity'],
                        pnl=trade_dict['pnl'],
                        pnl_percentage=trade_dict['pnl_percentage'],
                        hold_time_minutes=trade_dict['hold_time_minutes'],
                        exit_reason=trade_dict['exit_reason'],
                        confidence=trade_dict['confidence'],
                        risk_reward_ratio=trade_dict['risk_reward_ratio']
                    )
                    
                    self.trade_history.append(trade)
                    self._update_strategy_metrics(trade)
                
                logger.info(f"Loaded {len(self.trade_history)} trades from history")
                
        except Exception as e:
            logger.error(f"Error loading trade history: {e}")
    
    def generate_performance_report(self) -> str:
        """Generate a comprehensive performance report."""
        try:
            overall = self.get_overall_performance()
            best_strategies = self.identify_best_strategies()
            worst_strategies = self.identify_worst_strategies()
            daily_perf = self.get_daily_performance()
            
            report = f"""
📊 TRADING PERFORMANCE REPORT
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*60}

📈 OVERALL PERFORMANCE:
Total Trades: {overall.get('total_trades', 0)}
Win Rate: {overall.get('win_rate', 0):.1f}%
Total P&L: ${overall.get('total_pnl', 0):.2f}
Current Balance: ${overall.get('current_balance', 0):.2f}
Max Drawdown: {overall.get('max_drawdown', 0):.1f}%
Recent Win Rate (10 trades): {overall.get('recent_win_rate', 0):.1f}%

🏆 BEST STRATEGIES:
"""
            
            for i, strategy in enumerate(best_strategies[:3], 1):
                report += f"{i}. {strategy['strategy']}: {strategy['win_rate']:.1f}% win rate, ${strategy['total_pnl']:.2f} P&L\n"
            
            if worst_strategies:
                report += f"\n⚠️  STRATEGIES TO REVIEW:\n"
                for strategy in worst_strategies[:3]:
                    report += f"• {strategy['strategy']}: {strategy['win_rate']:.1f}% win rate, ${strategy['avg_pnl']:.2f} avg P&L\n"
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return f"Error generating report: {e}"
