"""
Mean Reversion Strategy Implementation
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Optional
from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)

class MeanReversionStrategy(BaseStrategy):
    """
    Mean Reversion Strategy: Assumes that the price will eventually revert to its mean.
    
    - BUY: When price is below lower bound (oversold, expect reversion up)
    - SELL: When price is above upper bound (overbought, expect reversion down)
    """
    
    def __init__(self, client, config: Dict):
        super().__init__(client, config)
        self.name = "Mean_Reversion"
        
        # Strategy parameters
        self.window = config.get('window', 20)
        self.num_std = config.get('num_std', 1.5)
        self.min_data_points = self.window + 5
        
        logger.info(f"Mean Reversion Strategy initialized: {self.window} period, {self.num_std} std dev")
    
    def mean_reversion_strategy(self, data: pd.DataFrame, window: int, num_std: float) -> pd.DataFrame:
        """
        Compute mean reversion signals.
        
        Args:
            data: Price data with OHLCV
            window: Moving average window
            num_std: Number of standard deviations for bounds
            
        Returns:
            DataFrame with signals
        """
        # Compute rolling mean and standard deviation
        data['rolling_mean'] = data['close'].rolling(window=window).mean()
        data['rolling_std'] = data['close'].rolling(window=window).std()
        
        # Compute upper and lower bounds
        data['upper_bound'] = data['rolling_mean'] + (data['rolling_std'] * num_std)
        data['lower_bound'] = data['rolling_mean'] - (data['rolling_std'] * num_std)
        
        # Generate buy/sell signals
        data['signal'] = 0
        data.loc[data['close'] > data['upper_bound'], 'signal'] = -1  # SELL (overbought condition)
        data.loc[data['close'] < data['lower_bound'], 'signal'] = 1   # BUY (oversold condition)
        
        return data
    
    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """
        Generate trading signal based on mean reversion.
        
        Args:
            data: Historical price data
            symbol: Trading symbol
            
        Returns:
            'BUY', 'SELL', or 'HOLD'
        """
        try:
            if len(data) < self.min_data_points:
                logger.warning(f"[{symbol}] Insufficient data: {len(data)} < {self.min_data_points}")
                return 'HOLD'
            
            # Apply mean reversion strategy
            strategy_data = self.mean_reversion_strategy(
                data.copy(), self.window, self.num_std
            )
            
            # Get the latest data
            latest = strategy_data.iloc[-1]
            
            current_price = latest['close']
            mean_price = latest['rolling_mean']
            upper_bound = latest['upper_bound']
            lower_bound = latest['lower_bound']
            std_dev = latest['rolling_std']
            
            # Calculate deviation from mean
            deviation_from_mean = current_price - mean_price
            deviation_in_std = deviation_from_mean / std_dev if std_dev > 0 else 0
            
            # Generate signals
            if current_price < lower_bound:
                # Price below lower bound - BUY signal (oversold, expect reversion up)
                logger.info(f"[{symbol}] 🚀 BUY signal - Mean Reversion:")
                logger.info(f"[{symbol}]   Price: {current_price:.6f}")
                logger.info(f"[{symbol}]   Mean: {mean_price:.6f}")
                logger.info(f"[{symbol}]   Lower Bound: {lower_bound:.6f}")
                logger.info(f"[{symbol}]   Upper Bound: {upper_bound:.6f}")
                logger.info(f"[{symbol}]   ✅ Price below lower bound ({deviation_in_std:.1f}σ below mean)")
                return 'BUY'
            
            elif current_price > upper_bound:
                # Price above upper bound - SELL signal (overbought, expect reversion down)
                logger.info(f"[{symbol}] 📉 SELL signal - Mean Reversion:")
                logger.info(f"[{symbol}]   Price: {current_price:.6f}")
                logger.info(f"[{symbol}]   Mean: {mean_price:.6f}")
                logger.info(f"[{symbol}]   Lower Bound: {lower_bound:.6f}")
                logger.info(f"[{symbol}]   Upper Bound: {upper_bound:.6f}")
                logger.info(f"[{symbol}]   ✅ Price above upper bound ({deviation_in_std:.1f}σ above mean)")
                return 'SELL'
            
            else:
                # Price within bounds - HOLD
                distance_to_mean_pct = (deviation_from_mean / mean_price) * 100
                logger.info(f"[{symbol}] ➡️  No mean reversion signal:")
                logger.info(f"[{symbol}]   Price: {current_price:.6f}")
                logger.info(f"[{symbol}]   Mean: {mean_price:.6f}")
                logger.info(f"[{symbol}]   Distance from mean: {distance_to_mean_pct:+.2f}% ({deviation_in_std:+.1f}σ)")
                logger.info(f"[{symbol}]   Bounds: [{lower_bound:.6f}, {upper_bound:.6f}]")
                return 'HOLD'
        
        except Exception as e:
            logger.error(f"[{symbol}] Error in Mean Reversion strategy: {e}")
            return 'HOLD'
    
    def get_strategy_info(self) -> Dict:
        """Get strategy information."""
        return {
            'name': self.name,
            'type': 'Mean Reversion',
            'window': self.window,
            'num_std': self.num_std,
            'min_data_points': self.min_data_points,
            'description': f'Mean Reversion {self.window}p, {self.num_std}σ'
        }
    
    def validate_signal(self, data: pd.DataFrame, signal: str) -> bool:
        """
        Validate the generated signal.
        
        Args:
            data: Price data
            signal: Generated signal
            
        Returns:
            True if signal is valid
        """
        try:
            if signal == 'HOLD':
                return True
            
            latest = data.iloc[-1]
            
            # Ensure we have valid values
            required_fields = ['upper_bound', 'lower_bound', 'rolling_mean', 'rolling_std']
            for field in required_fields:
                if pd.isna(latest.get(field)):
                    logger.warning(f"Invalid {field} value: {latest.get(field)}")
                    return False
            
            # Ensure bounds are properly ordered
            if latest['lower_bound'] >= latest['upper_bound']:
                logger.warning(f"Invalid bound order: lower={latest['lower_bound']}, upper={latest['upper_bound']}")
                return False
            
            # Ensure standard deviation is reasonable
            if latest['rolling_std'] <= 0:
                logger.warning(f"Invalid standard deviation: {latest['rolling_std']}")
                return False
            
            # Ensure mean is between bounds
            mean = latest['rolling_mean']
            if mean <= latest['lower_bound'] or mean >= latest['upper_bound']:
                logger.warning(f"Mean outside bounds: mean={mean}, bounds=[{latest['lower_bound']}, {latest['upper_bound']}]")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating signal: {e}")
            return False

    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate mean reversion indicators.

        Args:
            data: Price data

        Returns:
            DataFrame with calculated indicators
        """
        # Calculate rolling mean and standard deviation
        data['rolling_mean'] = data['close'].rolling(window=self.window).mean()
        data['rolling_std'] = data['close'].rolling(window=self.window).std()

        # Calculate upper and lower bounds
        data['upper_bound'] = data['rolling_mean'] + (data['rolling_std'] * self.num_std)
        data['lower_bound'] = data['rolling_mean'] - (data['rolling_std'] * self.num_std)

        return data

    def should_buy(self, data: pd.DataFrame) -> bool:
        """
        Check if conditions are met for a BUY signal.

        Args:
            data: Price data with indicators

        Returns:
            True if should buy
        """
        if len(data) < self.min_data_points:
            return False

        latest = data.iloc[-1]

        # Check if price is below lower bound (oversold, expect reversion up)
        return latest['close'] < latest['lower_bound']

    def should_sell(self, data: pd.DataFrame) -> bool:
        """
        Check if conditions are met for a SELL signal.

        Args:
            data: Price data with indicators

        Returns:
            True if should sell
        """
        if len(data) < self.min_data_points:
            return False

        latest = data.iloc[-1]

        # Check if price is above upper bound (overbought, expect reversion down)
        return latest['close'] > latest['upper_bound']
