# 📱 Android/Termux Network Fixes for Trading Bot

## 🚨 Quick Fix for "Connection Refused" Error

If you're getting this error:
```
❌ Network error: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/ping (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7380416180>: Failed to establish a new connection: [Errno 111] Connection refused'))
```

### 🔧 IMMEDIATE FIXES (Try in order):

#### 1. **Switch Network Connection**
```bash
# Switch from WiFi to Mobile Data (or vice versa)
# Go to Android Settings → WiFi → Turn OFF
# Then use Mobile Data
```

#### 2. **Restart Termux Completely**
```bash
# Close Termux app completely
# Swipe up → Find Termux → Swipe up to close
# Reopen Termux
```

#### 3. **Update Termux Packages**
```bash
pkg update && pkg upgrade -y
```

#### 4. **Test Network Manually**
```bash
# Test if you can reach Binance
curl -I https://api.binance.com

# If this fails, it's a network issue
```

#### 5. **Install/Update Required Packages**
```bash
pkg install python python-pip curl wget
python -m pip install --upgrade pip requests python-binance
```

#### 6. **Use VPN (if carrier blocks crypto)**
```bash
# Install VPN
pkg install openvpn

# Or use Android VPN apps:
# - ProtonVPN (free)
# - Windscribe (free)
# - Cloudflare WARP (free)
```

#### 7. **Change DNS Settings**
```bash
# On Android:
# Settings → WiFi → Advanced → DNS
# Set to: *******, *******
```

#### 8. **Enable Wake Lock**
```bash
# Prevent Android from sleeping network
termux-wake-lock
```

---

## 🔍 DIAGNOSTIC COMMANDS

### Test Network Step by Step:
```bash
# 1. Test basic internet
ping *******

# 2. Test DNS resolution
nslookup api.binance.com

# 3. Test HTTPS connection
curl -I https://api.binance.com

# 4. Run comprehensive test
python test_mobile_network.py
```

### Check Termux Environment:
```bash
# Check Android version
uname -a

# Check Termux info
termux-info

# Check Python installation
python --version
pip --version
```

---

## 🏥 COMMON ISSUES & SOLUTIONS

### Issue: "Connection Refused (Errno 111)"
**Cause**: Mobile carrier blocking crypto APIs or network instability
**Solution**: 
- Switch to different network (WiFi ↔ Mobile Data)
- Use VPN
- Check with carrier about crypto API access

### Issue: "Max Retries Exceeded"
**Cause**: Weak signal or network congestion
**Solution**:
- Move to area with better signal
- Switch to WiFi if on mobile data
- Restart network connection

### Issue: "Name Resolution Failed"
**Cause**: DNS issues
**Solution**:
- Change DNS to *******, *******
- Clear DNS cache: `nslookup api.binance.com`

### Issue: "SSL Certificate Error"
**Cause**: Outdated certificates or proxy interference
**Solution**:
```bash
pkg update ca-certificates
python -m pip install --upgrade certifi
```

---

## 📋 MOBILE-SPECIFIC SETUP

### Complete Android/Termux Setup:
```bash
# 1. Run automated setup
chmod +x setup_android.sh
./setup_android.sh

# 2. Or manual setup:
pkg update && pkg upgrade -y
pkg install python python-pip curl wget git
python -m pip install requests python-binance pandas numpy pyyaml
termux-setup-storage
termux-wake-lock

# 3. Test network
python test_mobile_network.py

# 4. Configure API keys in .env file
nano .env

# 5. Start trading bot
python main.py
```

---

## 🌐 CARRIER-SPECIFIC ISSUES

### Carriers Known to Block Crypto APIs:
- **Verizon**: Sometimes blocks crypto APIs
- **AT&T**: May throttle crypto traffic
- **T-Mobile**: Generally allows crypto APIs
- **International carriers**: Varies by country

### Solutions for Blocked Carriers:
1. **Use VPN**: Most effective solution
2. **WiFi**: Use different WiFi network
3. **Hotspot**: Use another device's hotspot
4. **Contact Carrier**: Ask about crypto API access

---

## 🔄 TROUBLESHOOTING WORKFLOW

```bash
# Step 1: Basic connectivity test
ping *******
# ✅ If works: Internet OK
# ❌ If fails: Check WiFi/Mobile Data

# Step 2: DNS test
nslookup api.binance.com
# ✅ If works: DNS OK
# ❌ If fails: Change DNS settings

# Step 3: HTTPS test
curl -I https://api.binance.com
# ✅ If works: Network OK for trading
# ❌ If fails: Use VPN or switch network

# Step 4: Python test
python test_mobile_network.py
# ✅ If works: Ready for trading bot
# ❌ If fails: Follow specific error fixes

# Step 5: Start trading bot
python main.py
```

---

## 📞 EMERGENCY FIXES

If nothing works, try these emergency fixes:

### Nuclear Option 1: Complete Termux Reset
```bash
# Backup your files first!
cp -r ~/trading-bot ~/trading-bot-backup

# Reset Termux (WARNING: Deletes everything)
# Go to Android Settings → Apps → Termux → Storage → Clear Data
```

### Nuclear Option 2: Use Different Device
- Try running on different Android device
- Use computer instead of mobile
- Use cloud server (AWS, Google Cloud)

### Nuclear Option 3: Alternative Networks
- Use public WiFi (coffee shop, library)
- Use mobile hotspot from different carrier
- Use friend's network connection

---

## 📱 MOBILE TRADING BEST PRACTICES

1. **Keep Termux in Foreground**: Don't switch apps frequently
2. **Use Wake Lock**: `termux-wake-lock` prevents sleep
3. **Stable Connection**: Prefer WiFi over mobile data
4. **Battery Management**: Keep device charged
5. **Network Monitoring**: Watch for connection drops
6. **Backup Plan**: Have alternative network ready

---

## 🆘 STILL NOT WORKING?

If you've tried everything above and still can't connect:

1. **Run Full Diagnostic**:
   ```bash
   python test_mobile_network.py
   ```

2. **Check Logs**:
   ```bash
   tail -f logs/trading.log
   ```

3. **Try Testnet First**:
   - Make sure `TESTNET_MODE=true` in .env
   - Testnet is more forgiving for testing

4. **Contact Support**:
   - Share the exact error message
   - Include output of `python test_mobile_network.py`
   - Mention your carrier and location

Remember: Mobile trading requires stable internet. If your connection is unstable, consider using a computer or cloud server instead.
