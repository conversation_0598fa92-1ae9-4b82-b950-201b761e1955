"""
Test script to verify scalping position management system.
"""

import os
import sys
from dotenv import load_dotenv
import time
import pandas as pd

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from utils import load_config

def test_scalping_system():
    """Test the scalping position management system."""
    print("🎯 Testing Scalping Position Management System")
    print("=" * 70)
    
    try:
        # Import here to avoid circular imports
        sys.path.append(os.path.join(os.path.dirname(__file__)))
        from main import TradingBot
        
        # Initialize bot
        bot = TradingBot()
        
        print("📊 Scalping System Configuration:")
        print(f"   Max positions: {bot.max_positions}")
        print(f"   Profit threshold: {bot.profit_threshold} USDT")
        print(f"   Active positions: {len(bot.active_positions)}")
        
        # Test position limit logic
        print(f"\n🔧 Testing Position Limit Logic:")
        
        # Simulate adding positions
        test_symbols = ['DOGEUSDT', 'ADAUSDT', 'SOLUSDT', 'BTCUSDT']
        
        for i, symbol in enumerate(test_symbols):
            print(f"\n   Testing position {i+1}: {symbol}")
            
            # Check if we can add more positions
            if len(bot.active_positions) >= bot.max_positions:
                print(f"   ✅ Position limit reached ({len(bot.active_positions)}/{bot.max_positions})")
                print(f"   🚫 Would skip {symbol} due to position limit")
                break
            else:
                print(f"   ✅ Can add position ({len(bot.active_positions)}/{bot.max_positions})")
                
                # Simulate adding position
                bot.active_positions[symbol] = {
                    'side': 'BUY',
                    'quantity': 100.0,
                    'entry_price': 1.0,
                    'leverage': 3,
                    'entry_time': pd.Timestamp.now(),
                    'reason': 'Test position'
                }
                bot.position_profits[symbol] = 0.0
                
                print(f"   📊 Added test position for {symbol}")
        
        print(f"\n📊 Final active positions: {len(bot.active_positions)}/{bot.max_positions}")
        print(f"   Positions: {list(bot.active_positions.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing scalping system: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_profit_calculation():
    """Test profit calculation logic."""
    print("\n🎯 Testing Profit Calculation Logic")
    print("=" * 70)
    
    try:
        # Test profit calculation scenarios
        test_cases = [
            {
                'name': 'BUY position profit',
                'side': 'BUY',
                'entry_price': 1.0,
                'current_price': 1.1,
                'quantity': 100.0,
                'leverage': 3,
                'expected_profit': 30.0  # (1.1-1.0)/1.0 * 100 * 3 = 30
            },
            {
                'name': 'SELL position profit',
                'side': 'SELL',
                'entry_price': 1.0,
                'current_price': 0.9,
                'quantity': 100.0,
                'leverage': 3,
                'expected_profit': 30.0  # (1.0-0.9)/1.0 * 100 * 3 = 30
            },
            {
                'name': 'Small profit (below threshold)',
                'side': 'BUY',
                'entry_price': 1.0,
                'current_price': 1.01,
                'quantity': 100.0,
                'leverage': 3,
                'expected_profit': 3.0  # Should be below 5 USDT threshold
            }
        ]
        
        for test_case in test_cases:
            print(f"\n📊 {test_case['name']}:")
            
            entry_price = test_case['entry_price']
            current_price = test_case['current_price']
            quantity = test_case['quantity']
            side = test_case['side']
            leverage = test_case['leverage']
            
            # Calculate profit (same logic as in bot)
            if side == 'BUY':
                price_diff = current_price - entry_price
            else:  # SELL
                price_diff = entry_price - current_price
            
            profit_usdt = (price_diff / entry_price) * (quantity * entry_price) * leverage
            
            print(f"   Entry: {entry_price}, Current: {current_price}")
            print(f"   Quantity: {quantity}, Leverage: {leverage}x")
            print(f"   Calculated profit: {profit_usdt:.2f} USDT")
            print(f"   Expected profit: {test_case['expected_profit']:.2f} USDT")
            
            if abs(profit_usdt - test_case['expected_profit']) < 0.01:
                print(f"   ✅ Calculation correct")
            else:
                print(f"   ❌ Calculation incorrect")
            
            # Check threshold
            if profit_usdt >= 5.0:
                print(f"   🎯 Above profit threshold - would close position")
            else:
                print(f"   ⏳ Below profit threshold - would keep position")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing profit calculation: {e}")
        return False

def show_scalping_features():
    """Show the scalping system features."""
    print("\n🚀 Scalping System Features")
    print("=" * 70)
    
    print("📊 POSITION MANAGEMENT:")
    print("   • Maximum 3 positions at once")
    print("   • Prevents over-exposure to market")
    print("   • Tracks all active positions")
    print()
    
    print("💰 PROFIT TARGETING:")
    print("   • Auto-exit when profit > 5 USDT")
    print("   • Real-time profit monitoring")
    print("   • Considers leverage in calculations")
    print()
    
    print("🔄 RE-ENTRY LOGIC:")
    print("   • Closes profitable positions automatically")
    print("   • Frees up slots for new opportunities")
    print("   • Continuous scalping cycle")
    print()
    
    print("📈 SCALPING BENEFITS:")
    print("   • Quick profit taking")
    print("   • Reduced market exposure")
    print("   • Higher trade frequency")
    print("   • Consistent small gains")

def main():
    """Run scalping management tests."""
    print("🧪 Scalping Position Management Test Suite")
    print("=" * 80)
    
    show_scalping_features()
    
    tests = [
        ("Scalping System", test_scalping_system),
        ("Profit Calculation", test_profit_calculation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All scalping management tests passed!")
        print("\n🚀 Scalping System Ready:")
        print("✅ Maximum 3 positions enforced")
        print("✅ 5 USDT profit threshold configured")
        print("✅ Real-time profit monitoring")
        print("✅ Automatic position closing")
        print("✅ Re-entry logic for continuous scalping")
        
        print("\n🎯 Expected Bot Behavior:")
        print("1. Opens max 3 positions")
        print("2. Monitors profits every cycle")
        print("3. Closes positions when profit > 5 USDT")
        print("4. Immediately looks for new entries")
        print("5. Maintains continuous scalping cycle")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
