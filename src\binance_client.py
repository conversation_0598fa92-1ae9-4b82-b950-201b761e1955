"""
Binance API client wrapper for the trading bot.
"""

import os
import logging
import time
import math
from typing import Dict, List, Optional, Tuple
from binance.client import Client
from binance.exceptions import BinanceAPIException
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class BinanceClient:
    """Wrapper for Binance API client with additional functionality."""
    
    def __init__(self, api_key: str = None, secret_key: str = None, testnet: bool = False):
        """
        Initialize Binance client.
        
        Args:
            api_key: Binance API key
            secret_key: Binance secret key
            testnet: Whether to use testnet
        """
        self.api_key = api_key or os.getenv('BINANCE_API_KEY')
        self.secret_key = secret_key or os.getenv('BINANCE_SECRET_KEY')
        self.testnet = testnet
        
        if not self.api_key or not self.secret_key:
            logger.warning("API keys not provided. Running in read-only mode.")
            self.client = Client(testnet=testnet)
        else:
            # Initialize client with proper testnet configuration
            self.client = Client(
                api_key=self.api_key,
                api_secret=self.secret_key,
                testnet=testnet
            )

            # Set longer timeout and recv window for better connection stability
            self.client.session.timeout = 30

            # Increase recv window for timestamp tolerance (60 seconds for testnet)
            self.client.RECV_WINDOW = 60000

            # Add retry configuration for network resilience
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # Enhanced server time synchronization to prevent timestamp errors
            try:
                logger.info("🔄 Syncing with Binance server time...")
                if self.fix_timestamp_sync():
                    logger.info("✅ Initial timestamp synchronization successful")
                else:
                    logger.warning("⚠️  Initial timestamp sync failed, using basic sync")
                    server_time = self.client.get_server_time()
                    local_time = int(time.time() * 1000)
                    self.timestamp_offset = server_time['serverTime'] - local_time
                    logger.info(f"Basic server time sync: offset = {self.timestamp_offset} ms")
            except Exception as e:
                logger.warning(f"Could not sync server time: {e}")
                self.timestamp_offset = 0

            # Log which environment we're connecting to
            if testnet:
                logger.info("Connecting to Binance Futures Testnet")
                logger.info("Using extended recv window for testnet stability")
            else:
                logger.info("Connecting to Binance Live API")

        # Cache for symbol precision info to avoid repeated API calls
        self.symbol_precision_cache = {}

        # Test network connectivity
        self.test_network_connectivity()

        logger.info("BinanceClient initialized successfully")

    def test_network_connectivity(self):
        """Test network connectivity to Binance API with testnet/mainnet awareness."""
        try:
            # Test basic connectivity (uses testnet if testnet=True)
            server_time = self.client.get_server_time()

            # Show which endpoint we're actually connecting to
            endpoint = "TESTNET" if self.testnet else "MAINNET"
            logger.info(f"✅ Network connectivity OK to {endpoint} - Server time: {server_time['serverTime']}")
            return True

        except Exception as e:
            error_msg = str(e).lower()

            if "connection refused" in error_msg or "errno 111" in error_msg:
                logger.error("❌ Connection Refused Error (Common on Android/Termux)")
                logger.error("🔧 ANDROID/TERMUX FIXES:")
                logger.error("   1. Switch from WiFi to Mobile Data (or vice versa)")
                logger.error("   2. Restart Termux app completely")
                logger.error("   3. Run: pkg update && pkg upgrade")
                logger.error("   4. Run: pkg install curl && curl -I https://api.binance.com")
                logger.error("   5. Try VPN: pkg install openvpn")
                logger.error("   6. Check carrier: Some block crypto APIs")
            elif "max retries exceeded" in error_msg:
                logger.error("❌ Max Retries Exceeded (Mobile Network Issue)")
                logger.error("🔧 MOBILE SOLUTIONS:")
                logger.error("   1. Move to area with better signal strength")
                logger.error("   2. Switch network (WiFi ↔ Mobile Data)")
                logger.error("   3. Run: termux-wake-lock (prevent sleep)")
                logger.error("   4. Restart network interface")
            elif "name resolution" in error_msg or "getaddrinfo failed" in error_msg:
                logger.error("❌ DNS Resolution Error - Cannot resolve Binance domain names")
                logger.error("💡 Try: 1) Check internet connection 2) Change DNS to ******* 3) Use VPN")
            elif "timeout" in error_msg or "timed out" in error_msg:
                logger.error("❌ Connection Timeout - Network is too slow or blocked")
                logger.error("💡 Try: 1) Check firewall 2) Use VPN 3) Check corporate proxy")
            elif "connection" in error_msg:
                logger.error("❌ Connection Error - Cannot reach Binance servers")
                logger.error("💡 Try: 1) Check internet 2) Disable firewall temporarily 3) Use VPN")
            else:
                logger.error(f"❌ Network Error: {e}")

            logger.error("🔧 QUICK ANDROID/TERMUX TEST:")
            logger.error("   • Run: curl -I https://api.binance.com")
            logger.error("   • Run: nslookup api.binance.com")
            logger.error("   • Run: ping *******")
            return False

    def fix_timestamp_sync(self):
        """Enhanced timestamp synchronization with Binance servers."""
        try:
            logger.info("🔄 Syncing with Binance server time...")

            # Get server time multiple times and use average for accuracy
            server_times = []
            for i in range(5):  # Increased from 3 to 5 for better accuracy
                try:
                    server_time = self.client.get_server_time()
                    server_times.append(server_time['serverTime'])
                    time.sleep(0.05)  # Reduced sleep time
                except Exception as e:
                    logger.warning(f"Failed to get server time (attempt {i+1}): {e}")
                    continue

            if not server_times:
                logger.error("Could not get any server time samples")
                return False

            # Calculate average server time
            avg_server_time = sum(server_times) / len(server_times)
            local_time = int(time.time() * 1000)

            # Calculate offset with buffer for network latency
            self.timestamp_offset = int(avg_server_time - local_time)

            # Add small buffer for network latency (testnet can be slower)
            if self.testnet:
                self.timestamp_offset += 1000  # Add 1 second buffer for testnet
            else:
                self.timestamp_offset += 500   # Add 0.5 second buffer for mainnet

            logger.info(f"✅ Timestamp sync completed: offset = {self.timestamp_offset} ms")
            logger.info(f"📊 Server time samples: {len(server_times)}/5 successful")

            # Apply offset to client
            if hasattr(self.client, 'timestamp_offset'):
                self.client.timestamp_offset = self.timestamp_offset

            return True

        except Exception as e:
            logger.error(f"❌ Failed to sync timestamp: {e}")
            return False

    def validate_market_conditions(self, symbol: str) -> bool:
        """
        Validate market conditions before placing order to avoid PERCENT_PRICE errors.

        Args:
            symbol: Trading symbol to validate

        Returns:
            True if market conditions are suitable for trading
        """
        try:
            # Get 24h ticker to check price change and volume
            ticker = self.client.get_24hr_ticker(symbol=symbol)

            if not ticker:
                logger.warning(f"Could not get ticker data for {symbol}")
                return False

            # Check for extreme price movements (might cause PERCENT_PRICE issues)
            price_change_percent = float(ticker['priceChangePercent'])
            volume = float(ticker['volume'])

            # Skip if price moved too much (>15% in 24h might cause issues)
            if abs(price_change_percent) > 15:
                logger.warning(f"⚠️  {symbol}: Extreme price movement {price_change_percent:.2f}% - skipping")
                return False

            # Skip if volume is too low (might cause liquidity issues)
            if volume < 1000:  # Minimum volume threshold
                logger.warning(f"⚠️  {symbol}: Low volume {volume:.0f} - skipping")
                return False

            logger.debug(f"✅ {symbol}: Market conditions OK - {price_change_percent:.2f}% change, {volume:.0f} volume")
            return True

        except Exception as e:
            logger.error(f"Error validating market conditions for {symbol}: {e}")
            return False

    def get_symbol_precision(self, symbol: str) -> Dict:
        """Get precision information for a symbol."""
        if symbol in self.symbol_precision_cache:
            return self.symbol_precision_cache[symbol]

        try:
            # Get futures exchange info
            exchange_info = self.client.futures_exchange_info()

            for symbol_info in exchange_info['symbols']:
                if symbol_info['symbol'] == symbol:
                    # Extract precision info
                    precision_info = {
                        'quantity_precision': symbol_info['quantityPrecision'],
                        'price_precision': symbol_info['pricePrecision'],
                        'base_asset_precision': symbol_info['baseAssetPrecision'],
                        'quote_precision': symbol_info['quotePrecision']
                    }

                    # Extract step size, tick size, and min quantity from filters
                    for filter_info in symbol_info['filters']:
                        if filter_info['filterType'] == 'LOT_SIZE':
                            precision_info['step_size'] = float(filter_info['stepSize'])
                            precision_info['min_qty'] = float(filter_info['minQty'])
                            precision_info['max_qty'] = float(filter_info['maxQty'])
                        elif filter_info['filterType'] == 'PRICE_FILTER':
                            precision_info['tick_size'] = float(filter_info['tickSize'])
                            precision_info['min_price'] = float(filter_info['minPrice'])
                            precision_info['max_price'] = float(filter_info['maxPrice'])
                        elif filter_info['filterType'] == 'MIN_NOTIONAL':
                            precision_info['min_notional'] = float(filter_info['notional'])

                    # Cache the result
                    self.symbol_precision_cache[symbol] = precision_info
                    logger.info(f"Cached precision for {symbol}: qty={precision_info['quantity_precision']}, price={precision_info['price_precision']}")
                    return precision_info

            logger.error(f"Symbol {symbol} not found in exchange info")
            return {}

        except Exception as e:
            logger.error(f"Error getting precision for {symbol}: {e}")
            return {}

    def round_to_precision(self, value: float, precision: int) -> float:
        """Round value to specified decimal precision."""
        return round(float(value), precision)

    def round_to_step_size(self, value: float, step_size: float) -> float:
        """Round value to step size (for quantity)."""
        if step_size == 0:
            return value
        precision = max(0, int(round(-math.log10(step_size))))
        return round(float(value), precision)

    def round_to_tick_size(self, value: float, tick_size: float) -> float:
        """Round value to tick size (for price)."""
        if tick_size == 0:
            return value
        precision = max(0, int(round(-math.log10(tick_size))))
        return round(float(value), precision)

    def get_account_info(self) -> Dict:
        """Get account information."""
        try:
            return self.client.get_account()
        except BinanceAPIException as e:
            logger.error(f"Error getting account info: {e}")
            return {}
    
    def get_balance(self, asset: str) -> float:
        """Get balance for a specific asset."""
        try:
            account = self.get_account_info()
            for balance in account.get('balances', []):
                if balance['asset'] == asset:
                    return float(balance['free'])
            return 0.0
        except Exception as e:
            logger.error(f"Error getting balance for {asset}: {e}")
            return 0.0
    
    def get_symbol_info(self, symbol: str) -> Dict:
        """Get symbol information."""
        try:
            info = self.client.get_symbol_info(symbol)
            return info
        except BinanceAPIException as e:
            logger.error(f"Error getting symbol info for {symbol}: {e}")
            return {}
    
    def get_historical_klines(self, symbol: str, interval: str, limit: int = 500) -> pd.DataFrame:
        """
        Get historical kline data.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            interval: Kline interval (e.g., '1h', '4h', '1d')
            limit: Number of klines to retrieve
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            klines = self.client.get_klines(symbol=symbol, interval=interval, limit=limit)
            
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to appropriate data types
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['open'] = df['open'].astype(float)
            df['high'] = df['high'].astype(float)
            df['low'] = df['low'].astype(float)
            df['close'] = df['close'].astype(float)
            df['volume'] = df['volume'].astype(float)
            
            df.set_index('timestamp', inplace=True)
            return df[['open', 'high', 'low', 'close', 'volume']]
            
        except BinanceAPIException as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_current_price(self, symbol: str) -> float:
        """Get current price for a symbol."""
        try:
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
        except BinanceAPIException as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return 0.0
    
    def place_market_order(self, symbol: str, side: str, quantity: float) -> Dict:
        """
        Place a market order.
        
        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL'
            quantity: Order quantity
            
        Returns:
            Order response
        """
        try:
            order = self.client.order_market(
                symbol=symbol,
                side=side,
                quantity=quantity
            )
            logger.info(f"Market order placed: {side} {quantity} {symbol}")
            return order
        except BinanceAPIException as e:
            logger.error(f"Error placing market order: {e}")
            return {}
    
    def place_limit_order(self, symbol: str, side: str, quantity: float, price: float) -> Dict:
        """
        Place a limit order.
        
        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL'
            quantity: Order quantity
            price: Order price
            
        Returns:
            Order response
        """
        try:
            order = self.client.order_limit(
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=str(price)
            )
            logger.info(f"Limit order placed: {side} {quantity} {symbol} at {price}")
            return order
        except BinanceAPIException as e:
            logger.error(f"Error placing limit order: {e}")
            return {}
    
    def cancel_order(self, symbol: str, order_id: int) -> Dict:
        """Cancel an order."""
        try:
            result = self.client.cancel_order(symbol=symbol, orderId=order_id)
            logger.info(f"Order {order_id} cancelled for {symbol}")
            return result
        except BinanceAPIException as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return {}
    
    def get_open_orders(self, symbol: str = None) -> List[Dict]:
        """Get open orders."""
        try:
            orders = self.client.get_open_orders(symbol=symbol)
            return orders
        except BinanceAPIException as e:
            logger.error(f"Error getting open orders: {e}")
            return []

    # ===== FUTURES-SPECIFIC METHODS =====

    def futures_change_leverage(self, symbol: str, leverage: int) -> Dict:
        """
        Change leverage for a futures symbol.

        Args:
            symbol: Futures symbol (e.g., 'BTCUSDT')
            leverage: Leverage value (1-125)

        Returns:
            Response from API
        """
        try:
            result = self.client.futures_change_leverage(symbol=symbol, leverage=leverage)
            logger.info(f"Leverage changed to {leverage}x for {symbol}")
            return result
        except BinanceAPIException as e:
            logger.error(f"Error changing leverage for {symbol}: {e}")
            return {}

    def futures_get_position_info(self, symbol: str = None) -> List[Dict]:
        """
        Get futures position information.

        Args:
            symbol: Specific symbol to get position for (optional)

        Returns:
            List of position information
        """
        try:
            positions = self.client.futures_position_information(symbol=symbol)
            return positions
        except BinanceAPIException as e:
            logger.error(f"Error getting position info: {e}")
            return []

    def futures_get_account_info(self) -> Dict:
        """Get futures account information with timestamp error handling."""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                return self.client.futures_account()

            except BinanceAPIException as e:
                if "Timestamp for this request is outside of the recvWindow" in str(e):
                    logger.warning(f"Timestamp error (attempt {attempt + 1}/{max_retries}), trying to fix...")

                    # Try to fix timestamp sync
                    if self.fix_timestamp_sync():
                        logger.info("Timestamp sync fixed, retrying...")
                        time.sleep(1)  # Wait a moment before retry
                        continue
                    else:
                        logger.error("Failed to fix timestamp sync")

                logger.error(f"Error getting futures account info: {e}")

                if attempt == max_retries - 1:
                    return {}

                # Wait before retry
                time.sleep(2 ** attempt)  # Exponential backoff

        return {}

    def futures_place_market_order(self, symbol: str, side: str, quantity: float) -> Dict:
        """
        Place a futures market order with proper precision handling.

        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL'
            quantity: Order quantity

        Returns:
            Order response
        """
        try:
            # Validate market conditions first
            if not self.validate_market_conditions(symbol):
                logger.warning(f"❌ Market conditions not suitable for {symbol}")
                return {}

            # Get symbol precision info
            precision_info = self.get_symbol_precision(symbol)
            if not precision_info:
                logger.error(f"Could not get precision info for {symbol}")
                return {}

            # Round quantity to proper precision
            if 'step_size' in precision_info:
                rounded_quantity = self.round_to_step_size(quantity, precision_info['step_size'])
            else:
                rounded_quantity = self.round_to_precision(quantity, precision_info['quantity_precision'])

            logger.info(f"Precision adjustment for {symbol}: {quantity:.8f} → {rounded_quantity:.8f}")

            # Validate minimum quantity
            if rounded_quantity <= 0:
                logger.error(f"Rounded quantity is zero or negative: {rounded_quantity}")
                return {}

            # Check minimum quantity requirement
            min_qty = precision_info.get('min_qty', 0)
            if rounded_quantity < min_qty:
                logger.error(f"Quantity {rounded_quantity} below minimum {min_qty} for {symbol}")
                return {}

            # Check minimum notional value if available
            min_notional = precision_info.get('min_notional', 0)
            if min_notional > 0:
                # We'd need current price to check notional, but for market orders this is usually handled by exchange
                logger.info(f"Minimum notional for {symbol}: {min_notional}")

            # Get current market price for validation
            current_price = self.get_current_price(symbol)
            if not current_price or current_price <= 0:
                logger.error(f"Could not get valid current price for {symbol}")
                return {}

            # Try to place order with enhanced error handling
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # For market orders, we don't specify price but validate market conditions
                    order = self.client.futures_create_order(
                        symbol=symbol,
                        side=side,
                        type='MARKET',
                        quantity=rounded_quantity,
                        timestamp=int(time.time() * 1000) + self.timestamp_offset
                    )
                    logger.info(f"✅ Futures market order placed: {side} {rounded_quantity} {symbol}")
                    return order

                except BinanceAPIException as e:
                    error_code = getattr(e, 'code', None)
                    error_message = str(e)

                    # Handle timestamp errors specifically
                    if error_code == -1021 or 'timestamp' in error_message.lower() or 'recvwindow' in error_message.lower():
                        logger.warning(f"⚠️  Timestamp error (attempt {attempt+1}/{max_retries}): {e}")

                        if attempt < max_retries - 1:
                            logger.info("🔄 Attempting to fix timestamp synchronization...")
                            if self.fix_timestamp_sync():
                                logger.info("✅ Timestamp sync fixed, retrying order...")
                                time.sleep(0.5)  # Brief pause before retry
                                continue
                            else:
                                logger.error("❌ Could not fix timestamp sync")
                                break

                    # Handle PERCENT_PRICE filter errors
                    elif error_code == -4131 or 'PERCENT_PRICE' in error_message:
                        logger.warning(f"⚠️  PERCENT_PRICE filter error for {symbol}: {e}")
                        logger.info("💡 This usually means market conditions are too volatile for this symbol")
                        logger.info("🔄 Skipping this symbol and trying next opportunity...")
                        return {}  # Return empty to skip this symbol

                    # Handle other specific trading errors
                    elif error_code in [-1013, -1111, -2010, -2011]:  # Common trading errors
                        logger.warning(f"⚠️  Trading error for {symbol} (attempt {attempt+1}/{max_retries}): {e}")
                        if attempt < max_retries - 1:
                            time.sleep(1)  # Wait before retry
                            continue
                        else:
                            logger.error(f"❌ Trading error persists after {max_retries} attempts")
                            return {}

                    else:
                        # Other API errors - re-raise to be handled by outer catch
                        raise e

            # If we get here, timestamp retries failed
            logger.error(f"❌ Failed to place order after timestamp retry attempts")
            return {}

        except BinanceAPIException as e:
            logger.error(f"❌ Error placing futures market order for {symbol}: {e}")
            logger.error(f"   Original quantity: {quantity}")
            logger.error(f"   Rounded quantity: {rounded_quantity if 'rounded_quantity' in locals() else 'N/A'}")
            return {}
        except Exception as e:
            logger.error(f"❌ Unexpected error placing futures order for {symbol}: {e}")
            return {}

    def place_futures_order(self, symbol: str, side: str, quantity: float, order_type: str = 'MARKET') -> Dict:
        """
        Unified interface for placing futures orders with enhanced error handling.

        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL'
            quantity: Order quantity
            order_type: Order type (default: 'MARKET')

        Returns:
            Order response dictionary
        """
        if order_type.upper() == 'MARKET':
            return self.futures_place_market_order(symbol, side, quantity)
        else:
            logger.error(f"Order type {order_type} not yet implemented")
            return {}

    def futures_close_position(self, symbol: str) -> Dict:
        """
        Close all positions for a symbol.

        Args:
            symbol: Symbol to close positions for

        Returns:
            Order response
        """
        try:
            # Get current position
            positions = self.futures_get_position_info(symbol)
            if not positions:
                return {}

            position = positions[0]
            position_amt = float(position['positionAmt'])

            if position_amt == 0:
                logger.info(f"No position to close for {symbol}")
                return {}

            # Determine side for closing
            side = 'SELL' if position_amt > 0 else 'BUY'
            quantity = abs(position_amt)

            # Place closing order
            return self.futures_place_market_order(symbol, side, quantity)

        except BinanceAPIException as e:
            logger.error(f"Error closing futures position for {symbol}: {e}")
            return {}

    def futures_get_pnl(self, symbol: str) -> float:
        """
        Get unrealized PnL for a futures position.

        Args:
            symbol: Symbol to check PnL for

        Returns:
            Unrealized PnL
        """
        try:
            positions = self.futures_get_position_info(symbol)
            if not positions:
                return 0.0

            position = positions[0]
            return float(position.get('unRealizedProfit', 0.0))

        except Exception as e:
            logger.error(f"Error getting PnL for {symbol}: {e}")
            return 0.0
