"""
Interactive script to help set up the .env file for live trading.
"""

import os
import getpass
from pathlib import Path

def create_env_file():
    """Interactive setup for .env file."""
    print("🔧 Environment Setup for Live Trading")
    print("=" * 50)
    print("This script will help you create the .env file needed for live trading.")
    print("⚠️  Your API keys will be stored locally in .env file.")
    print("🔒 Make sure to keep your API keys secure and never share them.")
    print()
    
    # Check if .env already exists
    env_file = Path('.env')
    if env_file.exists():
        response = input("⚠️  .env file already exists. Overwrite? (y/N): ").lower()
        if response != 'y':
            print("Setup cancelled.")
            return
    
    print("📝 Please provide the following information:")
    print()
    
    # Get API credentials
    print("1. Binance API Credentials")
    print("   (Get these from Binance > API Management)")
    api_key = input("   Enter your Binance API Key: ").strip()
    
    if not api_key:
        print("❌ API Key is required. Setup cancelled.")
        return
    
    secret_key = getpass.getpass("   Enter your Binance Secret Key (hidden): ").strip()
    
    if not secret_key:
        print("❌ Secret Key is required. Setup cancelled.")
        return
    
    print()
    
    # Get initial balance
    print("2. Initial Balance (for tracking purposes)")
    while True:
        try:
            initial_balance = input("   Enter your initial balance in USDT (e.g., 1000): ").strip()
            if initial_balance:
                float(initial_balance)  # Validate it's a number
                break
            else:
                print("   Please enter a valid number.")
        except ValueError:
            print("   Please enter a valid number.")
    
    print()
    
    # Optional Telegram setup
    print("3. Telegram Notifications (Optional)")
    telegram_setup = input("   Do you want to set up Telegram notifications? (y/N): ").lower()
    
    telegram_token = ""
    telegram_chat_id = ""
    
    if telegram_setup == 'y':
        print("   To set up Telegram notifications:")
        print("   1. Create a bot by messaging @BotFather on Telegram")
        print("   2. Get your chat ID by messaging @userinfobot")
        print()
        
        telegram_token = input("   Enter your Telegram Bot Token (or press Enter to skip): ").strip()
        if telegram_token:
            telegram_chat_id = input("   Enter your Telegram Chat ID: ").strip()
    
    # Create .env content
    env_content = f"""# Binance API Configuration
BINANCE_API_KEY={api_key}
BINANCE_SECRET_KEY={secret_key}

# Initial Balance (for tracking purposes)
INITIAL_BALANCE={initial_balance}
"""
    
    if telegram_token and telegram_chat_id:
        env_content += f"""
# Telegram Notifications
TELEGRAM_TOKEN={telegram_token}
TELEGRAM_CHAT_ID={telegram_chat_id}
"""
    else:
        env_content += """
# Telegram Notifications (Optional)
TELEGRAM_TOKEN=
TELEGRAM_CHAT_ID=
"""
    
    # Write .env file
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print()
        print("✅ .env file created successfully!")
        print()
        print("📋 Next Steps:")
        print("1. Test your setup: python test_live_setup.py")
        print("2. Read the live trading guide: LIVE_TRADING_SETUP.md")
        print("3. Start with small position sizes")
        print("4. Monitor the bot closely when you start")
        print()
        print("⚠️  IMPORTANT SECURITY NOTES:")
        print("- Never share your .env file")
        print("- Add .env to your .gitignore file")
        print("- Use IP restrictions on your API keys")
        print("- Enable 2FA on your Binance account")
        
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")

def main():
    """Main function."""
    try:
        create_env_file()
    except KeyboardInterrupt:
        print("\n\nSetup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
