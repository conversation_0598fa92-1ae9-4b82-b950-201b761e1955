"""
Single Position Manager for Chart-Based Trading
"""

import pandas as pd
import logging
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class ActivePosition:
    """Active position information."""
    symbol: str
    side: str  # 'BUY' or 'SELL'
    entry_price: float
    quantity: float
    leverage: int
    entry_time: pd.Timestamp
    entry_reason: str
    target_price: float
    stop_loss: float
    support_level: float
    resistance_level: float
    chart_pattern: str
    risk_reward_ratio: float

class SinglePositionManager:
    """
    Manages single position trading with chart-based analysis.
    Ensures only 1 active position at a time across all altcoins.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Single position settings
        self.max_positions = 1  # Override any other setting
        self.current_position: Optional[ActivePosition] = None
        
        # Profit taking intervals (from your requirements)
        self.profit_intervals = {
            5: {'min_profit': 0.0, 'description': 'Take any profit > $0'},
            15: {'max_loss': 2.0, 'description': 'Close if loss < $2'},
            30: {'force_close': True, 'description': 'Force close regardless of P&L'}
        }
        
        # Position tracking
        self.position_history = []
        self.last_position_close_time = None
        self.min_time_between_trades = config.get('min_time_between_trades', 2)  # 2 minutes
        
        logger.info("Single Position Manager initialized - Maximum 1 concurrent position")
    
    def has_active_position(self) -> bool:
        """Check if there's an active position."""
        return self.current_position is not None
    
    def get_active_position(self) -> Optional[ActivePosition]:
        """Get the current active position."""
        return self.current_position
    
    def can_open_new_position(self) -> Tuple[bool, str]:
        """Check if a new position can be opened."""
        # Check if already have active position
        if self.has_active_position():
            return False, f"Already have active position: {self.current_position.symbol}"
        
        # Check minimum time between trades
        if self.last_position_close_time:
            time_since_close = pd.Timestamp.now() - self.last_position_close_time
            min_wait = timedelta(minutes=self.min_time_between_trades)
            
            if time_since_close < min_wait:
                remaining = min_wait - time_since_close
                return False, f"Must wait {remaining.total_seconds():.0f}s between trades"
        
        return True, "Ready for new position"
    
    def open_position(self, symbol: str, side: str, entry_price: float, quantity: float,
                     leverage: int, entry_reason: str, target_price: float, stop_loss: float,
                     support_level: float, resistance_level: float, chart_pattern: str,
                     risk_reward_ratio: float) -> bool:
        """Open a new single position."""
        try:
            can_open, reason = self.can_open_new_position()
            if not can_open:
                logger.warning(f"Cannot open position: {reason}")
                return False
            
            # Create new position
            self.current_position = ActivePosition(
                symbol=symbol,
                side=side,
                entry_price=entry_price,
                quantity=quantity,
                leverage=leverage,
                entry_time=pd.Timestamp.now(),
                entry_reason=entry_reason,
                target_price=target_price,
                stop_loss=stop_loss,
                support_level=support_level,
                resistance_level=resistance_level,
                chart_pattern=chart_pattern,
                risk_reward_ratio=risk_reward_ratio
            )
            
            logger.info(f"📊 SINGLE POSITION OPENED:")
            logger.info(f"   Symbol: {symbol}")
            logger.info(f"   Side: {side}")
            logger.info(f"   Entry: ${entry_price:.6f}")
            logger.info(f"   Quantity: {quantity:.6f}")
            logger.info(f"   Leverage: {leverage}x")
            logger.info(f"   Target: ${target_price:.6f}")
            logger.info(f"   Stop Loss: ${stop_loss:.6f}")
            logger.info(f"   Support: ${support_level:.6f}")
            logger.info(f"   Resistance: ${resistance_level:.6f}")
            logger.info(f"   Pattern: {chart_pattern}")
            logger.info(f"   Risk/Reward: {risk_reward_ratio:.2f}")
            logger.info(f"   Reason: {entry_reason}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error opening position: {e}")
            return False
    
    def close_position(self, current_price: float, close_reason: str, 
                      realized_pnl: float = 0.0) -> bool:
        """Close the current position."""
        try:
            if not self.has_active_position():
                logger.warning("No active position to close")
                return False
            
            position = self.current_position
            hold_time = pd.Timestamp.now() - position.entry_time
            hold_minutes = hold_time.total_seconds() / 60
            
            # Calculate P&L if not provided
            if realized_pnl == 0.0:
                if position.side == 'BUY':
                    pnl_pct = (current_price - position.entry_price) / position.entry_price
                else:  # SELL
                    pnl_pct = (position.entry_price - current_price) / position.entry_price
                
                position_value = (position.quantity * position.entry_price) / position.leverage
                realized_pnl = position_value * pnl_pct
            
            logger.info(f"📊 SINGLE POSITION CLOSED:")
            logger.info(f"   Symbol: {position.symbol}")
            logger.info(f"   Side: {position.side}")
            logger.info(f"   Entry: ${position.entry_price:.6f}")
            logger.info(f"   Exit: ${current_price:.6f}")
            logger.info(f"   Hold Time: {hold_minutes:.1f} minutes")
            logger.info(f"   P&L: ${realized_pnl:.2f}")
            logger.info(f"   Reason: {close_reason}")
            
            # Add to history
            position_record = {
                'symbol': position.symbol,
                'side': position.side,
                'entry_price': position.entry_price,
                'exit_price': current_price,
                'quantity': position.quantity,
                'leverage': position.leverage,
                'entry_time': position.entry_time,
                'exit_time': pd.Timestamp.now(),
                'hold_minutes': hold_minutes,
                'realized_pnl': realized_pnl,
                'close_reason': close_reason,
                'entry_reason': position.entry_reason,
                'chart_pattern': position.chart_pattern,
                'risk_reward_ratio': position.risk_reward_ratio
            }
            
            self.position_history.append(position_record)
            
            # Clear current position
            self.current_position = None
            self.last_position_close_time = pd.Timestamp.now()
            
            return True
            
        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return False
    
    def check_profit_taking_rules(self, current_price: float) -> Tuple[bool, str]:
        """Check if position should be closed based on profit-taking rules."""
        if not self.has_active_position():
            return False, "No active position"
        
        position = self.current_position
        hold_time = pd.Timestamp.now() - position.entry_time
        hold_minutes = hold_time.total_seconds() / 60
        
        # Calculate current P&L
        if position.side == 'BUY':
            pnl_pct = (current_price - position.entry_price) / position.entry_price
        else:  # SELL
            pnl_pct = (position.entry_price - current_price) / position.entry_price
        
        position_value = (position.quantity * position.entry_price) / position.leverage
        current_pnl = position_value * pnl_pct
        
        # Check profit-taking rules in order
        for minutes, rule in sorted(self.profit_intervals.items()):
            if hold_minutes >= minutes:
                # 5+ minutes: Take any profit > $0
                if minutes == 5 and 'min_profit' in rule:
                    if current_pnl > rule['min_profit']:
                        return True, f"Profit taking at {hold_minutes:.1f}min: ${current_pnl:.2f} > ${rule['min_profit']}"
                
                # 15+ minutes: Close if loss < $2
                elif minutes == 15 and 'max_loss' in rule:
                    if current_pnl < -rule['max_loss']:
                        return True, f"Loss limit at {hold_minutes:.1f}min: ${current_pnl:.2f} < -${rule['max_loss']}"
                
                # 30+ minutes: Force close regardless of P&L
                elif minutes == 30 and rule.get('force_close'):
                    return True, f"Force close at {hold_minutes:.1f}min: ${current_pnl:.2f} (time limit)"
        
        return False, f"Hold position: {hold_minutes:.1f}min, P&L: ${current_pnl:.2f}"
    
    def check_stop_loss_target(self, current_price: float) -> Tuple[bool, str]:
        """Check if position should be closed due to stop loss or target hit."""
        if not self.has_active_position():
            return False, "No active position"
        
        position = self.current_position
        
        if position.side == 'BUY':
            # Check stop loss
            if current_price <= position.stop_loss:
                return True, f"Stop loss hit: ${current_price:.6f} <= ${position.stop_loss:.6f}"
            
            # Check target
            if current_price >= position.target_price:
                return True, f"Target reached: ${current_price:.6f} >= ${position.target_price:.6f}"
        
        else:  # SELL
            # Check stop loss
            if current_price >= position.stop_loss:
                return True, f"Stop loss hit: ${current_price:.6f} >= ${position.stop_loss:.6f}"
            
            # Check target
            if current_price <= position.target_price:
                return True, f"Target reached: ${current_price:.6f} <= ${position.target_price:.6f}"
        
        return False, "Within stop loss and target range"
    
    def should_close_position(self, current_price: float) -> Tuple[bool, str]:
        """Comprehensive check if position should be closed."""
        if not self.has_active_position():
            return False, "No active position"
        
        # Check stop loss and target first (highest priority)
        should_close_sl, reason_sl = self.check_stop_loss_target(current_price)
        if should_close_sl:
            return True, reason_sl
        
        # Check profit-taking rules
        should_close_pt, reason_pt = self.check_profit_taking_rules(current_price)
        if should_close_pt:
            return True, reason_pt
        
        return False, reason_pt  # Return the profit-taking status message
    
    def get_position_status(self, current_price: float) -> Dict:
        """Get detailed status of current position."""
        if not self.has_active_position():
            return {
                'has_position': False,
                'can_open_new': self.can_open_new_position()[0],
                'message': 'No active position'
            }
        
        position = self.current_position
        hold_time = pd.Timestamp.now() - position.entry_time
        hold_minutes = hold_time.total_seconds() / 60
        
        # Calculate current P&L
        if position.side == 'BUY':
            pnl_pct = (current_price - position.entry_price) / position.entry_price
        else:  # SELL
            pnl_pct = (position.entry_price - current_price) / position.entry_price
        
        position_value = (position.quantity * position.entry_price) / position.leverage
        current_pnl = position_value * pnl_pct
        
        return {
            'has_position': True,
            'symbol': position.symbol,
            'side': position.side,
            'entry_price': position.entry_price,
            'current_price': current_price,
            'quantity': position.quantity,
            'leverage': position.leverage,
            'hold_minutes': hold_minutes,
            'current_pnl': current_pnl,
            'pnl_percentage': pnl_pct * 100,
            'target_price': position.target_price,
            'stop_loss': position.stop_loss,
            'support_level': position.support_level,
            'resistance_level': position.resistance_level,
            'chart_pattern': position.chart_pattern,
            'entry_reason': position.entry_reason,
            'risk_reward_ratio': position.risk_reward_ratio,
            'can_open_new': False
        }
    
    def get_trading_statistics(self) -> Dict:
        """Get trading statistics from position history."""
        if not self.position_history:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'avg_hold_time': 0.0,
                'best_trade': 0.0,
                'worst_trade': 0.0
            }
        
        total_trades = len(self.position_history)
        winning_trades = sum(1 for trade in self.position_history if trade['realized_pnl'] > 0)
        losing_trades = total_trades - winning_trades
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        total_pnl = sum(trade['realized_pnl'] for trade in self.position_history)
        avg_hold_time = sum(trade['hold_minutes'] for trade in self.position_history) / total_trades
        
        pnls = [trade['realized_pnl'] for trade in self.position_history]
        best_trade = max(pnls) if pnls else 0.0
        worst_trade = min(pnls) if pnls else 0.0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_hold_time': avg_hold_time,
            'best_trade': best_trade,
            'worst_trade': worst_trade
        }
