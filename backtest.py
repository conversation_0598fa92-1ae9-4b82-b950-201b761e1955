"""
Backtesting module for trading strategies.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from strategies.sma_crossover import SMACrossoverStrategy
from strategies.rsi_strategy import RSIStrategy
from portfolio import Portfolio
from risk_management import RiskManager
from utils import setup_logging, load_config

class Backtester:
    """Backtesting engine for trading strategies."""
    
    def __init__(self, config_path: str = 'config/config.yaml'):
        """Initialize backtester."""
        self.config = load_config(config_path)
        setup_logging('INFO')
        
        self.client = BinanceClient()
        self.results = {}
        
    def get_historical_data(self, symbol: str, interval: str, 
                          start_date: str, end_date: str = None) -> pd.DataFrame:
        """
        Get historical data for backtesting.
        
        Args:
            symbol: Trading symbol
            interval: Timeframe (1h, 4h, 1d)
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD), defaults to today
            
        Returns:
            Historical OHLCV data
        """
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        # Convert dates to timestamps
        start_ts = int(pd.Timestamp(start_date).timestamp() * 1000)
        end_ts = int(pd.Timestamp(end_date).timestamp() * 1000)
        
        # Get data in chunks (Binance limit is 1000 klines per request)
        all_data = []
        current_ts = start_ts
        
        while current_ts < end_ts:
            try:
                klines = self.client.client.get_historical_klines(
                    symbol, interval, current_ts, end_ts, limit=1000
                )
                
                if not klines:
                    break
                
                df = pd.DataFrame(klines, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_asset_volume', 'number_of_trades',
                    'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
                ])
                
                # Convert to appropriate data types
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    df[col] = df[col].astype(float)
                
                all_data.append(df[['timestamp', 'open', 'high', 'low', 'close', 'volume']])
                
                # Update current timestamp
                current_ts = int(df['timestamp'].iloc[-1].timestamp() * 1000) + 1
                
            except Exception as e:
                print(f"Error getting data: {e}")
                break
        
        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            result.set_index('timestamp', inplace=True)
            result = result[~result.index.duplicated(keep='first')]
            return result.sort_index()
        
        return pd.DataFrame()
    
    def run_backtest(self, strategy_name: str, symbol: str, 
                    start_date: str, end_date: str = None,
                    initial_balance: float = 10000) -> Dict:
        """
        Run backtest for a specific strategy.
        
        Args:
            strategy_name: Name of strategy to test
            symbol: Trading symbol
            start_date: Start date for backtest
            end_date: End date for backtest
            initial_balance: Initial portfolio balance
            
        Returns:
            Backtest results
        """
        print(f"Running backtest for {strategy_name} on {symbol}")
        print(f"Period: {start_date} to {end_date or 'today'}")
        
        # Get historical data
        data = self.get_historical_data(symbol, '1h', start_date, end_date)
        if data.empty:
            print("No data available for backtesting")
            return {}
        
        print(f"Loaded {len(data)} data points")
        
        # Initialize strategy
        strategy_configs = self.config.get('strategies', {})
        
        if strategy_name == 'sma_crossover':
            strategy = SMACrossoverStrategy(strategy_configs.get('sma_crossover', {}))
        elif strategy_name == 'rsi_strategy':
            strategy = RSIStrategy(strategy_configs.get('rsi_strategy', {}))
        else:
            raise ValueError(f"Unknown strategy: {strategy_name}")
        
        # Initialize portfolio and risk manager
        portfolio = Portfolio(initial_balance)
        risk_manager = RiskManager(self.config.get('risk_management', {}))
        
        # Track results
        equity_curve = []
        trades = []
        signals = []
        
        # Run backtest
        for i in range(len(data)):
            current_data = data.iloc[:i+1]
            current_price = current_data['close'].iloc[-1]
            current_time = current_data.index[-1]
            
            # Skip if not enough data for strategy
            if len(current_data) < 30:
                equity_curve.append(initial_balance)
                continue
            
            # Update portfolio with current price
            portfolio.update_position_pnl(symbol, current_price)
            
            # Check for exit conditions
            position = portfolio.positions.get(symbol)
            if position:
                # Check stop loss
                if risk_manager.check_stop_loss(position, current_price):
                    portfolio.close_position(symbol, current_price, current_time)
                    trades.append({
                        'timestamp': current_time,
                        'action': 'CLOSE',
                        'reason': 'Stop Loss',
                        'price': current_price,
                        'pnl': portfolio.trade_history[-1]['realized_pnl'] if portfolio.trade_history else 0
                    })
                # Check take profit
                elif risk_manager.check_take_profit(position, current_price):
                    portfolio.close_position(symbol, current_price, current_time)
                    trades.append({
                        'timestamp': current_time,
                        'action': 'CLOSE',
                        'reason': 'Take Profit',
                        'price': current_price,
                        'pnl': portfolio.trade_history[-1]['realized_pnl'] if portfolio.trade_history else 0
                    })
            
            # Generate signal
            signal = strategy.generate_signal(current_data, symbol)
            signals.append({
                'timestamp': current_time,
                'signal': signal,
                'price': current_price
            })
            
            # Execute trades based on signal
            if signal == 'BUY' and not position:
                # Calculate position size
                portfolio_value = portfolio.get_total_value({symbol: current_price})
                quantity = risk_manager.calculate_position_size(portfolio_value, current_price)
                
                portfolio.add_position(symbol, 'BUY', quantity, current_price, current_time)
                trades.append({
                    'timestamp': current_time,
                    'action': 'BUY',
                    'reason': 'Strategy Signal',
                    'price': current_price,
                    'quantity': quantity
                })
                
            elif signal == 'SELL' and position and position['side'] == 'BUY':
                portfolio.close_position(symbol, current_price, current_time)
                trades.append({
                    'timestamp': current_time,
                    'action': 'SELL',
                    'reason': 'Strategy Signal',
                    'price': current_price,
                    'pnl': portfolio.trade_history[-1]['realized_pnl'] if portfolio.trade_history else 0
                })
            
            # Record equity
            current_equity = portfolio.get_total_value({symbol: current_price})
            equity_curve.append(current_equity)
        
        # Close any remaining positions
        if portfolio.positions:
            final_price = data['close'].iloc[-1]
            final_time = data.index[-1]
            for symbol_pos in list(portfolio.positions.keys()):
                portfolio.close_position(symbol_pos, final_price, final_time)
        
        # Calculate performance metrics
        equity_series = pd.Series(equity_curve, index=data.index[:len(equity_curve)])
        returns = equity_series.pct_change().dropna()
        
        # Calculate metrics
        total_return = (equity_series.iloc[-1] / equity_series.iloc[0] - 1) * 100
        max_drawdown = self.calculate_max_drawdown(equity_series) * 100
        sharpe_ratio = self.calculate_sharpe_ratio(returns)
        win_rate = len([t for t in trades if t.get('pnl', 0) > 0]) / max(len([t for t in trades if 'pnl' in t]), 1)
        
        results = {
            'strategy': strategy_name,
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date or datetime.now().strftime('%Y-%m-%d'),
            'initial_balance': initial_balance,
            'final_balance': equity_series.iloc[-1],
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_trades': len([t for t in trades if 'pnl' in t]),
            'win_rate': win_rate,
            'equity_curve': equity_series,
            'trades': trades,
            'signals': signals,
            'portfolio_metrics': portfolio.get_performance_metrics()
        }
        
        return results
    
    def calculate_max_drawdown(self, equity_curve: pd.Series) -> float:
        """Calculate maximum drawdown."""
        peak = equity_curve.expanding().max()
        drawdown = (equity_curve - peak) / peak
        return abs(drawdown.min())
    
    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.0) -> float:
        """Calculate Sharpe ratio."""
        if returns.std() == 0:
            return 0.0
        excess_returns = returns.mean() * 8760 - risk_free_rate  # Annualized (hourly data)
        volatility = returns.std() * np.sqrt(8760)
        return excess_returns / volatility
    
    def plot_results(self, results: Dict, save_path: str = None):
        """Plot backtest results."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Equity curve
        equity_curve = results['equity_curve']
        ax1.plot(equity_curve.index, equity_curve.values)
        ax1.set_title('Equity Curve')
        ax1.set_ylabel('Portfolio Value')
        ax1.grid(True)
        
        # Drawdown
        peak = equity_curve.expanding().max()
        drawdown = (equity_curve - peak) / peak * 100
        ax2.fill_between(drawdown.index, drawdown.values, 0, alpha=0.3, color='red')
        ax2.set_title('Drawdown')
        ax2.set_ylabel('Drawdown (%)')
        ax2.grid(True)
        
        # Returns distribution
        returns = equity_curve.pct_change().dropna() * 100
        ax3.hist(returns, bins=50, alpha=0.7)
        ax3.set_title('Returns Distribution')
        ax3.set_xlabel('Returns (%)')
        ax3.set_ylabel('Frequency')
        ax3.grid(True)
        
        # Performance metrics
        metrics_text = f"""
        Total Return: {results['total_return']:.2f}%
        Max Drawdown: {results['max_drawdown']:.2f}%
        Sharpe Ratio: {results['sharpe_ratio']:.2f}
        Total Trades: {results['total_trades']}
        Win Rate: {results['win_rate']:.2%}
        """
        ax4.text(0.1, 0.5, metrics_text, transform=ax4.transAxes, fontsize=12,
                verticalalignment='center', bbox=dict(boxstyle='round', facecolor='wheat'))
        ax4.set_title('Performance Metrics')
        ax4.axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to {save_path}")
        
        plt.show()

def main():
    """Main backtesting function."""
    backtester = Backtester()
    
    # Example backtest
    results = backtester.run_backtest(
        strategy_name='sma_crossover',
        symbol='BTCUSDT',
        start_date='2023-01-01',
        end_date='2023-12-31',
        initial_balance=10000
    )
    
    if results:
        print("\nBacktest Results:")
        print(f"Total Return: {results['total_return']:.2f}%")
        print(f"Max Drawdown: {results['max_drawdown']:.2f}%")
        print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
        print(f"Total Trades: {results['total_trades']}")
        print(f"Win Rate: {results['win_rate']:.2%}")
        
        # Plot results
        backtester.plot_results(results, 'backtest_results/sma_crossover_BTCUSDT.png')

if __name__ == "__main__":
    main()
