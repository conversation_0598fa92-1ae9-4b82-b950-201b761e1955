"""
Test script for Scalping Fallback System
"""

import os
import sys
from dotenv import load_dotenv
import pandas as pd
import numpy as np

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_scalping_test_data():
    """Create test data suitable for scalping scenarios."""
    np.random.seed(42)
    
    # Create sideways/ranging market data (good for scalping)
    dates = pd.date_range('2024-01-01', periods=50, freq='1min')
    
    # Base price oscillating in a range
    base_price = 100
    oscillation = 2 * np.sin(np.arange(50) * 0.3)  # Oscillating pattern
    noise = np.random.normal(0, 0.5, 50)
    
    close_prices = base_price + oscillation + noise
    
    # Generate OHLCV data
    data = pd.DataFrame({
        'timestamp': dates,
        'open': close_prices * (1 + np.random.normal(0, 0.002, 50)),
        'high': close_prices * (1 + np.abs(np.random.normal(0, 0.005, 50))),
        'low': close_prices * (1 - np.abs(np.random.normal(0, 0.005, 50))),
        'close': close_prices,
        'volume': np.random.uniform(1000, 5000, 50) * (1 + np.random.choice([0, 1.5], 50, p=[0.7, 0.3]))  # Volume spikes
    })
    
    # Add RSI that oscillates between oversold/overbought
    rsi_base = 50 + 20 * np.sin(np.arange(50) * 0.2)
    data['RSI'] = np.clip(rsi_base + np.random.normal(0, 5, 50), 0, 100)
    
    return data

def test_scalping_signal_generation():
    """Test scalping signal generation."""
    print("🎯 Testing Scalping Signal Generation")
    print("=" * 50)
    
    try:
        from scalping_fallback import ScalpingFallback
        
        # Initialize scalping system
        config = {
            'min_price_movement': 0.002,
            'quick_profit_target': 0.005,
            'scalp_stop_loss': 0.003,
            'max_hold_minutes': 10,
            'rsi_oversold_scalp': 35,
            'rsi_overbought_scalp': 65
        }
        
        scalping = ScalpingFallback(config)
        
        print(f"📊 Scalping Configuration:")
        print(f"   Min price movement: {config['min_price_movement']*100:.1f}%")
        print(f"   Profit target: {config['quick_profit_target']*100:.1f}%")
        print(f"   Stop loss: {config['scalp_stop_loss']*100:.1f}%")
        print(f"   Max hold time: {config['max_hold_minutes']} minutes")
        
        # Create test data
        data = create_scalping_test_data()
        
        # Test different scenarios
        test_scenarios = [
            {'name': 'Normal ranging market', 'data': data},
            {'name': 'High RSI scenario', 'data': data.copy()},
            {'name': 'Low RSI scenario', 'data': data.copy()}
        ]
        
        # Modify RSI for different scenarios
        test_scenarios[1]['data']['RSI'] = 75  # High RSI
        test_scenarios[2]['data']['RSI'] = 25  # Low RSI
        
        signals_generated = 0
        
        for scenario in test_scenarios:
            print(f"\n📊 {scenario['name']}:")
            
            signal = scalping.generate_scalping_signal(scenario['data'], 'TESTUSDT')
            scalping.log_scalping_decision(signal, 'TESTUSDT')
            
            print(f"   Signal: {signal.signal}")
            print(f"   Confidence: {signal.confidence:.1f}%")
            print(f"   Reason: {signal.entry_reason}")
            
            if signal.signal != 'HOLD':
                signals_generated += 1
        
        if signals_generated > 0:
            print(f"\n✅ Scalping signal generation working: {signals_generated} signals generated")
            return True
        else:
            print(f"\n❌ No scalping signals generated")
            return False
        
    except Exception as e:
        print(f"❌ Error testing scalping signals: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scalping_activation_conditions():
    """Test scalping activation conditions."""
    print("\n⚡ Testing Scalping Activation Conditions")
    print("=" * 50)
    
    try:
        from scalping_fallback import ScalpingFallback
        
        scalping = ScalpingFallback({})
        
        # Test different activation scenarios
        test_cases = [
            {
                'name': 'Main signal rejected',
                'main_signal_rejected': True,
                'market_regime': 'bull_trending',
                'time_since_last_trade': 10,
                'expected': True
            },
            {
                'name': 'Sideways market',
                'main_signal_rejected': False,
                'market_regime': 'sideways_ranging',
                'time_since_last_trade': 10,
                'expected': True
            },
            {
                'name': 'Long time since trade',
                'main_signal_rejected': False,
                'market_regime': 'bull_trending',
                'time_since_last_trade': 35,
                'expected': True
            },
            {
                'name': 'No activation conditions',
                'main_signal_rejected': False,
                'market_regime': 'bull_trending',
                'time_since_last_trade': 10,
                'expected': False
            }
        ]
        
        passed = 0
        
        for test_case in test_cases:
            print(f"\n📊 {test_case['name']}:")
            
            should_activate = scalping.should_activate_scalping(
                main_signal_rejected=test_case['main_signal_rejected'],
                market_regime=test_case['market_regime'],
                time_since_last_trade=test_case['time_since_last_trade']
            )
            
            print(f"   Main signal rejected: {test_case['main_signal_rejected']}")
            print(f"   Market regime: {test_case['market_regime']}")
            print(f"   Time since last trade: {test_case['time_since_last_trade']}min")
            print(f"   Should activate: {should_activate}")
            print(f"   Expected: {test_case['expected']}")
            
            if should_activate == test_case['expected']:
                print(f"   ✅ CORRECT")
                passed += 1
            else:
                print(f"   ❌ INCORRECT")
        
        if passed == len(test_cases):
            print(f"\n✅ Scalping activation logic working correctly")
            return True
        else:
            print(f"\n❌ Scalping activation logic failed: {passed}/{len(test_cases)} passed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing scalping activation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scalping_position_sizing():
    """Test scalping position sizing."""
    print("\n💰 Testing Scalping Position Sizing")
    print("=" * 50)
    
    try:
        from scalping_fallback import ScalpingFallback
        
        scalping = ScalpingFallback({})
        
        # Test position size multiplier
        multiplier = scalping.get_scalping_position_size_multiplier()
        
        print(f"📊 Scalping Position Sizing:")
        print(f"   Base position multiplier: {multiplier:.1f}x")
        print(f"   Reasoning: Smaller positions for higher frequency trading")
        
        # Simulate position sizing
        base_position_value = 100  # $100 base position
        scalping_position_value = base_position_value * multiplier
        
        print(f"\n📊 Position Size Example:")
        print(f"   Normal position: ${base_position_value}")
        print(f"   Scalping position: ${scalping_position_value}")
        print(f"   Reduction: {(1-multiplier)*100:.0f}% smaller")
        
        if 0.5 <= multiplier <= 0.8:  # Reasonable range for scalping
            print(f"   ✅ Scalping position sizing appropriate")
            return True
        else:
            print(f"   ❌ Scalping position sizing out of range")
            return False
        
    except Exception as e:
        print(f"❌ Error testing scalping position sizing: {e}")
        return False

def show_scalping_features():
    """Show scalping fallback features."""
    print("🎯 Scalping Fallback System Features")
    print("=" * 60)
    
    print("⚡ ACTIVATION CONDITIONS:")
    print("   • Main signal rejected by quality filter")
    print("   • Market in sideways/ranging mode")
    print("   • No trades for 30+ minutes")
    print("   • Valid trading hours (avoid low liquidity)")
    print()
    
    print("🎯 SCALPING TECHNIQUES:")
    print("   • Price momentum detection (0.2% minimum)")
    print("   • RSI scalping (35/65 levels)")
    print("   • Support/resistance bounces")
    print("   • Volume spike confirmation")
    print()
    
    print("💰 SCALPING PARAMETERS:")
    print("   • Quick profit target: 0.5%")
    print("   • Stop loss: 0.3%")
    print("   • Maximum hold time: 10 minutes")
    print("   • Position size: 70% of normal")
    print()
    
    print("🚀 BENEFITS:")
    print("   • Ensures continuous trading activity")
    print("   • Captures small profits in ranging markets")
    print("   • Prevents long periods without trades")
    print("   • Adapts to different market conditions")

def main():
    """Run scalping fallback tests."""
    print("🧪 Scalping Fallback System Test Suite")
    print("=" * 70)
    
    show_scalping_features()
    
    tests = [
        ("Scalping Signal Generation", test_scalping_signal_generation),
        ("Scalping Activation Conditions", test_scalping_activation_conditions),
        ("Scalping Position Sizing", test_scalping_position_sizing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 SCALPING FALLBACK SYSTEM READY!")
        print("\n✅ Features Implemented:")
        print("   ✅ Scalping signal generation")
        print("   ✅ Smart activation conditions")
        print("   ✅ Reduced position sizing")
        print("   ✅ Quick profit targets")
        print("   ✅ Time-based activation")
        
        print("\n🚀 Expected Bot Behavior:")
        print("   • Trades continuously even when main signals are weak")
        print("   • Activates scalping when no trades for 30+ minutes")
        print("   • Uses smaller positions for quick scalping")
        print("   • Targets 0.5% profits with 0.3% stop loss")
        print("   • Maximum 10-minute hold times")
        print("   • Ensures bot never sits idle for long periods")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
