"""
Advanced AI-powered trading strategy with multi-timeframe analysis.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import logging
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from .base_strategy import BaseStrategy
from advanced_technical_analysis import AdvancedTechnicalAnalysis
from multi_timeframe_analysis import MultiTimeframeAnalysis
from token_scanner import TokenScanner

logger = logging.getLogger(__name__)

class AdvancedAIStrategy(BaseStrategy):
    """
    Advanced AI-powered strategy that:
    1. Automatically scans for profitable tokens
    2. Uses multi-timeframe analysis
    3. Combines technical and fundamental analysis
    4. Adapts to market conditions
    """
    
    def __init__(self, binance_client, config: Dict):
        """Initialize advanced AI strategy."""
        super().__init__("Advanced_AI_Strategy", config)
        
        self.client = binance_client
        self.ta = AdvancedTechnicalAnalysis()
        self.mtf = MultiTimeframeAnalysis(binance_client)
        self.scanner = TokenScanner(binance_client, config.get('scanner', {}))
        
        # Strategy parameters
        self.min_confidence = config.get('min_confidence', 70)
        self.max_positions = config.get('max_positions', 3)
        self.scan_interval = config.get('scan_interval', 6)  # Scan every 6 cycles
        self.scan_counter = 0
        
        # Current watchlist
        self.watchlist = config.get('initial_symbols', ['BTCUSDT', 'ETHUSDT'])
        self.last_scan_time = None
        
        # Performance tracking
        self.symbol_performance = {}
        
        logger.info(f"Advanced AI Strategy initialized with {len(self.watchlist)} symbols")
    
    def update_watchlist(self):
        """Update watchlist with new profitable tokens."""
        try:
            logger.info("Scanning for new profitable tokens...")
            
            # Get top tokens from scanner
            top_tokens = self.scanner.get_top_tokens(n=10)
            
            if top_tokens:
                # Extract symbols and scores
                new_symbols = []
                for token in top_tokens:
                    symbol = token['symbol']
                    score = token['combined_score']
                    recommendation = token['recommendation']
                    
                    if (score >= 60 and 
                        recommendation in ['STRONG_BUY', 'BUY'] and
                        symbol not in self.watchlist):
                        new_symbols.append(symbol)
                        logger.info(f"Added {symbol} to watchlist (score: {score:.1f})")
                
                # Update watchlist (keep top performers + new discoveries)
                self.watchlist = list(set(self.watchlist + new_symbols))
                
                # Limit watchlist size
                if len(self.watchlist) > 15:
                    # Keep symbols with open positions + top performers
                    symbols_with_positions = list(self.positions.keys())
                    other_symbols = [s for s in self.watchlist if s not in symbols_with_positions]
                    
                    # Keep top 10 other symbols based on recent performance
                    other_symbols = other_symbols[:10]
                    self.watchlist = symbols_with_positions + other_symbols
                
                logger.info(f"Updated watchlist: {len(self.watchlist)} symbols")
                self.last_scan_time = pd.Timestamp.now()
                
        except Exception as e:
            logger.error(f"Error updating watchlist: {e}")
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate all technical indicators."""
        return self.ta.calculate_all_indicators(data)
    
    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """Generate trading signal using advanced analysis."""
        try:
            # Update watchlist periodically
            self.scan_counter += 1
            if self.scan_counter >= self.scan_interval:
                self.update_watchlist()
                self.scan_counter = 0
            
            # Skip if symbol not in watchlist (unless we have a position)
            if symbol not in self.watchlist and symbol not in self.positions:
                return 'HOLD'
            
            # Perform multi-timeframe analysis
            mtf_analysis = self.mtf.analyze_symbol_all_timeframes(symbol)
            
            if not mtf_analysis or 'overall_recommendation' not in mtf_analysis:
                return 'HOLD'
            
            recommendation = mtf_analysis['overall_recommendation']
            action = recommendation['action']
            confidence = recommendation['confidence']
            
            # Check minimum confidence threshold
            if confidence < self.min_confidence:
                return 'HOLD'
            
            # Check position limits
            if action in ['BUY', 'STRONG_BUY']:
                if len(self.positions) >= self.max_positions and symbol not in self.positions:
                    logger.info(f"Max positions reached, skipping {symbol}")
                    return 'HOLD'
            
            # Additional risk checks
            risk_check = self.perform_risk_checks(symbol, mtf_analysis)
            if not risk_check['passed']:
                logger.warning(f"Risk check failed for {symbol}: {risk_check['reason']}")
                return 'HOLD'
            
            # Market condition check
            market_condition = self.assess_market_condition(mtf_analysis)
            adjusted_action = self.adjust_for_market_condition(action, market_condition)
            
            # Log detailed analysis
            self.log_analysis(symbol, mtf_analysis, adjusted_action, confidence)
            
            # Convert to standard signals
            if adjusted_action in ['STRONG_BUY', 'BUY']:
                return 'BUY'
            elif adjusted_action in ['STRONG_SELL', 'SELL']:
                return 'SELL'
            else:
                return 'HOLD'
                
        except Exception as e:
            logger.error(f"Error generating signal for {symbol}: {e}")
            return 'HOLD'
    
    def perform_risk_checks(self, symbol: str, analysis: Dict) -> Dict:
        """Perform additional risk checks."""
        try:
            timeframe_results = analysis.get('timeframe_analysis', {})
            
            # Check for conflicting signals across timeframes
            signals = []
            for tf, results in timeframe_results.items():
                signal = results['signals']['signal']
                if signal != 'HOLD':
                    signals.append(signal)
            
            # If more than 50% of signals conflict, it's risky
            if signals:
                buy_signals = signals.count('BUY')
                sell_signals = signals.count('SELL')
                total_signals = len(signals)
                
                if total_signals > 1:
                    conflict_ratio = min(buy_signals, sell_signals) / total_signals
                    if conflict_ratio > 0.4:  # More than 40% conflicting signals
                        return {'passed': False, 'reason': 'Conflicting timeframe signals'}
            
            # Check volatility
            if '1h' in timeframe_results:
                indicators = timeframe_results['1h']['signals'].get('indicators', {})
                atr = indicators.get('atr', 0)
                current_price = timeframe_results['1h']['current_price']
                
                if atr > 0 and current_price > 0:
                    volatility_ratio = atr / current_price
                    if volatility_ratio > 0.1:  # More than 10% ATR
                        return {'passed': False, 'reason': 'Excessive volatility'}
            
            # Check volume
            volume_confirmation = analysis['overall_recommendation'].get('volume_confirmation', False)
            if not volume_confirmation:
                return {'passed': False, 'reason': 'Insufficient volume confirmation'}
            
            return {'passed': True, 'reason': 'All checks passed'}
            
        except Exception as e:
            logger.error(f"Error in risk checks for {symbol}: {e}")
            return {'passed': False, 'reason': 'Risk check error'}
    
    def assess_market_condition(self, analysis: Dict) -> str:
        """Assess overall market condition."""
        try:
            # Use trend alignment as market condition indicator
            trend_alignment = analysis['overall_recommendation'].get('trend_alignment', 0)
            
            if trend_alignment >= 0.7:
                return 'BULLISH'
            elif trend_alignment <= -0.7:
                return 'BEARISH'
            elif abs(trend_alignment) < 0.3:
                return 'SIDEWAYS'
            else:
                return 'MIXED'
                
        except Exception as e:
            logger.error(f"Error assessing market condition: {e}")
            return 'UNKNOWN'
    
    def adjust_for_market_condition(self, action: str, market_condition: str) -> str:
        """Adjust trading action based on market condition."""
        # In bearish markets, be more conservative
        if market_condition == 'BEARISH':
            if action == 'BUY':
                return 'HOLD'  # Avoid buying in bearish market
            elif action == 'STRONG_BUY':
                return 'BUY'   # Reduce strength
        
        # In sideways markets, prefer range trading
        elif market_condition == 'SIDEWAYS':
            if action in ['STRONG_BUY', 'STRONG_SELL']:
                return action.replace('STRONG_', '')  # Reduce strength
        
        # In bullish markets, be more aggressive
        elif market_condition == 'BULLISH':
            if action == 'SELL':
                return 'HOLD'  # Avoid selling in bullish market
        
        return action
    
    def log_analysis(self, symbol: str, analysis: Dict, action: str, confidence: float):
        """Log detailed analysis for transparency."""
        try:
            recommendation = analysis['overall_recommendation']
            
            logger.info(f"=== ANALYSIS: {symbol} ===")
            logger.info(f"Action: {action} (Confidence: {confidence:.1f}%)")
            logger.info(f"Signal Score: {recommendation.get('signal_score', 0):.3f}")
            logger.info(f"Trend Alignment: {recommendation.get('trend_alignment', 0):.3f}")
            logger.info(f"Volume Confirmation: {recommendation.get('volume_confirmation', False)}")
            
            # Log timeframe breakdown
            timeframe_results = analysis.get('timeframe_analysis', {})
            for tf, results in timeframe_results.items():
                signal = results['signals']['signal']
                trend = results['trend']['direction']
                tf_confidence = results['signals']['confidence']
                logger.info(f"  {tf}: {signal} | {trend} | {tf_confidence:.1f}%")
            
            logger.info("=" * 30)
            
        except Exception as e:
            logger.error(f"Error logging analysis: {e}")
    
    def get_strategy_info(self) -> Dict:
        """Get strategy information."""
        return {
            'name': self.name,
            'type': 'Advanced AI Multi-Strategy',
            'watchlist_size': len(self.watchlist),
            'max_positions': self.max_positions,
            'min_confidence': self.min_confidence,
            'scan_interval': self.scan_interval,
            'current_watchlist': self.watchlist[:5],  # Show first 5
            'description': 'AI-powered strategy with automatic token discovery and multi-timeframe analysis'
        }
    
    def get_performance_summary(self) -> Dict:
        """Get performance summary for monitored symbols."""
        summary = {}
        
        for symbol in self.watchlist[:10]:  # Top 10 symbols
            if symbol in self.symbol_performance:
                perf = self.symbol_performance[symbol]
                summary[symbol] = {
                    'signals_generated': perf.get('signals', 0),
                    'last_signal': perf.get('last_signal', 'NONE'),
                    'last_confidence': perf.get('last_confidence', 0)
                }
        
        return summary
