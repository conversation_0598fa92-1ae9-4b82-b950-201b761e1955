"""
Adaptive Risk Management System
Dynamic stop-loss, position sizing, and risk controls based on market conditions.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import talib

logger = logging.getLogger(__name__)

@dataclass
class RiskParameters:
    """Risk parameters for a trade."""
    position_size: float
    stop_loss: float
    take_profit: float
    max_hold_time: int  # minutes
    risk_amount: float
    risk_reward_ratio: float
    volatility_adjustment: float

class AdaptiveRiskManager:
    """
    Advanced risk management system that adapts to market conditions,
    volatility, and trading performance.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Base risk parameters
        self.base_risk_per_trade = config.get('base_risk_per_trade', 0.02)  # 2% per trade
        self.max_risk_per_trade = config.get('max_risk_per_trade', 0.05)   # 5% max
        self.min_risk_reward_ratio = config.get('min_risk_reward_ratio', 2.0)
        
        # Volatility-based adjustments
        self.volatility_lookback = config.get('volatility_lookback', 20)
        self.high_volatility_threshold = config.get('high_volatility_threshold', 0.04)
        self.low_volatility_threshold = config.get('low_volatility_threshold', 0.015)
        
        # Time-based risk controls
        self.max_hold_time_minutes = config.get('max_hold_time_minutes', 120)  # 2 hours
        self.news_event_buffer_minutes = config.get('news_event_buffer_minutes', 30)
        
        # Performance tracking
        self.recent_trades = []
        self.max_recent_trades = 20
        
        # Market condition tracking
        self.market_conditions = {
            'volatility_level': 'medium',
            'trend_strength': 'moderate',
            'volume_activity': 'normal'
        }
        
        logger.info("Adaptive Risk Manager initialized")
    
    def calculate_risk_parameters(self, signal_data: Dict, market_data: pd.DataFrame, 
                                portfolio_balance: float) -> RiskParameters:
        """Calculate adaptive risk parameters for a trade."""
        try:
            # Get market volatility
            volatility = self._calculate_market_volatility(market_data)
            
            # Calculate ATR for dynamic stops
            atr = self._calculate_atr(market_data)
            
            # Assess market conditions
            self._update_market_conditions(market_data)
            
            # Calculate base position size
            base_position_size = self._calculate_base_position_size(
                portfolio_balance, signal_data['confidence'], volatility
            )
            
            # Calculate dynamic stop loss
            dynamic_stop = self._calculate_dynamic_stop_loss(
                signal_data, market_data, atr, volatility
            )
            
            # Calculate take profit levels
            take_profit = self._calculate_take_profit(
                signal_data, market_data, atr, volatility
            )
            
            # Calculate max hold time based on conditions
            max_hold_time = self._calculate_max_hold_time(volatility, signal_data['confidence'])
            
            # Calculate risk amount
            entry_price = signal_data['entry_price']
            risk_amount = abs(entry_price - dynamic_stop) * base_position_size
            
            # Calculate risk/reward ratio
            reward_amount = abs(take_profit - entry_price) * base_position_size
            risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
            
            # Volatility adjustment factor
            volatility_adjustment = self._get_volatility_adjustment(volatility)
            
            return RiskParameters(
                position_size=base_position_size,
                stop_loss=dynamic_stop,
                take_profit=take_profit,
                max_hold_time=max_hold_time,
                risk_amount=risk_amount,
                risk_reward_ratio=risk_reward_ratio,
                volatility_adjustment=volatility_adjustment
            )
            
        except Exception as e:
            logger.error(f"Error calculating risk parameters: {e}")
            # Return conservative defaults
            return self._get_conservative_defaults(signal_data, portfolio_balance)
    
    def _calculate_market_volatility(self, data: pd.DataFrame) -> float:
        """Calculate current market volatility."""
        try:
            if len(data) < self.volatility_lookback:
                return 0.025  # Default volatility
            
            # Calculate price volatility (standard deviation of returns)
            returns = data['close'].pct_change().dropna()
            volatility = returns.tail(self.volatility_lookback).std()
            
            return volatility
            
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.025
    
    def _calculate_atr(self, data: pd.DataFrame) -> float:
        """Calculate Average True Range for dynamic stops."""
        try:
            if len(data) < 14:
                return data['close'].iloc[-1] * 0.02  # 2% default
            
            atr = talib.ATR(
                data['high'].values,
                data['low'].values, 
                data['close'].values,
                timeperiod=14
            )
            
            return atr[-1] if not np.isnan(atr[-1]) else data['close'].iloc[-1] * 0.02
            
        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return data['close'].iloc[-1] * 0.02
    
    def _update_market_conditions(self, data: pd.DataFrame):
        """Update current market condition assessment."""
        try:
            volatility = self._calculate_market_volatility(data)
            
            # Classify volatility
            if volatility > self.high_volatility_threshold:
                self.market_conditions['volatility_level'] = 'high'
            elif volatility < self.low_volatility_threshold:
                self.market_conditions['volatility_level'] = 'low'
            else:
                self.market_conditions['volatility_level'] = 'medium'
            
            # Assess trend strength
            if len(data) >= 20:
                price_change = (data['close'].iloc[-1] - data['close'].iloc[-20]) / data['close'].iloc[-20]
                if abs(price_change) > 0.05:
                    self.market_conditions['trend_strength'] = 'strong'
                elif abs(price_change) < 0.02:
                    self.market_conditions['trend_strength'] = 'weak'
                else:
                    self.market_conditions['trend_strength'] = 'moderate'
            
            # Assess volume activity
            if 'volume' in data.columns and len(data) >= 20:
                recent_volume = data['volume'].tail(5).mean()
                avg_volume = data['volume'].tail(20).mean()
                volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
                
                if volume_ratio > 1.5:
                    self.market_conditions['volume_activity'] = 'high'
                elif volume_ratio < 0.7:
                    self.market_conditions['volume_activity'] = 'low'
                else:
                    self.market_conditions['volume_activity'] = 'normal'
            
        except Exception as e:
            logger.error(f"Error updating market conditions: {e}")
    
    def _calculate_base_position_size(self, portfolio_balance: float, 
                                    confidence: float, volatility: float) -> float:
        """Calculate base position size with confidence and volatility adjustments."""
        try:
            # Base risk amount
            base_risk_amount = portfolio_balance * self.base_risk_per_trade
            
            # Confidence adjustment (higher confidence = larger size)
            confidence_multiplier = 0.5 + (confidence / 100) * 0.5  # 0.5x to 1.0x
            
            # Volatility adjustment (higher volatility = smaller size)
            if volatility > self.high_volatility_threshold:
                volatility_multiplier = 0.7  # Reduce size in high volatility
            elif volatility < self.low_volatility_threshold:
                volatility_multiplier = 1.2  # Increase size in low volatility
            else:
                volatility_multiplier = 1.0
            
            # Performance adjustment based on recent trades
            performance_multiplier = self._get_performance_adjustment()
            
            # Calculate final position size
            adjusted_risk = base_risk_amount * confidence_multiplier * volatility_multiplier * performance_multiplier
            
            # Cap at maximum risk
            max_risk_amount = portfolio_balance * self.max_risk_per_trade
            final_risk_amount = min(adjusted_risk, max_risk_amount)
            
            return final_risk_amount
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return portfolio_balance * 0.01  # 1% conservative default
    
    def _calculate_dynamic_stop_loss(self, signal_data: Dict, market_data: pd.DataFrame,
                                   atr: float, volatility: float) -> float:
        """Calculate dynamic stop loss based on market conditions."""
        try:
            entry_price = signal_data['entry_price']
            signal_direction = signal_data['signal']
            
            # Base stop distance using ATR
            base_stop_distance = atr * 1.5  # 1.5x ATR
            
            # Volatility adjustment
            if volatility > self.high_volatility_threshold:
                volatility_multiplier = 1.5  # Wider stops in high volatility
            elif volatility < self.low_volatility_threshold:
                volatility_multiplier = 0.8  # Tighter stops in low volatility
            else:
                volatility_multiplier = 1.0
            
            # Market condition adjustment
            if self.market_conditions['trend_strength'] == 'strong':
                trend_multiplier = 0.8  # Tighter stops in strong trends
            elif self.market_conditions['trend_strength'] == 'weak':
                trend_multiplier = 1.2  # Wider stops in weak trends
            else:
                trend_multiplier = 1.0
            
            # Calculate final stop distance
            stop_distance = base_stop_distance * volatility_multiplier * trend_multiplier
            
            # Apply stop loss
            if signal_direction == 'BUY':
                stop_loss = entry_price - stop_distance
            else:  # SELL
                stop_loss = entry_price + stop_distance
            
            return stop_loss
            
        except Exception as e:
            logger.error(f"Error calculating dynamic stop loss: {e}")
            # Conservative default
            entry_price = signal_data['entry_price']
            if signal_data['signal'] == 'BUY':
                return entry_price * 0.98  # 2% stop
            else:
                return entry_price * 1.02
    
    def _calculate_take_profit(self, signal_data: Dict, market_data: pd.DataFrame,
                             atr: float, volatility: float) -> float:
        """Calculate take profit level."""
        try:
            entry_price = signal_data['entry_price']
            signal_direction = signal_data['signal']
            
            # Base target using ATR
            base_target_distance = atr * 2.0  # 2x ATR target
            
            # Volatility adjustment for targets
            if volatility > self.high_volatility_threshold:
                volatility_multiplier = 1.3  # Larger targets in high volatility
            elif volatility < self.low_volatility_threshold:
                volatility_multiplier = 0.9  # Smaller targets in low volatility
            else:
                volatility_multiplier = 1.0
            
            # Trend strength adjustment
            if self.market_conditions['trend_strength'] == 'strong':
                trend_multiplier = 1.2  # Larger targets in strong trends
            else:
                trend_multiplier = 1.0
            
            # Calculate final target distance
            target_distance = base_target_distance * volatility_multiplier * trend_multiplier
            
            # Apply take profit
            if signal_direction == 'BUY':
                take_profit = entry_price + target_distance
            else:  # SELL
                take_profit = entry_price - target_distance
            
            return take_profit
            
        except Exception as e:
            logger.error(f"Error calculating take profit: {e}")
            # Conservative default
            entry_price = signal_data['entry_price']
            if signal_data['signal'] == 'BUY':
                return entry_price * 1.02  # 2% target
            else:
                return entry_price * 0.98
    
    def _calculate_max_hold_time(self, volatility: float, confidence: float) -> int:
        """Calculate maximum hold time based on conditions."""
        try:
            base_hold_time = self.max_hold_time_minutes
            
            # Volatility adjustment
            if volatility > self.high_volatility_threshold:
                volatility_multiplier = 0.7  # Shorter holds in high volatility
            elif volatility < self.low_volatility_threshold:
                volatility_multiplier = 1.3  # Longer holds in low volatility
            else:
                volatility_multiplier = 1.0
            
            # Confidence adjustment
            confidence_multiplier = 0.8 + (confidence / 100) * 0.4  # 0.8x to 1.2x
            
            # Calculate final hold time
            max_hold_time = int(base_hold_time * volatility_multiplier * confidence_multiplier)
            
            # Ensure reasonable bounds
            return max(30, min(max_hold_time, 240))  # 30 min to 4 hours
            
        except Exception as e:
            logger.error(f"Error calculating max hold time: {e}")
            return 120  # 2 hours default
    
    def _get_volatility_adjustment(self, volatility: float) -> float:
        """Get volatility adjustment factor."""
        if volatility > self.high_volatility_threshold:
            return 0.8  # Reduce exposure in high volatility
        elif volatility < self.low_volatility_threshold:
            return 1.1  # Increase exposure in low volatility
        else:
            return 1.0
    
    def _get_performance_adjustment(self) -> float:
        """Get performance-based adjustment multiplier."""
        try:
            if len(self.recent_trades) < 5:
                return 1.0  # No adjustment with insufficient data
            
            # Calculate recent win rate
            recent_wins = sum(1 for trade in self.recent_trades[-10:] if trade['pnl'] > 0)
            recent_total = len(self.recent_trades[-10:])
            win_rate = recent_wins / recent_total
            
            # Calculate recent average PnL
            recent_pnl = [trade['pnl'] for trade in self.recent_trades[-10:]]
            avg_pnl = np.mean(recent_pnl)
            
            # Adjust based on performance
            if win_rate > 0.7 and avg_pnl > 0:
                return 1.2  # Increase size after good performance
            elif win_rate < 0.3 or avg_pnl < 0:
                return 0.7  # Reduce size after poor performance
            else:
                return 1.0
                
        except Exception as e:
            logger.error(f"Error calculating performance adjustment: {e}")
            return 1.0
    
    def _get_conservative_defaults(self, signal_data: Dict, portfolio_balance: float) -> RiskParameters:
        """Get conservative default risk parameters."""
        entry_price = signal_data['entry_price']
        signal_direction = signal_data['signal']
        
        if signal_direction == 'BUY':
            stop_loss = entry_price * 0.985  # 1.5% stop
            take_profit = entry_price * 1.03  # 3% target
        else:
            stop_loss = entry_price * 1.015
            take_profit = entry_price * 0.97
        
        return RiskParameters(
            position_size=portfolio_balance * 0.01,  # 1% risk
            stop_loss=stop_loss,
            take_profit=take_profit,
            max_hold_time=90,  # 1.5 hours
            risk_amount=portfolio_balance * 0.01,
            risk_reward_ratio=2.0,
            volatility_adjustment=1.0
        )
    
    def update_trade_result(self, trade_data: Dict):
        """Update recent trade results for performance tracking."""
        try:
            trade_record = {
                'timestamp': datetime.now(),
                'symbol': trade_data.get('symbol'),
                'pnl': trade_data.get('pnl', 0),
                'hold_time': trade_data.get('hold_time', 0),
                'exit_reason': trade_data.get('exit_reason', 'unknown')
            }
            
            self.recent_trades.append(trade_record)
            
            # Limit recent trades
            if len(self.recent_trades) > self.max_recent_trades:
                self.recent_trades = self.recent_trades[-self.max_recent_trades:]
                
        except Exception as e:
            logger.error(f"Error updating trade result: {e}")
    
    def get_risk_summary(self) -> Dict:
        """Get current risk management summary."""
        try:
            recent_performance = self._get_performance_adjustment()
            
            return {
                'market_conditions': self.market_conditions,
                'recent_trades_count': len(self.recent_trades),
                'performance_adjustment': recent_performance,
                'base_risk_per_trade': self.base_risk_per_trade,
                'max_risk_per_trade': self.max_risk_per_trade,
                'min_risk_reward_ratio': self.min_risk_reward_ratio
            }
            
        except Exception as e:
            logger.error(f"Error getting risk summary: {e}")
            return {}
