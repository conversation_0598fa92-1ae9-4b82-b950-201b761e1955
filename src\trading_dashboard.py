"""
Real-Time Trading Dashboard for Chart-Based Analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import seaborn as sns
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple
import json
import os

logger = logging.getLogger(__name__)

class TradingDashboard:
    """
    Real-time trading dashboard for visualizing chart-based analysis.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Dashboard settings
        self.update_interval = config.get('update_interval', 30)  # seconds
        self.max_history = config.get('max_history', 100)  # data points
        self.grid_cols = config.get('grid_cols', 4)
        self.grid_rows = config.get('grid_rows', 3)
        
        # Data storage
        self.symbol_data = {}
        self.analysis_history = {}
        self.error_log = []
        self.trade_history = []
        
        # Dashboard state
        self.active_symbols = []
        self.filters = {
            'min_volatility': 0.002,
            'max_volatility': 0.05,
            'min_volume_ratio': 1.0,
            'show_errors_only': False,
            'signal_types': ['BUY', 'SELL', 'HOLD']
        }
        
        # Setup matplotlib
        plt.style.use('dark_background')
        self.fig = None
        self.axes = {}
        
        logger.info("Trading Dashboard initialized")
    
    def update_symbol_data(self, symbol: str, data: Dict):
        """Update data for a specific symbol."""
        try:
            timestamp = datetime.now()
            
            if symbol not in self.symbol_data:
                self.symbol_data[symbol] = {
                    'timestamps': [],
                    'prices': [],
                    'bb_upper': [],
                    'bb_lower': [],
                    'bb_middle': [],
                    'bb_position': [],
                    'bb_width': [],
                    'mean_distance': [],
                    'breakout_position': [],
                    'signals': [],
                    'errors': [],
                    'volume_ratio': []
                }
            
            # Add new data point
            symbol_data = self.symbol_data[symbol]
            symbol_data['timestamps'].append(timestamp)
            symbol_data['prices'].append(data.get('price', 0))
            symbol_data['bb_upper'].append(data.get('bb_upper', 0))
            symbol_data['bb_lower'].append(data.get('bb_lower', 0))
            symbol_data['bb_middle'].append(data.get('bb_middle', 0))
            symbol_data['bb_position'].append(data.get('bb_position', 50))
            symbol_data['bb_width'].append(data.get('bb_width', 0))
            symbol_data['mean_distance'].append(data.get('mean_distance', 0))
            symbol_data['breakout_position'].append(data.get('breakout_position', 50))
            symbol_data['signals'].append(data.get('signal', 'HOLD'))
            symbol_data['errors'].append(data.get('error', None))
            symbol_data['volume_ratio'].append(data.get('volume_ratio', 1.0))
            
            # Limit history
            for key in symbol_data:
                if len(symbol_data[key]) > self.max_history:
                    symbol_data[key] = symbol_data[key][-self.max_history:]
            
            # Update active symbols list
            if symbol not in self.active_symbols:
                self.active_symbols.append(symbol)
                
        except Exception as e:
            logger.error(f"Error updating symbol data for {symbol}: {e}")
    
    def log_error(self, symbol: str, error_type: str, error_message: str):
        """Log an error for dashboard display."""
        error_entry = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'type': error_type,
            'message': error_message
        }
        self.error_log.append(error_entry)
        
        # Limit error log
        if len(self.error_log) > 100:
            self.error_log = self.error_log[-100:]
    
    def log_trade(self, symbol: str, side: str, price: float, quantity: float, reason: str):
        """Log a trade for dashboard display."""
        trade_entry = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'side': side,
            'price': price,
            'quantity': quantity,
            'reason': reason
        }
        self.trade_history.append(trade_entry)
        
        # Limit trade history
        if len(self.trade_history) > 50:
            self.trade_history = self.trade_history[-50:]
    
    def apply_filters(self, symbols: List[str]) -> List[str]:
        """Apply filters to symbol list."""
        filtered_symbols = []
        
        for symbol in symbols:
            if symbol not in self.symbol_data:
                continue
                
            data = self.symbol_data[symbol]
            if not data['timestamps']:
                continue
            
            # Get latest data
            latest_bb_width = data['bb_width'][-1] if data['bb_width'] else 0
            latest_volume_ratio = data['volume_ratio'][-1] if data['volume_ratio'] else 1
            latest_signal = data['signals'][-1] if data['signals'] else 'HOLD'
            has_errors = any(data['errors'])
            
            # Apply filters
            if latest_bb_width < self.filters['min_volatility']:
                continue
            if latest_bb_width > self.filters['max_volatility']:
                continue
            if latest_volume_ratio < self.filters['min_volume_ratio']:
                continue
            if self.filters['show_errors_only'] and not has_errors:
                continue
            if latest_signal not in self.filters['signal_types']:
                continue
                
            filtered_symbols.append(symbol)
        
        return filtered_symbols
    
    def create_mini_chart(self, ax, symbol: str):
        """Create a mini chart for a symbol."""
        try:
            if symbol not in self.symbol_data:
                ax.text(0.5, 0.5, f'{symbol}\nNo Data', ha='center', va='center', 
                       transform=ax.transAxes, fontsize=10, color='gray')
                return
            
            data = self.symbol_data[symbol]
            if not data['timestamps'] or len(data['prices']) < 2:
                ax.text(0.5, 0.5, f'{symbol}\nInsufficient Data', ha='center', va='center',
                       transform=ax.transAxes, fontsize=10, color='gray')
                return
            
            # Get recent data (last 50 points)
            recent_count = min(50, len(data['timestamps']))
            timestamps = data['timestamps'][-recent_count:]
            prices = data['prices'][-recent_count:]
            bb_upper = data['bb_upper'][-recent_count:]
            bb_lower = data['bb_lower'][-recent_count:]
            bb_middle = data['bb_middle'][-recent_count:]
            signals = data['signals'][-recent_count:]
            
            # Clear axis
            ax.clear()
            
            # Plot price line
            ax.plot(timestamps, prices, color='white', linewidth=1.5, alpha=0.8)
            
            # Plot Bollinger Bands
            if bb_upper and bb_lower and bb_middle:
                ax.fill_between(timestamps, bb_upper, bb_lower, alpha=0.2, color='blue')
                ax.plot(timestamps, bb_upper, color='blue', linewidth=0.5, alpha=0.7)
                ax.plot(timestamps, bb_lower, color='blue', linewidth=0.5, alpha=0.7)
                ax.plot(timestamps, bb_middle, color='yellow', linewidth=0.5, alpha=0.7)
            
            # Add signal markers
            for i, (ts, price, signal) in enumerate(zip(timestamps, prices, signals)):
                if signal == 'BUY':
                    ax.scatter(ts, price, color='green', marker='^', s=30, alpha=0.8)
                elif signal == 'SELL':
                    ax.scatter(ts, price, color='red', marker='v', s=30, alpha=0.8)
            
            # Get latest values for display
            latest_price = prices[-1]
            latest_bb_position = data['bb_position'][-1] if data['bb_position'] else 50
            latest_bb_width = data['bb_width'][-1] if data['bb_width'] else 0
            latest_signal = signals[-1]
            has_error = data['errors'][-1] is not None if data['errors'] else False
            
            # Format title with key metrics
            title_color = 'red' if has_error else ('green' if latest_signal == 'BUY' else ('red' if latest_signal == 'SELL' else 'white'))
            title = f'{symbol}\n${latest_price:.6f}'
            
            # Add status indicators
            status_text = f'BB: {latest_bb_position:.1f}% | W: {latest_bb_width:.4f}'
            if has_error:
                status_text += ' | ⚠️'
            
            ax.set_title(title, fontsize=9, color=title_color, fontweight='bold')
            ax.text(0.02, 0.02, status_text, transform=ax.transAxes, fontsize=7, 
                   color='gray', verticalalignment='bottom')
            
            # Signal indicator
            signal_color = {'BUY': 'green', 'SELL': 'red', 'HOLD': 'gray'}
            ax.text(0.98, 0.02, latest_signal, transform=ax.transAxes, fontsize=8,
                   color=signal_color.get(latest_signal, 'gray'), 
                   horizontalalignment='right', verticalalignment='bottom',
                   fontweight='bold')
            
            # Format axes
            ax.tick_params(axis='both', which='major', labelsize=6)
            ax.tick_params(axis='x', rotation=45)
            
            # Remove some ticks for cleaner look
            ax.locator_params(axis='x', nbins=3)
            ax.locator_params(axis='y', nbins=4)
            
        except Exception as e:
            logger.error(f"Error creating mini chart for {symbol}: {e}")
            ax.text(0.5, 0.5, f'{symbol}\nChart Error', ha='center', va='center',
                   transform=ax.transAxes, fontsize=10, color='red')
    
    def create_error_panel(self, ax):
        """Create error alerts panel."""
        try:
            ax.clear()
            ax.set_title('🚨 Error Alerts', fontsize=12, color='red', fontweight='bold')
            
            if not self.error_log:
                ax.text(0.5, 0.5, 'No Errors', ha='center', va='center',
                       transform=ax.transAxes, fontsize=10, color='green')
                ax.set_xticks([])
                ax.set_yticks([])
                return
            
            # Show recent errors
            recent_errors = self.error_log[-10:]  # Last 10 errors
            
            error_text = []
            for i, error in enumerate(reversed(recent_errors)):
                time_str = error['timestamp'].strftime('%H:%M:%S')
                error_line = f"{time_str} | {error['symbol']} | {error['type']}"
                error_text.append(error_line)
            
            # Display errors
            ax.text(0.02, 0.98, '\n'.join(error_text), transform=ax.transAxes,
                   fontsize=8, color='orange', verticalalignment='top',
                   fontfamily='monospace')
            
            ax.set_xticks([])
            ax.set_yticks([])
            
        except Exception as e:
            logger.error(f"Error creating error panel: {e}")
    
    def create_summary_panel(self, ax):
        """Create trading summary panel."""
        try:
            ax.clear()
            ax.set_title('📊 Trading Summary', fontsize=12, color='cyan', fontweight='bold')
            
            # Calculate summary stats
            total_symbols = len(self.active_symbols)
            symbols_with_signals = sum(1 for symbol in self.active_symbols 
                                     if symbol in self.symbol_data and 
                                     self.symbol_data[symbol]['signals'] and
                                     self.symbol_data[symbol]['signals'][-1] != 'HOLD')
            
            total_trades = len(self.trade_history)
            recent_errors = len([e for e in self.error_log 
                               if e['timestamp'] > datetime.now() - timedelta(minutes=30)])
            
            # Create summary text
            summary_lines = [
                f"Active Symbols: {total_symbols}",
                f"With Signals: {symbols_with_signals}",
                f"Total Trades: {total_trades}",
                f"Recent Errors: {recent_errors}",
                f"",
                f"Filters:",
                f"  Volatility: {self.filters['min_volatility']:.3f} - {self.filters['max_volatility']:.3f}",
                f"  Volume Ratio: ≥{self.filters['min_volume_ratio']:.1f}",
                f"  Signals: {', '.join(self.filters['signal_types'])}",
                f"",
                f"Last Update: {datetime.now().strftime('%H:%M:%S')}"
            ]
            
            ax.text(0.02, 0.98, '\n'.join(summary_lines), transform=ax.transAxes,
                   fontsize=9, color='white', verticalalignment='top',
                   fontfamily='monospace')
            
            ax.set_xticks([])
            ax.set_yticks([])
            
        except Exception as e:
            logger.error(f"Error creating summary panel: {e}")
    
    def update_dashboard(self):
        """Update the entire dashboard."""
        try:
            if self.fig is None:
                self.create_dashboard()
                return
            
            # Apply filters to get symbols to display
            filtered_symbols = self.apply_filters(self.active_symbols)
            
            # Calculate grid layout
            total_charts = min(len(filtered_symbols), (self.grid_rows - 1) * self.grid_cols)
            
            # Update mini charts
            chart_count = 0
            for row in range(self.grid_rows - 1):  # Reserve last row for panels
                for col in range(self.grid_cols):
                    if chart_count < total_charts:
                        symbol = filtered_symbols[chart_count]
                        ax_key = f'chart_{row}_{col}'
                        if ax_key in self.axes:
                            self.create_mini_chart(self.axes[ax_key], symbol)
                        chart_count += 1
                    else:
                        # Clear unused axes
                        ax_key = f'chart_{row}_{col}'
                        if ax_key in self.axes:
                            self.axes[ax_key].clear()
                            self.axes[ax_key].set_xticks([])
                            self.axes[ax_key].set_yticks([])
            
            # Update panels
            if 'error_panel' in self.axes:
                self.create_error_panel(self.axes['error_panel'])
            if 'summary_panel' in self.axes:
                self.create_summary_panel(self.axes['summary_panel'])
            
            # Refresh display
            self.fig.canvas.draw()
            
        except Exception as e:
            logger.error(f"Error updating dashboard: {e}")
    
    def create_dashboard(self):
        """Create the main dashboard layout."""
        try:
            # Create figure
            self.fig = plt.figure(figsize=(16, 12))
            self.fig.suptitle('🚀 Real-Time Trading Dashboard', fontsize=16, color='cyan', fontweight='bold')
            
            # Create grid layout
            gs = self.fig.add_gridspec(self.grid_rows, self.grid_cols, hspace=0.4, wspace=0.3)
            
            # Create mini chart axes
            for row in range(self.grid_rows - 1):  # Reserve last row for panels
                for col in range(self.grid_cols):
                    ax = self.fig.add_subplot(gs[row, col])
                    self.axes[f'chart_{row}_{col}'] = ax
            
            # Create panel axes (last row)
            last_row = self.grid_rows - 1
            self.axes['error_panel'] = self.fig.add_subplot(gs[last_row, :2])  # First 2 columns
            self.axes['summary_panel'] = self.fig.add_subplot(gs[last_row, 2:])  # Last 2 columns
            
            # Set dark theme
            self.fig.patch.set_facecolor('black')
            
            logger.info("Dashboard layout created")
            
        except Exception as e:
            logger.error(f"Error creating dashboard: {e}")
    
    def show_dashboard(self):
        """Show the dashboard."""
        try:
            if self.fig is None:
                self.create_dashboard()
            
            self.update_dashboard()
            plt.show(block=False)
            
        except Exception as e:
            logger.error(f"Error showing dashboard: {e}")
    
    def save_dashboard(self, filename: str = None):
        """Save dashboard as image."""
        try:
            if filename is None:
                filename = f"trading_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            
            if self.fig is not None:
                self.fig.savefig(filename, dpi=150, bbox_inches='tight', 
                               facecolor='black', edgecolor='none')
                logger.info(f"Dashboard saved as {filename}")
            
        except Exception as e:
            logger.error(f"Error saving dashboard: {e}")
    
    def set_filter(self, filter_name: str, value):
        """Set a dashboard filter."""
        if filter_name in self.filters:
            self.filters[filter_name] = value
            logger.info(f"Filter {filter_name} set to {value}")
        else:
            logger.warning(f"Unknown filter: {filter_name}")
    
    def export_data(self, filename: str = None):
        """Export dashboard data to JSON."""
        try:
            if filename is None:
                filename = f"dashboard_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            export_data = {
                'timestamp': datetime.now().isoformat(),
                'active_symbols': self.active_symbols,
                'filters': self.filters,
                'error_count': len(self.error_log),
                'trade_count': len(self.trade_history),
                'symbol_summary': {}
            }
            
            # Add symbol summaries
            for symbol in self.active_symbols:
                if symbol in self.symbol_data:
                    data = self.symbol_data[symbol]
                    if data['timestamps']:
                        export_data['symbol_summary'][symbol] = {
                            'latest_price': data['prices'][-1] if data['prices'] else 0,
                            'latest_signal': data['signals'][-1] if data['signals'] else 'HOLD',
                            'bb_position': data['bb_position'][-1] if data['bb_position'] else 50,
                            'bb_width': data['bb_width'][-1] if data['bb_width'] else 0,
                            'has_error': data['errors'][-1] is not None if data['errors'] else False
                        }
            
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            logger.info(f"Dashboard data exported to {filename}")
            
        except Exception as e:
            logger.error(f"Error exporting data: {e}")
