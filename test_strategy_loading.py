"""
Test script to verify strategies can be loaded without abstract method errors.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_strategy_imports():
    """Test that all strategies can be imported without errors."""
    print("📦 Testing Strategy Imports")
    print("=" * 50)
    
    try:
        from binance_client import BinanceClient
        print("✅ BinanceClient imported successfully")
        
        from strategies.moving_average_crossover import MovingAverageCrossoverStrategy
        print("✅ MovingAverageCrossoverStrategy imported successfully")
        
        from strategies.bollinger_bands import BollingerBandsStrategy
        print("✅ BollingerBandsStrategy imported successfully")
        
        from strategies.mean_reversion import MeanReversionStrategy
        print("✅ MeanReversionStrategy imported successfully")
        
        from strategies.breakout import BreakoutStrategy
        print("✅ BreakoutStrategy imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_instantiation():
    """Test that all strategies can be instantiated without abstract method errors."""
    print("\n🏗️ Testing Strategy Instantiation")
    print("=" * 50)
    
    try:
        from binance_client import BinanceClient
        from strategies.moving_average_crossover import MovingAverageCrossoverStrategy
        from strategies.bollinger_bands import BollingerBandsStrategy
        from strategies.mean_reversion import MeanReversionStrategy
        from strategies.breakout import BreakoutStrategy
        
        # Initialize client
        client = BinanceClient(testnet=True)
        print("✅ BinanceClient initialized")
        
        # Test Moving Average Crossover
        ma_config = {'short_window': 50, 'long_window': 200}
        ma_strategy = MovingAverageCrossoverStrategy(client, ma_config)
        print("✅ MovingAverageCrossoverStrategy instantiated")
        
        # Test Bollinger Bands
        bb_config = {'window': 20, 'num_std': 2, 'touch_threshold': 0.001}
        bb_strategy = BollingerBandsStrategy(client, bb_config)
        print("✅ BollingerBandsStrategy instantiated")
        
        # Test Mean Reversion
        mr_config = {'window': 20, 'num_std': 1.5}
        mr_strategy = MeanReversionStrategy(client, mr_config)
        print("✅ MeanReversionStrategy instantiated")
        
        # Test Breakout
        breakout_config = {'window': 20, 'breakout_threshold': 0.001}
        breakout_strategy = BreakoutStrategy(client, breakout_config)
        print("✅ BreakoutStrategy instantiated")
        
        return True
        
    except Exception as e:
        print(f"❌ Instantiation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_abstract_methods():
    """Test that all abstract methods are implemented."""
    print("\n🔍 Testing Abstract Method Implementation")
    print("=" * 50)
    
    try:
        from binance_client import BinanceClient
        from strategies.moving_average_crossover import MovingAverageCrossoverStrategy
        import pandas as pd
        import numpy as np
        
        # Create test data
        data = pd.DataFrame({
            'open': np.random.uniform(100, 110, 250),
            'high': np.random.uniform(105, 115, 250),
            'low': np.random.uniform(95, 105, 250),
            'close': np.random.uniform(100, 110, 250),
            'volume': np.random.uniform(1000, 10000, 250)
        })
        
        # Initialize strategy
        client = BinanceClient(testnet=True)
        config = {'short_window': 50, 'long_window': 200}
        strategy = MovingAverageCrossoverStrategy(client, config)
        
        # Test calculate_indicators method
        indicators_data = strategy.calculate_indicators(data.copy())
        print("✅ calculate_indicators method works")
        
        # Test generate_signal method
        signal = strategy.generate_signal(indicators_data, 'TESTUSDT')
        print(f"✅ generate_signal method works (returned: {signal})")
        
        # Test should_buy method
        should_buy = strategy.should_buy(indicators_data)
        print(f"✅ should_buy method works (returned: {should_buy})")
        
        # Test should_sell method
        should_sell = strategy.should_sell(indicators_data)
        print(f"✅ should_sell method works (returned: {should_sell})")
        
        return True
        
    except Exception as e:
        print(f"❌ Abstract method error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_bot_strategy_loading():
    """Test that main bot can load strategies without errors."""
    print("\n🤖 Testing Main Bot Strategy Loading")
    print("=" * 50)
    
    try:
        # Import main bot components
        sys.path.append(os.path.join(os.path.dirname(__file__)))
        from utils import load_config
        
        # Load config
        config = load_config('config/config.yaml')
        strategy_configs = config.get('strategies', {})
        
        print(f"📊 Strategy configurations loaded:")
        for strategy_name, strategy_config in strategy_configs.items():
            enabled = strategy_config.get('enabled', False)
            status = "✅ ENABLED" if enabled else "⚪ DISABLED"
            print(f"   {strategy_name}: {status}")
        
        # Test strategy loading logic (similar to main bot)
        from binance_client import BinanceClient
        client = BinanceClient(testnet=True)
        strategies = []
        
        # Test each enabled strategy
        if strategy_configs.get('moving_average_crossover', {}).get('enabled', False):
            from strategies.moving_average_crossover import MovingAverageCrossoverStrategy
            ma_config = strategy_configs.get('moving_average_crossover', {})
            strategies.append(MovingAverageCrossoverStrategy(client, ma_config))
            print("✅ Moving Average Crossover loaded")
        
        if strategy_configs.get('bollinger_bands', {}).get('enabled', False):
            from strategies.bollinger_bands import BollingerBandsStrategy
            bb_config = strategy_configs.get('bollinger_bands', {})
            strategies.append(BollingerBandsStrategy(client, bb_config))
            print("✅ Bollinger Bands loaded")
        
        if strategy_configs.get('mean_reversion', {}).get('enabled', False):
            from strategies.mean_reversion import MeanReversionStrategy
            mr_config = strategy_configs.get('mean_reversion', {})
            strategies.append(MeanReversionStrategy(client, mr_config))
            print("✅ Mean Reversion loaded")
        
        if strategy_configs.get('breakout', {}).get('enabled', False):
            from strategies.breakout import BreakoutStrategy
            breakout_config = strategy_configs.get('breakout', {})
            strategies.append(BreakoutStrategy(client, breakout_config))
            print("✅ Breakout loaded")
        
        print(f"\n📊 Total strategies loaded: {len(strategies)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Main bot loading error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all strategy loading tests."""
    print("🧪 Strategy Loading Test Suite")
    print("=" * 70)
    
    print("🎯 Testing that all strategies can be loaded without abstract method errors")
    
    tests = [
        ("Strategy Imports", test_strategy_imports),
        ("Strategy Instantiation", test_strategy_instantiation),
        ("Abstract Methods", test_abstract_methods),
        ("Main Bot Loading", test_main_bot_strategy_loading)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 ALL ABSTRACT METHOD ISSUES FIXED!")
        print("\n✅ Fixes Applied:")
        print("   ✅ calculate_indicators method added to all strategies")
        print("   ✅ should_buy method added to all strategies")
        print("   ✅ should_sell method added to all strategies")
        print("   ✅ generate_signal method working in all strategies")
        print("   ✅ All strategies can be instantiated without errors")
        print("   ✅ Main bot can load strategies successfully")
        
        print("\n🚀 The bot should now start without abstract method errors!")
        print("   Run: python main.py")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
