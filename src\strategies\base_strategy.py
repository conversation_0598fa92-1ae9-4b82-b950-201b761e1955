"""
Base strategy class for all trading strategies.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
import pandas as pd
import logging

logger = logging.getLogger(__name__)

class BaseStrategy(ABC):
    """Abstract base class for trading strategies."""
    
    def __init__(self, name: str, config: Dict):
        """
        Initialize strategy.
        
        Args:
            name: Strategy name
            config: Strategy configuration
        """
        self.name = name
        self.config = config
        self.positions = {}  # Track open positions
        self.signals = []    # Store trading signals
        
    @abstractmethod
    def generate_signal(self, data: pd.DataFrame, symbol: str) -> str:
        """
        Generate trading signal based on market data.
        
        Args:
            data: OHLCV data
            symbol: Trading symbol
            
        Returns:
            Signal: 'BUY', 'SELL', or 'HOLD'
        """
        pass
    
    @abstractmethod
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate technical indicators for the strategy.
        
        Args:
            data: OHLCV data
            
        Returns:
            DataFrame with indicators added
        """
        pass
    
    def should_enter_position(self, signal: str, symbol: str) -> bool:
        """
        Determine if we should enter a new position.
        
        Args:
            signal: Trading signal
            symbol: Trading symbol
            
        Returns:
            True if should enter position
        """
        # Don't enter if we already have a position
        if symbol in self.positions:
            return False
            
        return signal in ['BUY', 'SELL']
    
    def should_exit_position(self, signal: str, symbol: str) -> bool:
        """
        Determine if we should exit current position.
        
        Args:
            signal: Trading signal
            symbol: Trading symbol
            
        Returns:
            True if should exit position
        """
        if symbol not in self.positions:
            return False
            
        current_position = self.positions[symbol]['side']
        
        # Exit long position on sell signal
        if current_position == 'BUY' and signal == 'SELL':
            return True
            
        # Exit short position on buy signal
        if current_position == 'SELL' and signal == 'BUY':
            return True
            
        return False
    
    def add_position(self, symbol: str, side: str, quantity: float, price: float):
        """Add a new position."""
        self.positions[symbol] = {
            'side': side,
            'quantity': quantity,
            'entry_price': price,
            'timestamp': pd.Timestamp.now()
        }
        logger.info(f"Position added: {side} {quantity} {symbol} at {price}")
    
    def remove_position(self, symbol: str):
        """Remove a position."""
        if symbol in self.positions:
            del self.positions[symbol]
            logger.info(f"Position removed for {symbol}")
    
    def get_position(self, symbol: str) -> Optional[Dict]:
        """Get current position for symbol."""
        return self.positions.get(symbol)
    
    def log_signal(self, symbol: str, signal: str, price: float, indicators: Dict = None):
        """Log trading signal."""
        signal_data = {
            'timestamp': pd.Timestamp.now(),
            'symbol': symbol,
            'signal': signal,
            'price': price,
            'indicators': indicators or {}
        }
        self.signals.append(signal_data)
        logger.info(f"Signal: {signal} for {symbol} at {price}")
    
    def get_signal_history(self) -> List[Dict]:
        """Get signal history."""
        return self.signals
    
    def reset(self):
        """Reset strategy state."""
        self.positions = {}
        self.signals = []
        logger.info(f"Strategy {self.name} reset")
