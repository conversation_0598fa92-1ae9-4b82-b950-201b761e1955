"""
Network Connectivity Test for Binance API
"""

import socket
import requests
import time
import sys
from urllib3.exceptions import NameResolutionError
import subprocess
import platform

def test_basic_internet():
    """Test basic internet connectivity."""
    print("🌐 Testing Basic Internet Connectivity")
    print("=" * 50)
    
    test_sites = [
        "*******",  # Google DNS
        "*******",  # Cloudflare DNS
        "google.com",
        "github.com"
    ]
    
    for site in test_sites:
        try:
            if site.replace('.', '').isdigit():  # IP address
                socket.create_connection((site, 53), timeout=5)
                print(f"✅ {site} - Reachable")
            else:  # Domain name
                socket.gethostbyname(site)
                print(f"✅ {site} - DNS resolves")
        except Exception as e:
            print(f"❌ {site} - Failed: {e}")

def test_dns_resolution():
    """Test DNS resolution for Binance domains."""
    print("\n🔍 Testing DNS Resolution for Binance")
    print("=" * 50)
    
    binance_domains = [
        "api.binance.com",
        "testnet.binance.vision",
        "fapi.binance.com",
        "testnet.binancefuture.com"
    ]
    
    for domain in binance_domains:
        try:
            ip = socket.gethostbyname(domain)
            print(f"✅ {domain} → {ip}")
        except Exception as e:
            print(f"❌ {domain} - DNS failed: {e}")

def test_binance_api_connectivity():
    """Test direct connectivity to Binance API."""
    print("\n🔗 Testing Binance API Connectivity")
    print("=" * 50)
    
    test_endpoints = [
        {
            'name': 'Binance Mainnet Time',
            'url': 'https://api.binance.com/api/v3/time',
            'timeout': 10
        },
        {
            'name': 'Binance Testnet Time',
            'url': 'https://testnet.binance.vision/api/v3/time',
            'timeout': 10
        },
        {
            'name': 'Binance Futures Testnet',
            'url': 'https://testnet.binancefuture.com/fapi/v1/time',
            'timeout': 10
        }
    ]
    
    for endpoint in test_endpoints:
        try:
            print(f"Testing {endpoint['name']}...")
            response = requests.get(endpoint['url'], timeout=endpoint['timeout'])
            
            if response.status_code == 200:
                data = response.json()
                server_time = data.get('serverTime', 'Unknown')
                print(f"✅ {endpoint['name']} - OK (Server time: {server_time})")
            else:
                print(f"❌ {endpoint['name']} - HTTP {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ {endpoint['name']} - Timeout (>10s)")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ {endpoint['name']} - Connection failed: {e}")
        except Exception as e:
            print(f"❌ {endpoint['name']} - Error: {e}")

def test_firewall_and_proxy():
    """Test for firewall or proxy issues."""
    print("\n🛡️ Testing Firewall and Proxy Settings")
    print("=" * 50)
    
    # Check if behind corporate firewall/proxy
    try:
        # Test HTTPS on port 443
        sock = socket.create_connection(("api.binance.com", 443), timeout=5)
        sock.close()
        print("✅ Port 443 (HTTPS) - Open")
    except Exception as e:
        print(f"❌ Port 443 (HTTPS) - Blocked: {e}")
    
    # Check proxy settings
    import os
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    proxy_found = False
    
    for var in proxy_vars:
        if os.environ.get(var):
            print(f"🔍 Proxy detected: {var}={os.environ[var]}")
            proxy_found = True
    
    if not proxy_found:
        print("✅ No proxy environment variables detected")

def ping_test():
    """Test ping to Binance servers."""
    print("\n🏓 Ping Test to Binance Servers")
    print("=" * 50)
    
    hosts = [
        "api.binance.com",
        "testnet.binance.vision"
    ]
    
    for host in hosts:
        try:
            # Determine ping command based on OS
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "4", host]
            else:
                cmd = ["ping", "-c", "4", host]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                print(f"✅ {host} - Ping successful")
                # Extract average time if possible
                output = result.stdout
                if "Average" in output or "avg" in output:
                    lines = output.split('\n')
                    for line in lines:
                        if "Average" in line or "avg" in line:
                            print(f"   {line.strip()}")
            else:
                print(f"❌ {host} - Ping failed")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {host} - Ping timeout")
        except Exception as e:
            print(f"❌ {host} - Ping error: {e}")

def suggest_fixes():
    """Suggest potential fixes for connectivity issues."""
    print("\n🔧 Suggested Fixes for Connectivity Issues")
    print("=" * 60)
    
    print("1. 🌐 NETWORK ISSUES:")
    print("   • Check your internet connection")
    print("   • Try switching to mobile hotspot temporarily")
    print("   • Restart your router/modem")
    print("   • Flush DNS cache: ipconfig /flushdns (Windows)")
    print()
    
    print("2. 🛡️ FIREWALL/ANTIVIRUS:")
    print("   • Temporarily disable Windows Firewall")
    print("   • Add Python.exe to firewall exceptions")
    print("   • Disable antivirus real-time protection temporarily")
    print("   • Check corporate firewall settings")
    print()
    
    print("3. 🔍 DNS ISSUES:")
    print("   • Change DNS servers to ******* and ******* (Google)")
    print("   • Or use ******* and ******* (Cloudflare)")
    print("   • Run: nslookup api.binance.com")
    print()
    
    print("4. 🌍 VPN/PROXY:")
    print("   • Try connecting through a VPN")
    print("   • Check if you're behind a corporate proxy")
    print("   • Some regions may block Binance - use VPN")
    print()
    
    print("5. ⏰ TIME SYNC:")
    print("   • Sync system time: w32tm /resync (Windows)")
    print("   • Enable automatic time sync in Windows settings")
    print()
    
    print("6. 🐍 PYTHON/REQUESTS:")
    print("   • Update requests library: pip install --upgrade requests")
    print("   • Update urllib3: pip install --upgrade urllib3")
    print("   • Try different Python version")

def create_simple_test():
    """Create a simple connectivity test that can be run manually."""
    print("\n📝 Creating Simple Test Script")
    print("=" * 50)
    
    simple_test = '''
import requests
import socket

print("Simple Binance Connectivity Test")
print("=" * 40)

# Test 1: DNS Resolution
try:
    ip = socket.gethostbyname("api.binance.com")
    print(f"✅ DNS: api.binance.com → {ip}")
except Exception as e:
    print(f"❌ DNS failed: {e}")

# Test 2: HTTP Request
try:
    response = requests.get("https://api.binance.com/api/v3/time", timeout=15)
    if response.status_code == 200:
        print("✅ HTTP: Binance API reachable")
        data = response.json()
        print(f"   Server time: {data.get('serverTime')}")
    else:
        print(f"❌ HTTP: Status {response.status_code}")
except Exception as e:
    print(f"❌ HTTP failed: {e}")

print("\\nIf both tests fail, you have network connectivity issues.")
print("If DNS works but HTTP fails, you may have firewall/proxy issues.")
'''
    
    with open('simple_connectivity_test.py', 'w') as f:
        f.write(simple_test)
    
    print("✅ Created simple_connectivity_test.py")
    print("   Run this with: python simple_connectivity_test.py")

def main():
    """Run comprehensive network connectivity tests."""
    print("🧪 Binance API Network Connectivity Test Suite")
    print("=" * 70)
    
    print("🎯 This will help diagnose network connectivity issues with Binance API")
    print()
    
    # Run all tests
    test_basic_internet()
    test_dns_resolution()
    test_binance_api_connectivity()
    test_firewall_and_proxy()
    ping_test()
    
    # Provide suggestions
    suggest_fixes()
    
    # Create simple test
    create_simple_test()
    
    print("\n" + "=" * 70)
    print("🎯 NEXT STEPS:")
    print("1. Review the test results above")
    print("2. Try the suggested fixes for any failed tests")
    print("3. Run simple_connectivity_test.py to verify fixes")
    print("4. If issues persist, try using a VPN")
    print("5. Contact your network administrator if on corporate network")

if __name__ == "__main__":
    main()
