"""
Test script to verify live trading setup is ready.
Run this before starting live trading to ensure everything is configured correctly.
"""

import os
import sys
import logging
from dotenv import load_dotenv
import pandas as pd

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from utils import load_config, setup_logging

def test_environment_variables():
    """Test if required environment variables are set."""
    print("🔧 Testing Environment Variables...")
    
    load_dotenv()
    
    api_key = os.getenv('BINANCE_API_KEY')
    secret_key = os.getenv('BINANCE_SECRET_KEY')
    initial_balance = os.getenv('INITIAL_BALANCE')
    
    if not api_key:
        print("❌ BINANCE_API_KEY not found in environment variables")
        return False
    else:
        print(f"✅ BINANCE_API_KEY found (length: {len(api_key)})")
    
    if not secret_key:
        print("❌ BINANCE_SECRET_KEY not found in environment variables")
        return False
    else:
        print(f"✅ BINANCE_SECRET_KEY found (length: {len(secret_key)})")
    
    if not initial_balance:
        print("⚠️  INITIAL_BALANCE not set, using default")
    else:
        print(f"✅ INITIAL_BALANCE set to {initial_balance}")
    
    return True

def test_api_connection():
    """Test Binance API connection."""
    print("\n🌐 Testing API Connection...")

    # Check if we're in testnet mode
    config = load_config('config/config.yaml')
    trading_mode = config.get('trading', {}).get('mode', 'unknown')
    testnet = trading_mode == 'testnet' or trading_mode == 'paper'

    if testnet:
        print("🧪 Testing TESTNET connection...")
    else:
        print("🔴 Testing LIVE connection...")

    try:
        client = BinanceClient(testnet=testnet)
        
        # Test basic connection
        account = client.get_account_info()
        if not account:
            print("❌ Failed to get account info")
            return False
        
        print("✅ Basic API connection successful")
        
        # Test futures account
        futures_account = client.futures_get_account_info()
        if not futures_account:
            print("❌ Failed to get futures account info")
            return False
        
        print("✅ Futures API connection successful")
        
        # Display account info
        total_balance = futures_account.get('totalWalletBalance', 'N/A')
        available_balance = futures_account.get('availableBalance', 'N/A')
        
        print(f"📊 Futures Account Info:")
        print(f"   Total Balance: {total_balance} USDT")
        print(f"   Available Balance: {available_balance} USDT")
        
        return True
        
    except Exception as e:
        print(f"❌ API connection failed: {e}")
        return False

def test_market_data():
    """Test market data retrieval."""
    print("\n📈 Testing Market Data...")

    try:
        # Check if we're in testnet mode
        config = load_config('config/config.yaml')
        trading_mode = config.get('trading', {}).get('mode', 'unknown')
        testnet = trading_mode == 'testnet' or trading_mode == 'paper'

        client = BinanceClient(testnet=testnet)

        # Use a common symbol that's available on both testnet and live
        test_symbol = 'BTCUSDT'
        print(f"   Testing with {test_symbol}...")

        # Test getting historical data
        data = client.get_historical_klines(test_symbol, '5m', 10)

        if data.empty:
            print("❌ Failed to get market data")

            # Try an alternative symbol
            print("   Trying alternative symbol ETHUSDT...")
            data = client.get_historical_klines('ETHUSDT', '5m', 10)

            if data.empty:
                print("❌ Failed to get market data for alternative symbol")
                return False

        print(f"✅ Market data retrieved: {len(data)} candles")
        print(f"   Latest price: {data['close'].iloc[-1]:.6f}")

        return True

    except Exception as e:
        print(f"❌ Market data test failed: {e}")
        print(f"   Error details: {str(e)}")
        return False

def test_leverage_setting():
    """Test leverage setting capability."""
    print("\n⚡ Testing Leverage Setting...")

    try:
        # Check if we're in testnet mode
        config = load_config('config/config.yaml')
        trading_mode = config.get('trading', {}).get('mode', 'unknown')
        testnet = trading_mode == 'testnet' or trading_mode == 'paper'

        client = BinanceClient(testnet=testnet)

        # Use a common symbol for testing
        test_symbol = 'BTCUSDT'

        # Test setting leverage (use a safe value)
        result = client.futures_change_leverage(test_symbol, 2)

        if not result:
            print("❌ Failed to set leverage")
            print("   This might be normal if you don't have testnet funds")
            return True  # Don't fail the test for this

        print("✅ Leverage setting successful")
        print(f"   Set leverage to 2x for {test_symbol}")

        return True

    except Exception as e:
        print(f"❌ Leverage setting test failed: {e}")
        print("   This might be normal if you don't have testnet funds or positions")
        return True  # Don't fail the test for this

def test_position_info():
    """Test position information retrieval."""
    print("\n📋 Testing Position Information...")

    try:
        # Check if we're in testnet mode
        config = load_config('config/config.yaml')
        trading_mode = config.get('trading', {}).get('mode', 'unknown')
        testnet = trading_mode == 'testnet' or trading_mode == 'paper'

        client = BinanceClient(testnet=testnet)

        # Get position info
        positions = client.futures_get_position_info()

        if positions is None:
            print("❌ Failed to get position info")
            return False

        print(f"✅ Position info retrieved: {len(positions)} symbols")

        # Check for any open positions
        open_positions = [pos for pos in positions if float(pos.get('positionAmt', 0)) != 0]

        if open_positions:
            print(f"⚠️  Found {len(open_positions)} open positions:")
            for pos in open_positions:
                symbol = pos.get('symbol', 'Unknown')
                size = pos.get('positionAmt', '0')
                pnl = pos.get('unRealizedProfit', '0')
                print(f"   {symbol}: {size} (PnL: {pnl})")
        else:
            print("✅ No open positions found")

        return True

    except Exception as e:
        print(f"❌ Position info test failed: {e}")
        print(f"   Error details: {str(e)}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\n⚙️  Testing Configuration...")

    try:
        config = load_config('config/config.yaml')

        if not config:
            print("❌ Failed to load configuration")
            return False

        print("✅ Configuration loaded successfully")

        # Check trading mode
        trading_mode = config.get('trading', {}).get('mode', 'unknown')
        print(f"   Trading Mode: {trading_mode}")

        if trading_mode == 'testnet':
            print("🧪 Trading mode is set to 'testnet' - Using Binance Futures Testnet")
            print("   Testnet URL: https://testnet.binancefuture.com")
        elif trading_mode == 'live':
            print("🔴 Trading mode is set to 'live' - Using real Binance Futures")
        else:
            print(f"⚠️  Trading mode is set to '{trading_mode}' - Not testnet or live")
        
        # Check symbols
        symbols = config.get('trading', {}).get('symbols', [])
        print(f"   Symbols to trade: {len(symbols)}")
        
        # Check risk management
        risk_config = config.get('risk_management', {})
        max_position = risk_config.get('max_position_size', 0)
        max_leverage = risk_config.get('max_leverage', 0)
        
        print(f"   Max Position Size: {max_position*100}%")
        print(f"   Max Leverage: {max_leverage}x")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Trading Bot Setup Test")
    print("=" * 50)

    # Check trading mode first
    config = load_config('config/config.yaml')
    trading_mode = config.get('trading', {}).get('mode', 'unknown') if config else 'unknown'

    if trading_mode == 'testnet':
        print("🧪 Testing TESTNET configuration")
        print("Using Binance Futures Testnet (https://testnet.binancefuture.com)")
    elif trading_mode == 'live':
        print("🔴 Testing LIVE configuration")
        print("Using real Binance Futures")
    else:
        print(f"⚠️  Unknown trading mode: {trading_mode}")

    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("API Connection", test_api_connection),
        ("Market Data", test_market_data),
        ("Leverage Setting", test_leverage_setting),
        ("Position Information", test_position_info),
        ("Configuration", test_configuration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"Overall: {passed}/{total} tests passed")
    
    if passed == total:
        if trading_mode == 'testnet':
            print("🎉 All tests passed! Ready for testnet trading.")
            print("\n🧪 TESTNET REMINDERS:")
            print("   - This is a demo environment with no real money at risk")
            print("   - Test your strategies thoroughly before going live")
            print("   - Check the TESTNET_GUIDE.md file for more information")
            print("   - Visit https://testnet.binancefuture.com to monitor positions")
        else:
            print("🎉 All tests passed! Ready for live trading.")
            print("\n⚠️  LIVE TRADING REMINDERS:")
            print("   - Start with small position sizes")
            print("   - Monitor the bot closely initially")
            print("   - Have a plan to stop trading if needed")
            print("   - Never risk more than you can afford to lose")
    else:
        if trading_mode == 'testnet':
            print("⚠️  Some tests failed. Please fix issues before testnet trading.")
        else:
            print("⚠️  Some tests failed. Please fix issues before live trading.")
        print("\n🔧 Common fixes:")
        print("   - Check .env file for API keys")
        print("   - Verify API permissions on Binance")
        print("   - Ensure Futures trading is enabled")
        print("   - Check internet connection")

if __name__ == "__main__":
    main()
