"""
Adaptive Configuration Manager for Trading Strategies
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MarketCondition:
    """Market condition assessment."""
    volatility_level: str  # 'low', 'medium', 'high'
    trend_strength: str    # 'weak', 'moderate', 'strong'
    volume_activity: str   # 'low', 'normal', 'high'
    market_phase: str      # 'accumulation', 'trending', 'distribution', 'decline'

class AdaptiveConfigManager:
    """
    Manages adaptive configuration that adjusts trading parameters
    based on current market conditions.
    """
    
    def __init__(self, base_config: Dict):
        self.base_config = base_config.copy()
        self.current_config = base_config.copy()
        
        # Market condition tracking
        self.market_history = []
        self.performance_history = []
        
        # Adaptive parameters
        self.adaptation_enabled = base_config.get('adaptive_enabled', True)
        self.adaptation_sensitivity = base_config.get('adaptation_sensitivity', 0.5)
        self.min_history_length = base_config.get('min_history_length', 20)
        
        # Configuration ranges
        self.config_ranges = {
            'min_confidence_threshold': {'min': 60, 'max': 85, 'default': 70},
            'min_risk_reward_ratio': {'min': 1.2, 'max': 2.5, 'default': 1.5},
            'breakout_threshold': {'min': 0.003, 'max': 0.008, 'default': 0.005},
            'volume_surge_threshold': {'min': 1.2, 'max': 2.5, 'default': 1.5},
            'support_resistance_periods': {'min': 15, 'max': 30, 'default': 20}
        }
        
        logger.info(f"Adaptive Config Manager initialized (enabled: {self.adaptation_enabled})")
    
    def assess_market_conditions(self, market_data: Dict[str, pd.DataFrame]) -> MarketCondition:
        """Assess current market conditions across multiple symbols."""
        try:
            if not market_data:
                return MarketCondition('medium', 'moderate', 'normal', 'trending')
            
            volatilities = []
            trend_strengths = []
            volume_ratios = []
            
            for symbol, data in market_data.items():
                if data is None or data.empty or len(data) < 20:
                    continue
                
                # Calculate volatility (price standard deviation)
                price_volatility = data['close'].tail(20).std() / data['close'].tail(20).mean()
                volatilities.append(price_volatility)
                
                # Calculate trend strength (price change over period)
                trend_strength = abs(data['close'].iloc[-1] - data['close'].iloc[-20]) / data['close'].iloc[-20]
                trend_strengths.append(trend_strength)
                
                # Calculate volume activity
                if 'volume' in data.columns:
                    recent_volume = data['volume'].tail(5).mean()
                    avg_volume = data['volume'].tail(20).mean()
                    volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0
                    volume_ratios.append(volume_ratio)
            
            # Aggregate market conditions
            avg_volatility = np.mean(volatilities) if volatilities else 0.02
            avg_trend_strength = np.mean(trend_strengths) if trend_strengths else 0.03
            avg_volume_ratio = np.mean(volume_ratios) if volume_ratios else 1.0
            
            # Classify conditions
            volatility_level = self._classify_volatility(avg_volatility)
            trend_strength_level = self._classify_trend_strength(avg_trend_strength)
            volume_activity_level = self._classify_volume_activity(avg_volume_ratio)
            market_phase = self._determine_market_phase(avg_volatility, avg_trend_strength, avg_volume_ratio)
            
            condition = MarketCondition(
                volatility_level=volatility_level,
                trend_strength=trend_strength_level,
                volume_activity=volume_activity_level,
                market_phase=market_phase
            )
            
            # Store in history
            self.market_history.append({
                'timestamp': datetime.now(),
                'condition': condition,
                'volatility': avg_volatility,
                'trend_strength': avg_trend_strength,
                'volume_ratio': avg_volume_ratio
            })
            
            # Limit history
            if len(self.market_history) > 100:
                self.market_history = self.market_history[-100:]
            
            return condition
            
        except Exception as e:
            logger.error(f"Error assessing market conditions: {e}")
            return MarketCondition('medium', 'moderate', 'normal', 'trending')
    
    def _classify_volatility(self, volatility: float) -> str:
        """Classify volatility level."""
        if volatility < 0.015:
            return 'low'
        elif volatility > 0.035:
            return 'high'
        else:
            return 'medium'
    
    def _classify_trend_strength(self, trend_strength: float) -> str:
        """Classify trend strength."""
        if trend_strength < 0.02:
            return 'weak'
        elif trend_strength > 0.05:
            return 'strong'
        else:
            return 'moderate'
    
    def _classify_volume_activity(self, volume_ratio: float) -> str:
        """Classify volume activity level."""
        if volume_ratio < 0.8:
            return 'low'
        elif volume_ratio > 1.5:
            return 'high'
        else:
            return 'normal'
    
    def _determine_market_phase(self, volatility: float, trend_strength: float, volume_ratio: float) -> str:
        """Determine current market phase."""
        if volatility < 0.02 and trend_strength < 0.03 and volume_ratio < 1.2:
            return 'accumulation'
        elif trend_strength > 0.04 and volume_ratio > 1.3:
            return 'trending'
        elif volatility > 0.04 and volume_ratio > 1.5:
            return 'distribution'
        else:
            return 'decline'
    
    def adapt_configuration(self, market_condition: MarketCondition, 
                          recent_performance: Optional[Dict] = None) -> Dict:
        """Adapt configuration based on market conditions and performance."""
        try:
            if not self.adaptation_enabled:
                return self.current_config
            
            # Start with base configuration
            adapted_config = self.base_config.copy()
            
            # Adapt based on market conditions
            adapted_config = self._adapt_for_volatility(adapted_config, market_condition.volatility_level)
            adapted_config = self._adapt_for_trend_strength(adapted_config, market_condition.trend_strength)
            adapted_config = self._adapt_for_volume_activity(adapted_config, market_condition.volume_activity)
            adapted_config = self._adapt_for_market_phase(adapted_config, market_condition.market_phase)
            
            # Adapt based on recent performance
            if recent_performance:
                adapted_config = self._adapt_for_performance(adapted_config, recent_performance)
            
            # Ensure values are within valid ranges
            adapted_config = self._validate_config_ranges(adapted_config)
            
            # Update current configuration
            self.current_config = adapted_config
            
            # Log significant changes
            self._log_config_changes(adapted_config)
            
            return adapted_config
            
        except Exception as e:
            logger.error(f"Error adapting configuration: {e}")
            return self.current_config
    
    def _adapt_for_volatility(self, config: Dict, volatility_level: str) -> Dict:
        """Adapt configuration for volatility conditions."""
        if volatility_level == 'high':
            # High volatility: Be more selective
            config['min_confidence_threshold'] = min(config['min_confidence_threshold'] + 5, 85)
            config['min_risk_reward_ratio'] = min(config['min_risk_reward_ratio'] + 0.2, 2.5)
            config['breakout_threshold'] = min(config['breakout_threshold'] + 0.001, 0.008)
        elif volatility_level == 'low':
            # Low volatility: Be more aggressive
            config['min_confidence_threshold'] = max(config['min_confidence_threshold'] - 3, 60)
            config['min_risk_reward_ratio'] = max(config['min_risk_reward_ratio'] - 0.1, 1.2)
            config['volume_surge_threshold'] = max(config['volume_surge_threshold'] - 0.1, 1.2)
        
        return config
    
    def _adapt_for_trend_strength(self, config: Dict, trend_strength: str) -> Dict:
        """Adapt configuration for trend strength."""
        if trend_strength == 'strong':
            # Strong trends: Lower thresholds for trend following
            config['min_confidence_threshold'] = max(config['min_confidence_threshold'] - 5, 60)
            config['breakout_threshold'] = max(config['breakout_threshold'] - 0.001, 0.003)
        elif trend_strength == 'weak':
            # Weak trends: Higher thresholds, more selective
            config['min_confidence_threshold'] = min(config['min_confidence_threshold'] + 3, 85)
            config['min_risk_reward_ratio'] = min(config['min_risk_reward_ratio'] + 0.1, 2.5)
        
        return config
    
    def _adapt_for_volume_activity(self, config: Dict, volume_activity: str) -> Dict:
        """Adapt configuration for volume activity."""
        if volume_activity == 'high':
            # High volume: Trust breakouts more
            config['volume_surge_threshold'] = max(config['volume_surge_threshold'] - 0.2, 1.2)
            config['breakout_threshold'] = max(config['breakout_threshold'] - 0.0005, 0.003)
        elif volume_activity == 'low':
            # Low volume: Be more cautious
            config['volume_surge_threshold'] = min(config['volume_surge_threshold'] + 0.2, 2.5)
            config['min_confidence_threshold'] = min(config['min_confidence_threshold'] + 3, 85)
        
        return config
    
    def _adapt_for_market_phase(self, config: Dict, market_phase: str) -> Dict:
        """Adapt configuration for market phase."""
        if market_phase == 'trending':
            # Trending market: Follow trends
            config['min_confidence_threshold'] = max(config['min_confidence_threshold'] - 3, 60)
            config['support_resistance_periods'] = max(config['support_resistance_periods'] - 2, 15)
        elif market_phase == 'accumulation':
            # Accumulation: Look for breakouts
            config['breakout_threshold'] = max(config['breakout_threshold'] - 0.001, 0.003)
            config['volume_surge_threshold'] = max(config['volume_surge_threshold'] - 0.1, 1.2)
        elif market_phase == 'distribution':
            # Distribution: Be very selective
            config['min_confidence_threshold'] = min(config['min_confidence_threshold'] + 8, 85)
            config['min_risk_reward_ratio'] = min(config['min_risk_reward_ratio'] + 0.3, 2.5)
        
        return config
    
    def _adapt_for_performance(self, config: Dict, performance: Dict) -> Dict:
        """Adapt configuration based on recent performance."""
        win_rate = performance.get('win_rate', 50)
        avg_return = performance.get('avg_return', 0)
        
        if win_rate < 40:  # Poor performance
            # Be more selective
            config['min_confidence_threshold'] = min(config['min_confidence_threshold'] + 5, 85)
            config['min_risk_reward_ratio'] = min(config['min_risk_reward_ratio'] + 0.2, 2.5)
        elif win_rate > 70:  # Good performance
            # Be slightly more aggressive
            config['min_confidence_threshold'] = max(config['min_confidence_threshold'] - 2, 60)
        
        return config
    
    def _validate_config_ranges(self, config: Dict) -> Dict:
        """Ensure configuration values are within valid ranges."""
        for param, value in config.items():
            if param in self.config_ranges:
                range_info = self.config_ranges[param]
                config[param] = max(range_info['min'], min(range_info['max'], value))
        
        return config
    
    def _log_config_changes(self, new_config: Dict):
        """Log significant configuration changes."""
        significant_changes = []
        
        for param in ['min_confidence_threshold', 'min_risk_reward_ratio', 'breakout_threshold']:
            if param in self.current_config and param in new_config:
                old_val = self.current_config[param]
                new_val = new_config[param]
                
                # Check for significant change (>5% for most parameters)
                threshold = 0.05 if param != 'min_confidence_threshold' else 2
                
                if abs(new_val - old_val) > threshold:
                    significant_changes.append(f"{param}: {old_val:.3f} → {new_val:.3f}")
        
        if significant_changes:
            logger.info(f"📊 Adaptive config changes: {', '.join(significant_changes)}")
    
    def get_current_config(self) -> Dict:
        """Get current adapted configuration."""
        return self.current_config.copy()
    
    def reset_to_base_config(self):
        """Reset configuration to base values."""
        self.current_config = self.base_config.copy()
        logger.info("Configuration reset to base values")
    
    def get_adaptation_summary(self) -> Dict:
        """Get summary of current adaptations."""
        if not self.market_history:
            return {'status': 'No market data available'}
        
        latest_condition = self.market_history[-1]['condition']
        
        return {
            'adaptation_enabled': self.adaptation_enabled,
            'market_condition': {
                'volatility': latest_condition.volatility_level,
                'trend_strength': latest_condition.trend_strength,
                'volume_activity': latest_condition.volume_activity,
                'market_phase': latest_condition.market_phase
            },
            'current_thresholds': {
                'confidence': self.current_config.get('min_confidence_threshold', 70),
                'risk_reward': self.current_config.get('min_risk_reward_ratio', 1.5),
                'breakout': self.current_config.get('breakout_threshold', 0.005),
                'volume_surge': self.current_config.get('volume_surge_threshold', 1.5)
            },
            'adaptations_from_base': {
                param: {
                    'base': self.base_config.get(param, 0),
                    'current': self.current_config.get(param, 0),
                    'change': self.current_config.get(param, 0) - self.base_config.get(param, 0)
                }
                for param in ['min_confidence_threshold', 'min_risk_reward_ratio', 
                             'breakout_threshold', 'volume_surge_threshold']
            }
        }
