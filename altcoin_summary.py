"""
Quick summary of available altcoins for trading.
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from altcoin_scanner import AltcoinScanner
from binance_client import BinanceClient

def main():
    """Show quick altcoin summary."""
    print("🚀 Altcoin Trading Bot - Available Coins Summary")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    try:
        # Initialize client and scanner
        client = BinanceClient()
        scanner_config = {
            'min_volume_24h': 500000,  # $500K minimum
            'min_price': 0.0001,
            'max_price': 10000,
            'quote_asset': 'USDT'
        }
        scanner = AltcoinScanner(client, scanner_config)
        
        # Quick scan
        print("Scanning Binance Futures for altcoins...")
        altcoins = scanner.scan_altcoins(force_rescan=True)
        
        if altcoins:
            print(f"✅ Found {len(altcoins)} qualifying altcoins")
            print(f"📊 Top 20: {', '.join(altcoins[:20])}")
            
            # Show summary
            summary = scanner.get_altcoin_summary()
            print(f"💰 Total 24h Volume: ${summary['total_volume_24h']:,.0f}")
            print(f"📈 Avg Price Change: {summary['avg_price_change_24h']:.2f}%")
            
            print("\n🔧 Configuration:")
            print("- Dynamic scanning: ENABLED")
            print(f"- Max symbols: 100 (configurable)")
            print(f"- Excluded: {', '.join(list(summary['excluded_assets'])[:5])}...")
            
            print("\n🎯 The bot will automatically trade the most liquid altcoins!")
        else:
            print("❌ No altcoins found")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure your API keys are configured in .env file")

if __name__ == "__main__":
    main()
