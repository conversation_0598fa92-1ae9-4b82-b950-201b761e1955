#!/usr/bin/env python3
"""
Quick Testnet Connection Test for Trading Bot
Tests specifically the testnet endpoints that the trading bot uses.
"""

import requests
import sys
import time

def test_testnet_endpoints():
    """Test all testnet endpoints the trading bot uses."""
    print("🔍 Testing Binance Testnet Endpoints (Trading Bot Configuration)")
    print("=" * 60)
    
    # Testnet endpoints that the trading bot actually uses
    endpoints = [
        {
            "name": "Testnet Futures Ping",
            "url": "https://testnet.binancefuture.com/fapi/v1/ping",
            "description": "Main futures trading endpoint"
        },
        {
            "name": "Testnet Futures Time", 
            "url": "https://testnet.binancefuture.com/fapi/v1/time",
            "description": "Time sync for futures trading"
        },
        {
            "name": "Testnet Spot Ping",
            "url": "https://testnet.binance.vision/api/v3/ping", 
            "description": "Backup spot endpoint"
        },
        {
            "name": "Testnet Spot Time",
            "url": "https://testnet.binance.vision/api/v3/time",
            "description": "Backup time sync"
        }
    ]
    
    success_count = 0
    total_count = len(endpoints)
    
    for endpoint in endpoints:
        print(f"\n🔗 Testing: {endpoint['name']}")
        print(f"   URL: {endpoint['url']}")
        print(f"   Purpose: {endpoint['description']}")
        
        try:
            response = requests.get(endpoint['url'], timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS - HTTP {response.status_code}")
                
                # Try to parse response for time endpoints
                if 'time' in endpoint['url']:
                    try:
                        data = response.json()
                        server_time = data.get('serverTime', 'N/A')
                        print(f"   📅 Server Time: {server_time}")
                    except:
                        pass
                        
                success_count += 1
            else:
                print(f"   ⚠️  HTTP {response.status_code} - Unexpected response")
                
        except requests.exceptions.ConnectionError as e:
            if "Connection refused" in str(e) or "Errno 111" in str(e):
                print(f"   ❌ CONNECTION REFUSED - Common on mobile networks")
            else:
                print(f"   ❌ CONNECTION ERROR - {e}")
        except requests.exceptions.Timeout:
            print(f"   ❌ TIMEOUT - Network too slow")
        except Exception as e:
            print(f"   ❌ ERROR - {e}")
    
    print("\n" + "=" * 60)
    print("📊 TESTNET CONNECTION RESULTS")
    print("=" * 60)
    
    if success_count == total_count:
        print("✅ ALL TESTNET ENDPOINTS WORKING")
        print("🚀 Your trading bot should work perfectly!")
        return True
    elif success_count > 0:
        print(f"⚠️  PARTIAL SUCCESS: {success_count}/{total_count} endpoints working")
        print("🔄 Trading bot might work but could have issues")
        return False
    else:
        print("❌ ALL TESTNET ENDPOINTS FAILED")
        print("🚫 Trading bot will NOT work with current network")
        return False

def test_mainnet_backup():
    """Test mainnet as backup to see if it's a testnet-specific issue."""
    print("\n🔍 Testing Mainnet (Backup Test)")
    print("=" * 40)
    
    try:
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
        if response.status_code == 200:
            print("✅ Mainnet works - Issue is testnet-specific")
            print("💡 Your carrier might block testnet but allow mainnet")
            return True
        else:
            print(f"⚠️  Mainnet HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Mainnet also fails: {e}")
        print("🌐 This is a general network connectivity issue")
        return False

def provide_solutions(testnet_works, mainnet_works):
    """Provide specific solutions based on test results."""
    print("\n🔧 SOLUTIONS FOR YOUR SITUATION")
    print("=" * 40)
    
    if testnet_works:
        print("✅ Perfect! Your network supports testnet trading.")
        print("🚀 Run: python main.py")
        
    elif mainnet_works and not testnet_works:
        print("⚠️  Testnet blocked, but mainnet works")
        print("💡 SOLUTIONS:")
        print("   1. Use VPN to access testnet")
        print("   2. Switch to mainnet (edit .env: TESTNET_MODE=false)")
        print("   3. Try different network (WiFi ↔ Mobile Data)")
        print("   4. Contact carrier about testnet access")
        
    else:
        print("❌ Both testnet and mainnet fail")
        print("🔧 NETWORK FIXES NEEDED:")
        print("   1. Switch networks (WiFi ↔ Mobile Data)")
        print("   2. Restart Termux app completely")
        print("   3. Use VPN (pkg install openvpn)")
        print("   4. Check carrier crypto API restrictions")
        print("   5. Try different DNS (8.8.8.8, 1.1.1.1)")
        print("   6. Update packages: pkg update && pkg upgrade")

def main():
    """Main test function."""
    print("🤖 TRADING BOT TESTNET CONNECTION TEST")
    print("Testing the exact endpoints your trading bot uses")
    print("=" * 60)
    
    # Test testnet endpoints
    testnet_success = test_testnet_endpoints()
    
    # Test mainnet as backup
    mainnet_success = test_mainnet_backup()
    
    # Provide specific solutions
    provide_solutions(testnet_success, mainnet_success)
    
    print("\n" + "=" * 60)
    if testnet_success:
        print("🎉 READY FOR TRADING!")
        print("Your network can access Binance testnet.")
        print("Run: python main.py")
    else:
        print("⚠️  NETWORK ISSUES DETECTED")
        print("Follow the solutions above before running the trading bot.")
    
    print("=" * 60)
    
    return testnet_success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
