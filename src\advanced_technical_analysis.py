"""
Advanced technical analysis with multiple indicators and chart patterns.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class AdvancedTechnicalAnalysis:
    """Advanced technical analysis with multiple indicators and pattern recognition."""
    
    def __init__(self):
        """Initialize technical analysis engine."""
        self.indicators = {}
        
    def calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate all technical indicators for the given data."""
        df = data.copy()
        
        # Price-based indicators
        df = self.add_moving_averages(df)
        df = self.add_bollinger_bands(df)
        df = self.add_rsi(df)
        df = self.add_macd(df)
        df = self.add_stochastic(df)
        df = self.add_williams_r(df)
        df = self.add_cci(df)
        
        # Volume indicators
        df = self.add_volume_indicators(df)
        
        # Volatility indicators
        df = self.add_atr(df)
        
        # Support/Resistance levels
        df = self.add_support_resistance(df)
        
        # Chart patterns
        df = self.add_chart_patterns(df)
        
        return df
    
    def add_moving_averages(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add various moving averages."""
        close = df['close']
        
        # Simple Moving Averages
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'SMA_{period}'] = close.rolling(period).mean()
        
        # Exponential Moving Averages
        for period in [5, 10, 20, 50]:
            df[f'EMA_{period}'] = close.ewm(span=period).mean()
        
        # Weighted Moving Average
        df['WMA_20'] = close.rolling(20).apply(lambda x: np.average(x, weights=range(1, len(x)+1)))
        
        return df
    
    def add_bollinger_bands(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add Bollinger Bands."""
        close = df['close']
        
        # Standard Bollinger Bands (20, 2)
        bb_middle = close.rolling(20).mean()
        bb_std = close.rolling(20).std()
        df['BB_Upper'] = bb_middle + (bb_std * 2)
        df['BB_Middle'] = bb_middle
        df['BB_Lower'] = bb_middle - (bb_std * 2)
        df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']
        df['BB_Position'] = (close - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
        
        return df
    
    def add_rsi(self, df: pd.DataFrame, periods: List[int] = [14, 21]) -> pd.DataFrame:
        """Add RSI indicators."""
        close = df['close']
        
        for period in periods:
            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            df[f'RSI_{period}'] = 100 - (100 / (1 + rs))
        
        return df
    
    def add_macd(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add MACD indicators."""
        close = df['close']
        
        # Standard MACD (12, 26, 9)
        ema_12 = close.ewm(span=12).mean()
        ema_26 = close.ewm(span=26).mean()
        df['MACD'] = ema_12 - ema_26
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
        
        return df
    
    def add_stochastic(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add Stochastic Oscillator."""
        high = df['high']
        low = df['low']
        close = df['close']
        
        # %K and %D
        lowest_low = low.rolling(14).min()
        highest_high = high.rolling(14).max()
        df['Stoch_K'] = 100 * (close - lowest_low) / (highest_high - lowest_low)
        df['Stoch_D'] = df['Stoch_K'].rolling(3).mean()
        
        return df
    
    def add_williams_r(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add Williams %R."""
        high = df['high']
        low = df['low']
        close = df['close']
        
        highest_high = high.rolling(14).max()
        lowest_low = low.rolling(14).min()
        df['Williams_R'] = -100 * (highest_high - close) / (highest_high - lowest_low)
        
        return df
    
    def add_cci(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add Commodity Channel Index."""
        high = df['high']
        low = df['low']
        close = df['close']
        
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(20).mean()
        mad = typical_price.rolling(20).apply(lambda x: np.mean(np.abs(x - x.mean())))
        df['CCI'] = (typical_price - sma_tp) / (0.015 * mad)
        
        return df
    
    def add_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based indicators."""
        close = df['close']
        volume = df['volume']
        high = df['high']
        low = df['low']
        
        # Volume Moving Average
        df['Volume_SMA'] = volume.rolling(20).mean()
        df['Volume_Ratio'] = volume / df['Volume_SMA']
        
        # On-Balance Volume
        df['OBV'] = (volume * np.where(close > close.shift(1), 1, 
                                      np.where(close < close.shift(1), -1, 0))).cumsum()
        
        # Volume Price Trend
        df['VPT'] = (volume * (close - close.shift(1)) / close.shift(1)).cumsum()
        
        # Money Flow Index
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(14).sum()
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(14).sum()
        mfi_ratio = positive_flow / negative_flow
        df['MFI'] = 100 - (100 / (1 + mfi_ratio))
        
        return df
    
    def add_atr(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add Average True Range."""
        high = df['high']
        low = df['low']
        close = df['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        df['ATR'] = true_range.rolling(14).mean()
        
        return df
    
    def add_support_resistance(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add support and resistance levels."""
        high = df['high']
        low = df['low']
        close = df['close']
        
        # Pivot Points
        df['Pivot'] = (high.shift(1) + low.shift(1) + close.shift(1)) / 3
        df['R1'] = 2 * df['Pivot'] - low.shift(1)
        df['S1'] = 2 * df['Pivot'] - high.shift(1)
        df['R2'] = df['Pivot'] + (high.shift(1) - low.shift(1))
        df['S2'] = df['Pivot'] - (high.shift(1) - low.shift(1))
        
        # Dynamic Support/Resistance (using rolling min/max)
        df['Resistance_20'] = high.rolling(20).max()
        df['Support_20'] = low.rolling(20).min()
        
        return df
    
    def add_chart_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect chart patterns."""
        close = df['close']
        high = df['high']
        low = df['low']
        
        # Doji pattern
        body = abs(df['open'] - close)
        upper_shadow = high - np.maximum(df['open'], close)
        lower_shadow = np.minimum(df['open'], close) - low
        df['Doji'] = (body <= (upper_shadow + lower_shadow) * 0.1)
        
        # Hammer pattern
        df['Hammer'] = ((lower_shadow >= 2 * body) & 
                       (upper_shadow <= 0.1 * body) & 
                       (close > df['open']))
        
        # Shooting Star pattern
        df['Shooting_Star'] = ((upper_shadow >= 2 * body) & 
                              (lower_shadow <= 0.1 * body) & 
                              (close < df['open']))
        
        # Engulfing patterns
        df['Bullish_Engulfing'] = ((close > df['open']) & 
                                  (close.shift(1) < df['open'].shift(1)) &
                                  (close > df['open'].shift(1)) &
                                  (df['open'] < close.shift(1)))
        
        df['Bearish_Engulfing'] = ((close < df['open']) & 
                                  (close.shift(1) > df['open'].shift(1)) &
                                  (close < df['open'].shift(1)) &
                                  (df['open'] > close.shift(1)))
        
        return df
    
    def generate_signals(self, df: pd.DataFrame) -> Dict[str, any]:
        """Generate comprehensive trading signals."""
        if len(df) < 50:
            return {'signal': 'HOLD', 'confidence': 0, 'reasons': []}
        
        latest = df.iloc[-1]
        prev = df.iloc[-2]
        
        buy_signals = []
        sell_signals = []
        confidence_factors = []
        
        # RSI Signals
        if latest['RSI_14'] < 30:
            buy_signals.append('RSI Oversold')
            confidence_factors.append(0.8)
        elif latest['RSI_14'] > 70:
            sell_signals.append('RSI Overbought')
            confidence_factors.append(0.8)
        
        # MACD Signals
        if (latest['MACD'] > latest['MACD_Signal'] and 
            prev['MACD'] <= prev['MACD_Signal']):
            buy_signals.append('MACD Bullish Crossover')
            confidence_factors.append(0.7)
        elif (latest['MACD'] < latest['MACD_Signal'] and 
              prev['MACD'] >= prev['MACD_Signal']):
            sell_signals.append('MACD Bearish Crossover')
            confidence_factors.append(0.7)
        
        # Moving Average Signals
        if (latest['close'] > latest['EMA_20'] and 
            latest['EMA_20'] > latest['EMA_50']):
            buy_signals.append('Price Above EMAs')
            confidence_factors.append(0.6)
        elif (latest['close'] < latest['EMA_20'] and 
              latest['EMA_20'] < latest['EMA_50']):
            sell_signals.append('Price Below EMAs')
            confidence_factors.append(0.6)
        
        # Bollinger Bands Signals
        if latest['close'] < latest['BB_Lower']:
            buy_signals.append('Below Bollinger Lower Band')
            confidence_factors.append(0.5)
        elif latest['close'] > latest['BB_Upper']:
            sell_signals.append('Above Bollinger Upper Band')
            confidence_factors.append(0.5)
        
        # Volume Signals
        if latest['Volume_Ratio'] > 2.0:
            confidence_factors.append(0.3)  # Volume confirmation
        
        # Stochastic Signals
        if latest['Stoch_K'] < 20 and latest['Stoch_D'] < 20:
            buy_signals.append('Stochastic Oversold')
            confidence_factors.append(0.4)
        elif latest['Stoch_K'] > 80 and latest['Stoch_D'] > 80:
            sell_signals.append('Stochastic Overbought')
            confidence_factors.append(0.4)
        
        # Chart Pattern Signals
        if latest['Bullish_Engulfing'] or latest['Hammer']:
            buy_signals.append('Bullish Pattern')
            confidence_factors.append(0.6)
        elif latest['Bearish_Engulfing'] or latest['Shooting_Star']:
            sell_signals.append('Bearish Pattern')
            confidence_factors.append(0.6)
        
        # Determine overall signal
        buy_strength = len(buy_signals)
        sell_strength = len(sell_signals)
        
        if buy_strength > sell_strength and buy_strength >= 2:
            signal = 'BUY'
            reasons = buy_signals
        elif sell_strength > buy_strength and sell_strength >= 2:
            signal = 'SELL'
            reasons = sell_signals
        else:
            signal = 'HOLD'
            reasons = ['Insufficient signals']
        
        # Calculate confidence (0-100)
        if confidence_factors:
            confidence = min(100, sum(confidence_factors) * 100 / len(confidence_factors))
        else:
            confidence = 0
        
        return {
            'signal': signal,
            'confidence': confidence,
            'reasons': reasons,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'indicators': {
                'rsi': latest['RSI_14'],
                'macd': latest['MACD'],
                'macd_signal': latest['MACD_Signal'],
                'bb_position': latest['BB_Position'],
                'volume_ratio': latest['Volume_Ratio'],
                'stoch_k': latest['Stoch_K'],
                'atr': latest['ATR']
            }
        }
