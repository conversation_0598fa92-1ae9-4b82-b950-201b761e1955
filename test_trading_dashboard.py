"""
Test and Demo for Real-Time Trading Dashboard
"""

import sys
import os
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_mock_trading_data():
    """Create mock trading data for dashboard testing."""
    symbols = ['SOLUSDT', 'ADAUSDT', 'XRPUSDT', 'SUIUSDT', 'LINKUSDT', 'BCHUSDT']
    
    mock_data = {}
    
    for symbol in symbols:
        # Generate realistic price data
        base_price = np.random.uniform(0.1, 100)
        price_trend = np.random.uniform(-0.02, 0.02)  # Daily trend
        volatility = np.random.uniform(0.01, 0.05)    # Volatility
        
        # Bollinger Band metrics
        bb_position = np.random.uniform(10, 90)  # Position in BB
        bb_width = np.random.uniform(0.002, 0.05)  # Band width
        
        # Mean reversion metrics
        mean_distance = np.random.uniform(-2, 2)  # % from mean
        
        # Volume metrics
        volume_ratio = np.random.uniform(0.5, 3.0)  # Volume ratio
        
        # Signal generation
        signals = ['BUY', 'SELL', 'HOLD']
        signal_weights = [0.2, 0.2, 0.6]  # Mostly HOLD
        signal = np.random.choice(signals, p=signal_weights)
        
        # Error simulation
        has_error = np.random.random() < 0.1  # 10% chance of error
        error_msg = "Test error: Connection timeout" if has_error else None
        
        mock_data[symbol] = {
            'price': base_price,
            'bb_upper': base_price * (1 + bb_width/2),
            'bb_lower': base_price * (1 - bb_width/2),
            'bb_middle': base_price,
            'bb_position': bb_position,
            'bb_width': bb_width,
            'mean_distance': mean_distance,
            'breakout_position': bb_position,
            'signal': signal,
            'volume_ratio': volume_ratio,
            'error': error_msg
        }
    
    return mock_data

def test_dashboard_creation():
    """Test dashboard creation and basic functionality."""
    print("📊 Testing Dashboard Creation")
    print("=" * 50)
    
    try:
        from trading_dashboard import TradingDashboard
        
        # Initialize dashboard
        config = {
            'update_interval': 5,
            'max_history': 50,
            'grid_cols': 3,
            'grid_rows': 3
        }
        
        dashboard = TradingDashboard(config)
        
        print(f"✅ Dashboard created successfully")
        print(f"   Update interval: {config['update_interval']} seconds")
        print(f"   Max history: {config['max_history']} points")
        print(f"   Grid layout: {config['grid_rows']}x{config['grid_cols']}")
        
        return dashboard
        
    except Exception as e:
        print(f"❌ Error creating dashboard: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_data_updates(dashboard):
    """Test dashboard data updates."""
    print("\n📈 Testing Data Updates")
    print("=" * 50)
    
    try:
        if dashboard is None:
            print("❌ No dashboard to test")
            return False
        
        # Generate mock data
        mock_data = create_mock_trading_data()
        
        print(f"📊 Updating dashboard with {len(mock_data)} symbols...")
        
        # Update dashboard with mock data
        for symbol, data in mock_data.items():
            dashboard.update_symbol_data(symbol, data)
            print(f"   ✅ Updated {symbol}: {data['signal']} signal, BB: {data['bb_position']:.1f}%")
        
        # Test error logging
        dashboard.log_error('TESTUSDT', 'Connection Error', 'Mock connection timeout')
        dashboard.log_error('TESTUSDT', 'Analysis Error', 'Mock analysis failure')
        
        # Test trade logging
        dashboard.log_trade('SOLUSDT', 'BUY', 1.234, 100.0, 'Chart breakout')
        dashboard.log_trade('ADAUSDT', 'SELL', 0.567, 200.0, 'Resistance rejection')
        
        print(f"✅ Data updates completed successfully")
        print(f"   Active symbols: {len(dashboard.active_symbols)}")
        print(f"   Error log entries: {len(dashboard.error_log)}")
        print(f"   Trade history entries: {len(dashboard.trade_history)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing data updates: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_filters(dashboard):
    """Test dashboard filtering functionality."""
    print("\n🔍 Testing Dashboard Filters")
    print("=" * 50)
    
    try:
        if dashboard is None:
            print("❌ No dashboard to test")
            return False
        
        # Test different filter settings
        filter_tests = [
            {
                'name': 'High Volatility Only',
                'filters': {'min_volatility': 0.03, 'max_volatility': 1.0}
            },
            {
                'name': 'Low Volatility Only', 
                'filters': {'min_volatility': 0.0, 'max_volatility': 0.02}
            },
            {
                'name': 'High Volume Only',
                'filters': {'min_volume_ratio': 2.0}
            },
            {
                'name': 'Buy Signals Only',
                'filters': {'signal_types': ['BUY']}
            },
            {
                'name': 'Errors Only',
                'filters': {'show_errors_only': True}
            }
        ]
        
        original_symbols = dashboard.active_symbols.copy()
        
        for test in filter_tests:
            print(f"\n📊 Testing filter: {test['name']}")
            
            # Apply filters
            for filter_name, value in test['filters'].items():
                dashboard.set_filter(filter_name, value)
            
            # Test filtering
            filtered_symbols = dashboard.apply_filters(original_symbols)
            
            print(f"   Original symbols: {len(original_symbols)}")
            print(f"   Filtered symbols: {len(filtered_symbols)}")
            print(f"   Filtered list: {filtered_symbols[:3]}{'...' if len(filtered_symbols) > 3 else ''}")
            
            # Reset filters
            dashboard.filters = {
                'min_volatility': 0.002,
                'max_volatility': 0.05,
                'min_volume_ratio': 1.0,
                'show_errors_only': False,
                'signal_types': ['BUY', 'SELL', 'HOLD']
            }
        
        print(f"\n✅ Filter testing completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error testing filters: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_visualization(dashboard):
    """Test dashboard visualization."""
    print("\n🎨 Testing Dashboard Visualization")
    print("=" * 50)
    
    try:
        if dashboard is None:
            print("❌ No dashboard to test")
            return False
        
        print("📊 Creating dashboard layout...")
        dashboard.create_dashboard()
        
        print("📈 Updating dashboard display...")
        dashboard.update_dashboard()
        
        print("💾 Testing dashboard save...")
        dashboard.save_dashboard("test_dashboard.png")
        
        print("📤 Testing data export...")
        dashboard.export_data("test_dashboard_data.json")
        
        print("✅ Visualization testing completed successfully")
        print("💡 Dashboard files saved:")
        print("   📊 test_dashboard.png - Dashboard screenshot")
        print("   📄 test_dashboard_data.json - Dashboard data export")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing visualization: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_live_dashboard(dashboard):
    """Demo live dashboard updates."""
    print("\n🚀 Live Dashboard Demo")
    print("=" * 50)
    
    try:
        if dashboard is None:
            print("❌ No dashboard to demo")
            return False
        
        print("🎬 Starting live dashboard demo...")
        print("📊 Dashboard will update with simulated live data")
        print("⏱️  Demo will run for 30 seconds with updates every 2 seconds")
        print("💡 Close the dashboard window to stop the demo")
        
        # Show dashboard
        dashboard.show_dashboard()
        
        # Simulate live updates
        symbols = list(dashboard.active_symbols)
        
        for update_count in range(15):  # 15 updates over 30 seconds
            print(f"📈 Update {update_count + 1}/15...")
            
            # Update each symbol with new data
            for symbol in symbols:
                # Simulate price movement
                if symbol in dashboard.symbol_data:
                    current_data = dashboard.symbol_data[symbol]
                    if current_data['prices']:
                        last_price = current_data['prices'][-1]
                        # Small random price movement
                        price_change = np.random.normal(0, last_price * 0.01)
                        new_price = max(last_price + price_change, 0.001)
                    else:
                        new_price = np.random.uniform(0.1, 100)
                else:
                    new_price = np.random.uniform(0.1, 100)
                
                # Generate new data
                bb_position = np.random.uniform(10, 90)
                bb_width = np.random.uniform(0.002, 0.05)
                signal = np.random.choice(['BUY', 'SELL', 'HOLD'], p=[0.15, 0.15, 0.7])
                
                new_data = {
                    'price': new_price,
                    'bb_upper': new_price * (1 + bb_width/2),
                    'bb_lower': new_price * (1 - bb_width/2),
                    'bb_middle': new_price,
                    'bb_position': bb_position,
                    'bb_width': bb_width,
                    'mean_distance': np.random.uniform(-2, 2),
                    'breakout_position': bb_position,
                    'signal': signal,
                    'volume_ratio': np.random.uniform(0.5, 3.0),
                    'error': None
                }
                
                dashboard.update_symbol_data(symbol, new_data)
            
            # Update dashboard display
            dashboard.update_dashboard()
            
            # Wait before next update
            time.sleep(2)
        
        print("✅ Live dashboard demo completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error in live demo: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_dashboard_features():
    """Show dashboard features and capabilities."""
    print("📊 Real-Time Trading Dashboard Features")
    print("=" * 70)
    
    print("🎨 VISUAL FEATURES:")
    print("   • Mini charts for each symbol with price history")
    print("   • Bollinger Bands visualization")
    print("   • Signal markers (BUY/SELL/HOLD)")
    print("   • Real-time price updates")
    print("   • Error indicators and alerts")
    print()
    
    print("📊 DATA TRACKING:")
    print("   • Price movements and trends")
    print("   • Bollinger Band position and width")
    print("   • Mean reversion distances")
    print("   • Volume ratios and confirmations")
    print("   • Trading signals and confidence")
    print()
    
    print("🔍 FILTERING & SEARCH:")
    print("   • Volatility range filters")
    print("   • Volume ratio thresholds")
    print("   • Signal type filtering")
    print("   • Error-only display mode")
    print("   • Custom filter combinations")
    print()
    
    print("🚨 ERROR MONITORING:")
    print("   • Real-time error alerts panel")
    print("   • Error categorization and timestamps")
    print("   • Symbol-specific error tracking")
    print("   • Error history and patterns")
    print()
    
    print("📈 TRADING INSIGHTS:")
    print("   • Active symbol count")
    print("   • Signal distribution")
    print("   • Trade history tracking")
    print("   • Success rate monitoring")
    print("   • Performance statistics")

def main():
    """Run dashboard test suite."""
    print("🧪 Real-Time Trading Dashboard Test Suite")
    print("=" * 80)
    
    show_dashboard_features()
    
    # Run tests
    dashboard = test_dashboard_creation()
    
    if dashboard:
        tests = [
            ("Data Updates", lambda: test_data_updates(dashboard)),
            ("Dashboard Filters", lambda: test_dashboard_filters(dashboard)),
            ("Dashboard Visualization", lambda: test_dashboard_visualization(dashboard))
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                print(f"❌ {test_name} test failed with exception: {e}")
        
        print("\n" + "=" * 80)
        print(f"📊 Test Results: {passed}/{total} passed")
        
        if passed == total:
            print("🎉 TRADING DASHBOARD READY!")
            print("\n✅ Features Verified:")
            print("   ✅ Dashboard creation and layout")
            print("   ✅ Real-time data updates")
            print("   ✅ Filtering and search")
            print("   ✅ Visualization and charts")
            print("   ✅ Error monitoring")
            print("   ✅ Data export capabilities")
            
            # Ask for live demo
            print("\n🚀 Would you like to see a live dashboard demo?")
            response = input("Enter 'y' for live demo or any other key to skip: ").lower().strip()
            
            if response == 'y':
                demo_live_dashboard(dashboard)
            else:
                print("📊 Dashboard testing completed. Run main.py to see it in action!")
        else:
            print("⚠️  Some tests failed. Check the errors above.")
    else:
        print("❌ Dashboard creation failed. Cannot run further tests.")

if __name__ == "__main__":
    main()
