"""
Setup script for the trading bot.
"""

import os
import sys
from dotenv import load_dotenv

def create_directories():
    """Create necessary directories."""
    directories = ['logs', 'data', 'backtest_results']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")

def setup_environment():
    """Setup environment file if it doesn't exist."""
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy('.env.example', '.env')
            print("Created .env file from .env.example")
            print("Please edit .env file with your Binance API credentials")
        else:
            print("Warning: .env.example not found")

def check_dependencies():
    """Check if all dependencies are installed."""
    try:
        import pandas
        import numpy
        import yaml
        import binance
        print("✓ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def validate_config():
    """Validate configuration file."""
    try:
        import yaml
        with open('config/config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        required_sections = ['trading', 'strategies', 'risk_management']
        for section in required_sections:
            if section not in config:
                print(f"✗ Missing section in config: {section}")
                return False
        
        print("✓ Configuration file is valid")
        return True
    except Exception as e:
        print(f"✗ Error validating config: {e}")
        return False

def main():
    """Main setup function."""
    print("Setting up Crypto Trading Bot...")
    print("=" * 40)
    
    # Create directories
    create_directories()
    
    # Setup environment
    setup_environment()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Validate config
    if not validate_config():
        sys.exit(1)
    
    print("\n" + "=" * 40)
    print("Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file with your Binance API credentials")
    print("2. Review and customize config/config.yaml")
    print("3. Run the bot: python main.py")
    print("\nFor paper trading, set TRADING_MODE=paper in .env")
    print("For live trading, set TRADING_MODE=live in .env")

if __name__ == "__main__":
    main()
