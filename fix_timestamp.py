"""
Fix timestamp issues for Binance API connection.
"""

import time
import requests
from datetime import datetime

def check_time_sync():
    """Check if system time is synchronized with Binance servers."""
    print("🕐 Checking Time Synchronization")
    print("=" * 40)
    
    try:
        # Get local time
        local_time = int(time.time() * 1000)
        local_datetime = datetime.fromtimestamp(local_time / 1000)
        
        print(f"Local time: {local_datetime}")
        
        # Get Binance server time
        response = requests.get('https://api.binance.com/api/v3/time', timeout=10)
        if response.status_code == 200:
            server_time = response.json()['serverTime']
            server_datetime = datetime.fromtimestamp(server_time / 1000)
            
            print(f"Binance server time: {server_datetime}")
            
            # Calculate difference
            time_diff = abs(server_time - local_time)
            print(f"Time difference: {time_diff} ms")
            
            if time_diff > 1000:
                print("❌ Time difference is too large (>1 second)")
                print("This will cause timestamp errors with Binance API")
                print("\n🔧 Solutions:")
                print("1. Sync your system clock with internet time")
                print("2. On Windows: Settings > Time & Language > Date & Time > Sync now")
                print("3. On Linux: sudo ntpdate -s time.nist.gov")
                print("4. On Mac: System Preferences > Date & Time > Set date and time automatically")
                return False
            else:
                print("✅ Time synchronization is good")
                return True
        else:
            print("❌ Failed to get Binance server time")
            return False
            
    except Exception as e:
        print(f"❌ Error checking time sync: {e}")
        return False

def test_internet_connection():
    """Test internet connection to Binance."""
    print("\n🌐 Testing Internet Connection")
    print("=" * 40)
    
    test_urls = [
        ('Binance API', 'https://api.binance.com/api/v3/ping'),
        ('Binance Testnet', 'https://testnet.binance.vision/api/v3/ping'),
        ('Google DNS', 'https://8.8.8.8'),
    ]
    
    for name, url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"✅ {name}: Connected")
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
        except requests.exceptions.Timeout:
            print(f"❌ {name}: Timeout")
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: Connection Error")
        except Exception as e:
            print(f"❌ {name}: {e}")

def main():
    """Main function."""
    print("🔧 Binance API Timestamp Fix Tool")
    print("=" * 50)
    print("This tool helps diagnose and fix timestamp issues")
    print("that prevent connection to Binance API.")
    print("=" * 50)
    
    # Check time sync
    time_ok = check_time_sync()
    
    # Test internet connection
    test_internet_connection()
    
    print("\n📋 Summary")
    print("=" * 20)
    
    if time_ok:
        print("✅ Time synchronization is working correctly")
        print("✅ You should be able to connect to Binance API")
        print("\nIf you're still having issues, try:")
        print("1. Check your firewall settings")
        print("2. Try a different internet connection")
        print("3. Contact your ISP if Binance is blocked")
    else:
        print("❌ Time synchronization needs to be fixed")
        print("❌ Fix your system time before using the trading bot")
        print("\n🔧 How to fix:")
        print("1. Enable automatic time synchronization")
        print("2. Restart your computer")
        print("3. Run this script again to verify")

if __name__ == "__main__":
    main()
