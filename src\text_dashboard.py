"""
Simple Text-Based Trading Dashboard (No matplotlib required)
"""

import pandas as pd
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import os

logger = logging.getLogger(__name__)

class TextDashboard:
    """
    Simple text-based trading dashboard for console display.
    No external dependencies required.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Dashboard settings
        self.max_history = config.get('max_history', 50)
        self.display_symbols = config.get('display_symbols', 10)
        
        # Data storage
        self.symbol_data = {}
        self.error_log = []
        self.trade_history = []
        self.scan_stats = {}
        
        # Display settings
        self.width = 80
        self.last_update = None
        
        logger.info("Text Dashboard initialized (console mode)")
    
    def update_symbol_data(self, symbol: str, data: Dict):
        """Update data for a specific symbol."""
        try:
            timestamp = datetime.now()
            
            if symbol not in self.symbol_data:
                self.symbol_data[symbol] = {
                    'timestamps': [],
                    'prices': [],
                    'signals': [],
                    'confidences': [],
                    'bb_positions': [],
                    'bb_widths': [],
                    'volume_ratios': [],
                    'errors': []
                }
            
            # Add new data point
            symbol_data = self.symbol_data[symbol]
            symbol_data['timestamps'].append(timestamp)
            symbol_data['prices'].append(data.get('price', 0))
            symbol_data['signals'].append(data.get('signal', 'HOLD'))
            symbol_data['confidences'].append(data.get('confidence', 0))
            symbol_data['bb_positions'].append(data.get('bb_position', 50))
            symbol_data['bb_widths'].append(data.get('bb_width', 0))
            symbol_data['volume_ratios'].append(data.get('volume_ratio', 1.0))
            symbol_data['errors'].append(data.get('error', None))
            
            # Limit history
            for key in symbol_data:
                if len(symbol_data[key]) > self.max_history:
                    symbol_data[key] = symbol_data[key][-self.max_history:]
            
            self.last_update = timestamp
            
        except Exception as e:
            logger.error(f"Error updating text dashboard data for {symbol}: {e}")
    
    def log_error(self, symbol: str, error_type: str, error_message: str):
        """Log an error for dashboard display."""
        error_entry = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'type': error_type,
            'message': error_message
        }
        self.error_log.append(error_entry)
        
        # Limit error log
        if len(self.error_log) > 20:
            self.error_log = self.error_log[-20:]
    
    def log_trade(self, symbol: str, side: str, price: float, quantity: float, reason: str):
        """Log a trade for dashboard display."""
        trade_entry = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'side': side,
            'price': price,
            'quantity': quantity,
            'reason': reason
        }
        self.trade_history.append(trade_entry)
        
        # Limit trade history
        if len(self.trade_history) > 10:
            self.trade_history = self.trade_history[-10:]
    
    def update_scan_stats(self, stats: Dict):
        """Update scanning statistics."""
        self.scan_stats = stats.copy()
        self.scan_stats['timestamp'] = datetime.now()
    
    def create_header(self) -> str:
        """Create dashboard header."""
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        title = "🚀 REAL-TIME TRADING DASHBOARD"
        
        header = "=" * self.width + "\n"
        header += f"{title:^{self.width}}\n"
        header += f"{'Last Update: ' + now:^{self.width}}\n"
        header += "=" * self.width + "\n"
        
        return header
    
    def create_summary_section(self) -> str:
        """Create summary statistics section."""
        section = "📊 TRADING SUMMARY\n"
        section += "-" * 40 + "\n"
        
        # Basic stats
        total_symbols = len(self.symbol_data)
        active_signals = sum(1 for symbol, data in self.symbol_data.items() 
                           if data['signals'] and data['signals'][-1] != 'HOLD')
        recent_errors = len([e for e in self.error_log 
                           if e['timestamp'] > datetime.now() - timedelta(minutes=30)])
        total_trades = len(self.trade_history)
        
        section += f"Active Symbols: {total_symbols:>15}\n"
        section += f"With Signals: {active_signals:>17}\n"
        section += f"Total Trades: {total_trades:>17}\n"
        section += f"Recent Errors: {recent_errors:>16}\n"
        
        # Scan statistics
        if self.scan_stats:
            section += f"\nLast Scan Results:\n"
            section += f"  Successfully Scanned: {self.scan_stats.get('successful_scans', 0)}\n"
            section += f"  Failed Scans: {self.scan_stats.get('failed_scans', 0)}\n"
            section += f"  Opportunities Found: {self.scan_stats.get('opportunities_found', 0)}\n"
            section += f"  Success Rate: {self.scan_stats.get('success_rate', 0):.1f}%\n"
        
        section += "\n"
        return section
    
    def create_symbols_section(self) -> str:
        """Create symbols overview section."""
        section = "📈 SYMBOL OVERVIEW (Top Signals)\n"
        section += "-" * 60 + "\n"
        
        if not self.symbol_data:
            section += "No symbol data available\n\n"
            return section
        
        # Sort symbols by signal priority and confidence
        symbol_priorities = []
        for symbol, data in self.symbol_data.items():
            if not data['signals'] or not data['prices']:
                continue
                
            latest_signal = data['signals'][-1]
            latest_confidence = data['confidences'][-1] if data['confidences'] else 0
            latest_price = data['prices'][-1]
            latest_bb_pos = data['bb_positions'][-1] if data['bb_positions'] else 50
            latest_bb_width = data['bb_widths'][-1] if data['bb_widths'] else 0
            has_error = data['errors'][-1] is not None if data['errors'] else False
            
            # Priority: BUY/SELL signals first, then by confidence
            priority = 0
            if latest_signal == 'BUY':
                priority = 1000 + latest_confidence
            elif latest_signal == 'SELL':
                priority = 900 + latest_confidence
            else:
                priority = latest_confidence
            
            symbol_priorities.append({
                'symbol': symbol,
                'priority': priority,
                'signal': latest_signal,
                'confidence': latest_confidence,
                'price': latest_price,
                'bb_position': latest_bb_pos,
                'bb_width': latest_bb_width,
                'has_error': has_error
            })
        
        # Sort by priority (highest first)
        symbol_priorities.sort(key=lambda x: x['priority'], reverse=True)
        
        # Display top symbols
        section += f"{'Symbol':<12} {'Signal':<6} {'Conf%':<6} {'Price':<12} {'BB Pos':<7} {'BB Width':<8} {'Status':<8}\n"
        section += "-" * 60 + "\n"
        
        display_count = min(self.display_symbols, len(symbol_priorities))
        for i in range(display_count):
            item = symbol_priorities[i]
            
            # Format signal with color indicators
            signal_indicator = {
                'BUY': '🟢',
                'SELL': '🔴', 
                'HOLD': '⚪'
            }.get(item['signal'], '⚪')
            
            status = '⚠️ ERR' if item['has_error'] else '✅ OK'
            
            section += f"{item['symbol']:<12} "
            section += f"{signal_indicator}{item['signal']:<5} "
            section += f"{item['confidence']:<6.1f} "
            section += f"${item['price']:<11.6f} "
            section += f"{item['bb_position']:<7.1f} "
            section += f"{item['bb_width']:<8.4f} "
            section += f"{status:<8}\n"
        
        if len(symbol_priorities) > display_count:
            section += f"... and {len(symbol_priorities) - display_count} more symbols\n"
        
        section += "\n"
        return section
    
    def create_errors_section(self) -> str:
        """Create recent errors section."""
        section = "🚨 RECENT ERRORS\n"
        section += "-" * 50 + "\n"
        
        if not self.error_log:
            section += "No recent errors ✅\n\n"
            return section
        
        # Show last 5 errors
        recent_errors = self.error_log[-5:]
        
        for error in reversed(recent_errors):
            time_str = error['timestamp'].strftime('%H:%M:%S')
            section += f"{time_str} | {error['symbol']:<10} | {error['type']:<12} | {error['message'][:30]}...\n"
        
        if len(self.error_log) > 5:
            section += f"... and {len(self.error_log) - 5} more errors\n"
        
        section += "\n"
        return section
    
    def create_trades_section(self) -> str:
        """Create recent trades section."""
        section = "💰 RECENT TRADES\n"
        section += "-" * 50 + "\n"
        
        if not self.trade_history:
            section += "No trades executed yet\n\n"
            return section
        
        for trade in reversed(self.trade_history[-3:]):  # Last 3 trades
            time_str = trade['timestamp'].strftime('%H:%M:%S')
            side_indicator = '🟢' if trade['side'] == 'BUY' else '🔴'
            section += f"{time_str} | {side_indicator}{trade['side']} {trade['symbol']} @ ${trade['price']:.6f}\n"
            section += f"         Qty: {trade['quantity']:.6f} | Reason: {trade['reason'][:25]}...\n"
        
        section += "\n"
        return section
    
    def display_dashboard(self):
        """Display the complete text dashboard."""
        try:
            # Clear screen (works on most terminals)
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Build dashboard
            dashboard = ""
            dashboard += self.create_header()
            dashboard += self.create_summary_section()
            dashboard += self.create_symbols_section()
            dashboard += self.create_errors_section()
            dashboard += self.create_trades_section()
            
            # Add footer
            dashboard += "=" * self.width + "\n"
            dashboard += f"{'💡 Text Dashboard Mode - Install matplotlib for visual charts':^{self.width}}\n"
            dashboard += "=" * self.width + "\n"
            
            print(dashboard)
            
        except Exception as e:
            logger.error(f"Error displaying text dashboard: {e}")
            print(f"Dashboard display error: {e}")
    
    def save_dashboard(self, filename: str = None):
        """Save dashboard as text file."""
        try:
            if filename is None:
                filename = f"text_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            # Build dashboard content
            content = ""
            content += self.create_header()
            content += self.create_summary_section()
            content += self.create_symbols_section()
            content += self.create_errors_section()
            content += self.create_trades_section()
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"Text dashboard saved as {filename}")
            
        except Exception as e:
            logger.error(f"Error saving text dashboard: {e}")
    
    def update_dashboard(self):
        """Update and display the dashboard."""
        self.display_dashboard()
    
    def show_dashboard(self):
        """Show the dashboard (same as display for text mode)."""
        self.display_dashboard()
    
    def export_data(self, filename: str = None):
        """Export dashboard data to text file."""
        try:
            if filename is None:
                filename = f"dashboard_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            content = f"Trading Dashboard Data Export\n"
            content += f"Generated: {datetime.now().isoformat()}\n"
            content += f"=" * 50 + "\n\n"
            
            content += f"Active Symbols: {len(self.symbol_data)}\n"
            content += f"Error Log Entries: {len(self.error_log)}\n"
            content += f"Trade History Entries: {len(self.trade_history)}\n\n"
            
            # Symbol summary
            content += "Symbol Summary:\n"
            for symbol, data in self.symbol_data.items():
                if data['signals'] and data['prices']:
                    latest_signal = data['signals'][-1]
                    latest_price = data['prices'][-1]
                    content += f"  {symbol}: {latest_signal} @ ${latest_price:.6f}\n"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"Dashboard data exported to {filename}")
            
        except Exception as e:
            logger.error(f"Error exporting dashboard data: {e}")
