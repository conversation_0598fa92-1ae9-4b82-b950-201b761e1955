"""
Enhanced Risk management module for Binance futures trading bot.
Implements advanced risk controls following context.txt guidelines.
"""

import logging
from typing import Dict, Optional, Tuple, List
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class RiskManager:
    """Enhanced risk management system for futures trading operations."""

    def __init__(self, config: Dict):
        """
        Initialize enhanced risk manager.

        Args:
            config: Risk management configuration
        """
        self.config = config

        # Enhanced position sizing (2-5% as per context.txt)
        self.max_position_size = config.get('max_position_size', 0.05)  # 5% of portfolio
        self.min_position_size = config.get('min_position_size', 0.02)  # 2% of portfolio

        # Enhanced stop loss (-1.5% as per context.txt)
        self.stop_loss_percentage = config.get('stop_loss_percentage', 0.015)  # 1.5%
        self.take_profit_percentage = config.get('take_profit_percentage', 0.0001)  # Any profit

        # Futures-specific settings
        self.max_leverage = config.get('max_leverage', 5)  # Max 5x leverage
        self.leverage_adjustment = config.get('leverage_adjustment', True)

        # Enhanced daily limits
        self.max_daily_loss = config.get('max_daily_loss', 0.05)  # 5%
        self.max_daily_trades = config.get('max_daily_trades', 20)

        # Cooldown and timing controls
        self.trade_cooldown_minutes = config.get('trade_cooldown_minutes', 5)
        self.last_trade_time = None

        # Track daily P&L and performance
        self.daily_pnl = 0.0
        self.daily_trades = 0
        self.consecutive_losses = 0
        self.max_consecutive_losses = config.get('max_consecutive_losses', 3)
        self.last_reset_date = pd.Timestamp.now().date()

        # Market condition awareness
        self.market_pause_active = False
        self.market_pause_until = None
        self.market_pause_duration_minutes = config.get('market_pause_duration', 30)

        # Volatility-based adjustments
        self.volatility_threshold = config.get('volatility_threshold', 0.05)  # 5%
        self.high_volatility_reduction = config.get('high_volatility_reduction', 0.5)  # 50% size reduction

        # STRICT POSITION LIMITS
        self.max_position_value_usd = config.get('max_position_value_usd', 100)  # MAX $100 per trade
        self.max_concurrent_positions = config.get('max_concurrent_positions', 3)  # MAX 3 positions

        logger.info(f"Enhanced Risk Manager initialized: MAX ${self.max_position_value_usd} per trade, MAX {self.max_concurrent_positions} positions")
    
    def calculate_position_size(self, portfolio_value: float, price: float,
                              risk_per_trade: float = None,
                              market_volatility: float = None,
                              symbol: str = None) -> Tuple[float, int]:
        """
        Calculate appropriate position size with enhanced risk controls.

        Args:
            portfolio_value: Total portfolio value
            price: Asset price
            risk_per_trade: Risk percentage per trade (optional)
            market_volatility: Current market volatility (optional)
            symbol: Trading symbol for leverage calculation

        Returns:
            Tuple of (quantity, leverage)
        """
        # Default risk per trade if not specified
        if risk_per_trade is None:
            risk_per_trade = self.max_position_size

        # Adjust position size based on market volatility
        if market_volatility and market_volatility > self.volatility_threshold:
            logger.info(f"High volatility detected ({market_volatility:.2%}), reducing position size")
            risk_per_trade *= self.high_volatility_reduction

        # Adjust position size based on consecutive losses
        if self.consecutive_losses > 0:
            reduction_factor = max(0.5, 1 - (self.consecutive_losses * 0.1))
            risk_per_trade *= reduction_factor
            logger.info(f"Reducing position size due to {self.consecutive_losses} consecutive losses")

        # Calculate maximum position value (between min and max limits)
        position_size_pct = max(self.min_position_size, min(risk_per_trade, self.max_position_size))
        max_position_value = portfolio_value * position_size_pct

        # ENFORCE $100 MAXIMUM PER TRADE (HARD LIMIT)
        max_usd_limit = getattr(self, 'max_position_value_usd', 100)  # Default to $100
        if max_position_value > max_usd_limit:
            logger.info(f"Position value capped: ${max_position_value:.2f} → ${max_usd_limit:.2f} (MAX $100 RULE)")
            max_position_value = max_usd_limit

        # Calculate leverage (default to max allowed)
        leverage = self.max_leverage

        # Adjust leverage based on volatility
        if market_volatility:
            # Lower leverage in high volatility
            leverage_factor = max(1, min(self.max_leverage, int(self.max_leverage * (1 - market_volatility))))
            leverage = max(1, leverage_factor)

        # Calculate quantity with leverage
        quantity = (max_position_value * leverage) / price

        logger.info(f"Position size calculated: {quantity:.6f} (value: {max_position_value:.2f}, leverage: {leverage}x)")
        return quantity, leverage
    
    def calculate_stop_loss_price(self, entry_price: float, side: str) -> float:
        """
        Calculate stop loss price.
        
        Args:
            entry_price: Entry price
            side: 'BUY' or 'SELL'
            
        Returns:
            Stop loss price
        """
        if side == 'BUY':
            # For long positions, stop loss is below entry price
            stop_loss = entry_price * (1 - self.stop_loss_percentage)
        else:
            # For short positions, stop loss is above entry price
            stop_loss = entry_price * (1 + self.stop_loss_percentage)
        
        return stop_loss
    
    def calculate_take_profit_price(self, entry_price: float, side: str) -> float:
        """
        Calculate take profit price.
        
        Args:
            entry_price: Entry price
            side: 'BUY' or 'SELL'
            
        Returns:
            Take profit price
        """
        if side == 'BUY':
            # For long positions, take profit is above entry price
            take_profit = entry_price * (1 + self.take_profit_percentage)
        else:
            # For short positions, take profit is below entry price
            take_profit = entry_price * (1 - self.take_profit_percentage)
        
        return take_profit
    
    def should_stop_trading(self) -> bool:
        """
        Enhanced check if trading should be stopped due to risk limits.

        Returns:
            True if trading should be stopped
        """
        # Reset daily tracking if new day
        current_date = pd.Timestamp.now().date()
        if current_date != self.last_reset_date:
            self.reset_daily_tracking()

        # Check daily loss limit
        if self.daily_pnl < -self.max_daily_loss:
            logger.warning(f"Daily loss limit reached: {self.daily_pnl:.4f}")
            return True

        # DISABLED: Check daily trade limit - ALLOW IMMEDIATE TRADING
        # if self.daily_trades >= self.max_daily_trades:
        #     logger.warning(f"Daily trade limit reached: {self.daily_trades}")
        #     return True

        # DISABLED: Check consecutive losses - ALLOW IMMEDIATE TRADING
        # if self.consecutive_losses >= self.max_consecutive_losses:
        #     logger.warning(f"Maximum consecutive losses reached: {self.consecutive_losses}")
        #     return True

        # DISABLED: Check market pause - ALLOW IMMEDIATE TRADING
        # if self.market_pause_active:
        #     if self.market_pause_until and pd.Timestamp.now() < self.market_pause_until:
        #         return True
        #     else:
        #         self.market_pause_active = False
        #         self.market_pause_until = None
        #         logger.info("Market pause period ended, resuming trading")

        # DISABLED: Check trade cooldown - ALLOW IMMEDIATE TRADING
        # if self.last_trade_time:
        #     time_since_last_trade = pd.Timestamp.now() - self.last_trade_time
        #     if time_since_last_trade.total_seconds() < (self.trade_cooldown_minutes * 60):
        #         return True

        return False  # Allow immediate trading
    
    def check_stop_loss(self, position: Dict, current_price: float) -> bool:
        """
        Check if stop loss should be triggered.
        
        Args:
            position: Position information
            current_price: Current market price
            
        Returns:
            True if stop loss should be triggered
        """
        entry_price = position['entry_price']
        side = position['side']
        
        stop_loss_price = self.calculate_stop_loss_price(entry_price, side)
        
        if side == 'BUY':
            # Long position: stop loss if price falls below stop loss
            return current_price <= stop_loss_price
        else:
            # Short position: stop loss if price rises above stop loss
            return current_price >= stop_loss_price
    
    def check_take_profit(self, position: Dict, current_price: float) -> bool:
        """
        Check if take profit should be triggered.
        
        Args:
            position: Position information
            current_price: Current market price
            
        Returns:
            True if take profit should be triggered
        """
        entry_price = position['entry_price']
        side = position['side']
        
        take_profit_price = self.calculate_take_profit_price(entry_price, side)
        
        if side == 'BUY':
            # Long position: take profit if price rises above take profit
            return current_price >= take_profit_price
        else:
            # Short position: take profit if price falls below take profit
            return current_price <= take_profit_price
    
    def calculate_pnl(self, position: Dict, current_price: float) -> float:
        """
        Calculate unrealized P&L for a position.
        
        Args:
            position: Position information
            current_price: Current market price
            
        Returns:
            Unrealized P&L
        """
        entry_price = position['entry_price']
        quantity = position['quantity']
        side = position['side']
        
        if side == 'BUY':
            # Long position P&L
            pnl = (current_price - entry_price) * quantity
        else:
            # Short position P&L
            pnl = (entry_price - current_price) * quantity
        
        return pnl
    
    def update_daily_pnl(self, realized_pnl: float):
        """Update daily P&L tracking with enhanced loss tracking."""
        self.daily_pnl += realized_pnl
        self.daily_trades += 1
        self.last_trade_time = pd.Timestamp.now()

        # Track consecutive losses
        if realized_pnl < 0:
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0  # Reset on profitable trade

        logger.info(f"Daily P&L updated: {self.daily_pnl:.4f} (trades: {self.daily_trades}, consecutive losses: {self.consecutive_losses})")

    def reset_daily_tracking(self):
        """Reset daily tracking for new day."""
        self.daily_pnl = 0.0
        self.daily_trades = 0
        self.consecutive_losses = 0
        self.last_reset_date = pd.Timestamp.now().date()
        logger.info("Daily tracking reset")

    def trigger_market_pause(self, reason: str = "Market conditions"):
        """
        Trigger market pause due to dangerous conditions.

        Args:
            reason: Reason for market pause
        """
        # DISABLED - Don't pause trading for immediate trading
        # self.market_pause_active = True
        # self.market_pause_until = pd.Timestamp.now() + timedelta(minutes=self.market_pause_duration_minutes)
        logger.info(f"Market pause would normally be triggered: {reason} - CONTINUING ANYWAY")

    def check_immediate_profit_exit(self, position: Dict, current_price: float) -> bool:
        """
        Check if position should be closed immediately when profitable (context.txt rule).

        Args:
            position: Position information
            current_price: Current market price

        Returns:
            True if position should be closed due to profit
        """
        pnl = self.calculate_pnl(position, current_price)

        # Close position as soon as it becomes profitable (context.txt rule)
        if pnl > 0:
            logger.info(f"Position profitable (PnL: {pnl:.4f}), triggering immediate exit")
            return True

        return False
    
    def calculate_market_volatility(self, price_data: pd.DataFrame, window: int = 20) -> float:
        """
        Calculate market volatility for risk adjustment.

        Args:
            price_data: DataFrame with price data
            window: Rolling window for volatility calculation

        Returns:
            Volatility as a percentage
        """
        if len(price_data) < window:
            return 0.0

        returns = price_data['close'].pct_change().dropna()
        volatility = returns.rolling(window=window).std().iloc[-1]

        return volatility if not pd.isna(volatility) else 0.0

    def assess_market_conditions(self, price_data: pd.DataFrame, volume_data: pd.Series = None) -> Dict:
        """
        Assess overall market conditions for trading decisions.

        Args:
            price_data: DataFrame with OHLCV data
            volume_data: Volume data for analysis

        Returns:
            Dictionary with market condition assessment
        """
        if len(price_data) < 20:
            return {'healthy': False, 'reason': 'Insufficient data'}

        # Calculate volatility
        volatility = self.calculate_market_volatility(price_data)

        # Check for sideways market (low volatility with no clear trend)
        price_range = (price_data['high'].tail(10).max() - price_data['low'].tail(10).min()) / price_data['close'].iloc[-1]

        # Check volume (if available)
        low_volume = False
        if volume_data is not None and len(volume_data) >= 10:
            avg_volume = volume_data.tail(20).mean()
            recent_volume = volume_data.tail(5).mean()
            low_volume = recent_volume < (avg_volume * 0.7)  # 30% below average

        # Determine market health
        conditions = {
            'volatility': volatility,
            'price_range': price_range,
            'low_volume': low_volume,
            'sideways_market': price_range < 0.02,  # Less than 2% range
            'high_volatility': volatility > self.volatility_threshold
        }

        # TEMPORARILY DISABLED - ALLOW TRADING IN ALL CONDITIONS
        # Market is unhealthy if any danger signals
        unhealthy_conditions = [
            conditions['low_volume'],
            conditions['sideways_market'],
            conditions['high_volatility']
        ]

        # FORCE HEALTHY = TRUE for immediate trading
        healthy = True  # Always return healthy for immediate trading

        if not healthy:
            reasons = []
            if conditions['low_volume']:
                reasons.append("Low volume")
            if conditions['sideways_market']:
                reasons.append("Sideways market")
            if conditions['high_volatility']:
                reasons.append("High volatility")

            reason = ", ".join(reasons)
        else:
            reason = "Market conditions favorable (forced for immediate trading)"

        return {
            'healthy': healthy,
            'reason': reason,
            'conditions': conditions
        }

    def get_risk_metrics(self) -> Dict:
        """Get enhanced risk metrics."""
        return {
            'max_position_size': self.max_position_size,
            'min_position_size': self.min_position_size,
            'stop_loss_percentage': self.stop_loss_percentage,
            'take_profit_percentage': self.take_profit_percentage,
            'max_daily_loss': self.max_daily_loss,
            'max_leverage': self.max_leverage,
            'daily_pnl': self.daily_pnl,
            'daily_trades': self.daily_trades,
            'consecutive_losses': self.consecutive_losses,
            'market_pause_active': self.market_pause_active,
            'trading_stopped': self.should_stop_trading(),
            'last_trade_time': self.last_trade_time,
            'cooldown_remaining': self._get_cooldown_remaining()
        }

    def _get_cooldown_remaining(self) -> int:
        """Get remaining cooldown time in seconds."""
        if not self.last_trade_time:
            return 0

        time_since_last = pd.Timestamp.now() - self.last_trade_time
        cooldown_seconds = self.trade_cooldown_minutes * 60
        remaining = cooldown_seconds - time_since_last.total_seconds()

        return max(0, int(remaining))

    def calculate_advanced_position_size(self, portfolio_value: float, price: float,
                                       signal_quality: float, market_regime_adjustment: float,
                                       volatility: float, symbol: str) -> Tuple[float, int]:
        """
        Advanced position sizing with signal quality and market regime adjustments.

        Args:
            portfolio_value: Current portfolio value
            price: Current asset price
            signal_quality: Signal quality score (0-100)
            market_regime_adjustment: Market regime risk adjustment (0.5-2.0)
            volatility: Asset volatility (0-1)
            symbol: Trading symbol

        Returns:
            Tuple of (quantity, leverage)
        """
        try:
            # Base position size calculation
            base_quantity, base_leverage = self.calculate_position_size(portfolio_value, price, symbol=symbol)

            # Signal quality adjustment (higher quality = larger position)
            quality_multiplier = 0.5 + (signal_quality / 100) * 0.8  # 0.5 to 1.3

            # Market regime adjustment
            regime_multiplier = market_regime_adjustment

            # Volatility adjustment (higher volatility = smaller position)
            volatility_multiplier = max(0.3, 1.0 - volatility)

            # Combined adjustment
            total_multiplier = quality_multiplier * regime_multiplier * volatility_multiplier

            # Apply adjustments
            adjusted_quantity = base_quantity * total_multiplier

            # Ensure we don't exceed maximum position value
            max_usd_limit = getattr(self, 'max_position_value_usd', 100)
            position_value = (adjusted_quantity * price) / base_leverage

            if position_value > max_usd_limit:
                adjusted_quantity = (max_usd_limit * base_leverage) / price
                logger.info(f"Position size capped at ${max_usd_limit} for {symbol}")

            # Ensure minimum viable position
            min_position_value = 10  # $10 minimum
            if position_value < min_position_value:
                adjusted_quantity = (min_position_value * base_leverage) / price
                logger.info(f"Position size increased to ${min_position_value} minimum for {symbol}")

            logger.info(f"Advanced position sizing for {symbol}:")
            logger.info(f"  Base: {base_quantity:.6f} @ {base_leverage}x")
            logger.info(f"  Quality: {signal_quality:.1f}% (×{quality_multiplier:.2f})")
            logger.info(f"  Regime: ×{regime_multiplier:.2f}")
            logger.info(f"  Volatility: {volatility:.3f} (×{volatility_multiplier:.2f})")
            logger.info(f"  Final: {adjusted_quantity:.6f} @ {base_leverage}x")

            return adjusted_quantity, base_leverage

        except Exception as e:
            logger.error(f"Error in advanced position sizing: {e}")
            return self.calculate_position_size(portfolio_value, price, symbol=symbol)
