"""
Test script for Chart-Based Single Position Trading System
"""

import os
import sys
from dotenv import load_dotenv
import pandas as pd
import numpy as np

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_chart_test_data():
    """Create realistic chart data for testing."""
    np.random.seed(42)
    
    # Create trending data with chart patterns
    dates = pd.date_range('2024-01-01', periods=100, freq='5min')
    
    # Base price with trend
    base_price = 100
    trend = np.linspace(0, 5, 100)  # Upward trend
    
    # Add chart patterns
    consolidation = np.where((np.arange(100) > 20) & (np.arange(100) < 40), 
                           np.random.normal(0, 0.2, 100), 0)
    breakout = np.where(np.arange(100) > 40, 
                       np.cumsum(np.random.normal(0.1, 0.3, 100)), 0)
    
    close_prices = base_price + trend + consolidation + breakout
    
    # Generate OHLCV data
    data = pd.DataFrame({
        'timestamp': dates,
        'open': close_prices * (1 + np.random.normal(0, 0.002, 100)),
        'high': close_prices * (1 + np.abs(np.random.normal(0, 0.005, 100))),
        'low': close_prices * (1 - np.abs(np.random.normal(0, 0.005, 100))),
        'close': close_prices,
        'volume': np.random.uniform(1000, 5000, 100) * (1 + np.random.choice([0, 2], 100, p=[0.7, 0.3]))
    })
    
    # Add technical indicators
    data['RSI'] = 50 + 30 * np.sin(np.arange(100) * 0.1) + np.random.normal(0, 5, 100)
    data['RSI'] = np.clip(data['RSI'], 0, 100)
    
    return data

def test_chart_analysis_engine():
    """Test the chart analysis engine."""
    print("📊 Testing Chart Analysis Engine")
    print("=" * 50)
    
    try:
        from chart_analysis_engine import ChartAnalysisEngine
        
        # Initialize engine
        config = {
            'support_resistance_periods': 20,
            'breakout_threshold': 0.005,
            'volume_surge_threshold': 1.5,
            'min_confidence_threshold': 70,
            'min_risk_reward_ratio': 1.5
        }
        
        engine = ChartAnalysisEngine(config)
        
        print(f"📊 Engine Configuration:")
        print(f"   Min confidence: {config['min_confidence_threshold']}%")
        print(f"   Min risk/reward: {config['min_risk_reward_ratio']}")
        print(f"   Breakout threshold: {config['breakout_threshold']*100:.1f}%")
        
        # Create test data for multiple timeframes
        data_5m = create_chart_test_data()
        data_15m = create_chart_test_data()
        data_1h = create_chart_test_data()
        
        # Perform comprehensive analysis
        analysis = engine.comprehensive_chart_analysis(data_5m, data_15m, data_1h, 'TESTUSDT')
        
        print(f"\n📊 Chart Analysis Results:")
        print(f"   Signal: {analysis.signal}")
        print(f"   Confidence: {analysis.confidence:.1f}%")
        print(f"   Pattern: {analysis.pattern.value}")
        print(f"   Entry Reason: {analysis.entry_reason}")
        print(f"   Support: ${analysis.support_level:.6f}")
        print(f"   Resistance: ${analysis.resistance_level:.6f}")
        print(f"   Target: ${analysis.target_price:.6f}")
        print(f"   Stop Loss: ${analysis.stop_loss:.6f}")
        print(f"   Risk/Reward: {analysis.risk_reward_ratio:.2f}")
        print(f"   Volume Confirmed: {analysis.volume_confirmation}")
        print(f"   Timeframe Alignment: {analysis.timeframe_alignment}")
        
        if analysis.confidence > 0:
            print(f"   ✅ Chart analysis engine working correctly")
            return True
        else:
            print(f"   ❌ Chart analysis engine not working")
            return False
        
    except Exception as e:
        print(f"❌ Error testing chart analysis engine: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_position_manager():
    """Test the single position manager."""
    print("\n🎯 Testing Single Position Manager")
    print("=" * 50)
    
    try:
        from single_position_manager import SinglePositionManager
        
        # Initialize manager
        config = {
            'min_time_between_trades': 2
        }
        
        manager = SinglePositionManager(config)
        
        print(f"📊 Manager Configuration:")
        print(f"   Max positions: 1 (single position only)")
        print(f"   Min time between trades: {config['min_time_between_trades']} minutes")
        
        # Test position management
        print(f"\n📊 Testing Position Management:")
        
        # Check initial state
        has_position = manager.has_active_position()
        can_open, reason = manager.can_open_new_position()
        
        print(f"   Initial state: No position = {not has_position}")
        print(f"   Can open new: {can_open} ({reason})")
        
        # Test opening position
        success = manager.open_position(
            symbol='TESTUSDT',
            side='BUY',
            entry_price=1.0,
            quantity=100.0,
            leverage=5,
            entry_reason='Test pattern',
            target_price=1.02,
            stop_loss=0.98,
            support_level=0.97,
            resistance_level=1.03,
            chart_pattern='breakout_bullish',
            risk_reward_ratio=2.0
        )
        
        print(f"   Position opened: {success}")
        
        if success:
            # Test position status
            status = manager.get_position_status(1.01)  # Price moved up
            print(f"   Position symbol: {status['symbol']}")
            print(f"   Current P&L: ${status['current_pnl']:.2f}")
            print(f"   Hold time: {status['hold_minutes']:.1f} minutes")
            
            # Test closing position
            closed = manager.close_position(1.01, 'Test close', status['current_pnl'])
            print(f"   Position closed: {closed}")
            
            # Test statistics
            stats = manager.get_trading_statistics()
            print(f"   Total trades: {stats['total_trades']}")
            print(f"   Win rate: {stats['win_rate']:.1f}%")
            
            print(f"   ✅ Single position manager working correctly")
            return True
        else:
            print(f"   ❌ Failed to open position")
            return False
        
    except Exception as e:
        print(f"❌ Error testing single position manager: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_timeframe_data():
    """Test multi-timeframe data retrieval."""
    print("\n📈 Testing Multi-Timeframe Data")
    print("=" * 50)
    
    try:
        from multi_timeframe_analysis import MultiTimeframeAnalysis
        from binance_client import BinanceClient
        
        # Initialize components (mock client for testing)
        class MockClient:
            def get_historical_klines(self, symbol, timeframe, periods):
                # Return mock data
                return create_chart_test_data()
        
        client = MockClient()
        mta = MultiTimeframeAnalysis(client)
        
        print(f"📊 Multi-Timeframe Configuration:")
        print(f"   Timeframes: {list(mta.timeframes.keys())}")
        print(f"   Weights: {[tf['weight'] for tf in mta.timeframes.values()]}")
        
        # Test data retrieval
        chart_data = mta.get_chart_analysis_data('TESTUSDT')
        
        print(f"\n📊 Chart Data Results:")
        for timeframe, data in chart_data.items():
            if data is not None:
                print(f"   {timeframe}: {len(data)} candles with indicators")
            else:
                print(f"   {timeframe}: No data")
        
        # Check if we got data for all timeframes
        valid_data = sum(1 for data in chart_data.values() if data is not None)
        
        if valid_data >= 2:  # At least 2 timeframes
            print(f"   ✅ Multi-timeframe data working correctly")
            return True
        else:
            print(f"   ❌ Insufficient timeframe data")
            return False
        
    except Exception as e:
        print(f"❌ Error testing multi-timeframe data: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_chart_based_features():
    """Show chart-based trading features."""
    print("📊 Chart-Based Single Position Trading Features")
    print("=" * 70)
    
    print("📈 CHART ANALYSIS PRIORITY:")
    print("   • Multi-timeframe analysis (5m, 15m, 1h)")
    print("   • Price action pattern detection")
    print("   • Support/resistance level identification")
    print("   • Volume confirmation requirements")
    print("   • Moving average trend analysis")
    print("   • RSI momentum confirmation")
    print()
    
    print("🎯 SINGLE POSITION MANAGEMENT:")
    print("   • Maximum 1 concurrent position")
    print("   • Wait for position close before new trades")
    print("   • Scalping disabled when position active")
    print("   • Comprehensive position tracking")
    print()
    
    print("⏰ PROFIT TAKING STRATEGY:")
    print("   • 5+ minutes: Take any profit > $0")
    print("   • 15+ minutes: Close if loss < $2")
    print("   • 30+ minutes: Force close regardless of P&L")
    print("   • Stop loss and target price monitoring")
    print()
    
    print("🔍 ENTRY CONDITIONS:")
    print("   • Minimum 70% confidence threshold")
    print("   • Minimum 1.5:1 risk/reward ratio")
    print("   • Volume surge confirmation")
    print("   • Multi-timeframe alignment")
    print("   • Chart pattern validation")

def main():
    """Run chart-based trading tests."""
    print("🧪 Chart-Based Single Position Trading Test Suite")
    print("=" * 80)
    
    show_chart_based_features()
    
    tests = [
        ("Chart Analysis Engine", test_chart_analysis_engine),
        ("Single Position Manager", test_single_position_manager),
        ("Multi-Timeframe Data", test_multi_timeframe_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 CHART-BASED SINGLE POSITION TRADING READY!")
        print("\n✅ Features Implemented:")
        print("   ✅ Multi-timeframe chart analysis")
        print("   ✅ Single position management")
        print("   ✅ Chart pattern detection")
        print("   ✅ Support/resistance analysis")
        print("   ✅ Volume confirmation")
        print("   ✅ Risk/reward validation")
        print("   ✅ Profit-taking intervals")
        
        print("\n🚀 Expected Bot Behavior:")
        print("   • Analyzes charts across 5m, 15m, 1h timeframes")
        print("   • Opens maximum 1 position at a time")
        print("   • Requires 70%+ confidence for entry")
        print("   • Maintains 1.5:1+ risk/reward ratio")
        print("   • Takes profits at 5, 15, 30 minute intervals")
        print("   • Waits for position close before new trades")
        print("   • Focuses on high-quality chart-based signals")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
