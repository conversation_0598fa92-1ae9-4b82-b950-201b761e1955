# Termux-Optimized Requirements for Mobile Trading Bot
# Install with: pip install -r requirements_termux.txt

# Core Python packages
numpy>=1.21.0
pandas>=1.3.0
requests>=2.25.0
PyYAML>=5.4.0
python-dateutil>=2.8.0
pytz>=2021.1

# Trading and API packages
python-binance>=1.0.15
websocket-client>=1.2.0

# Technical Analysis (optional - may fail on some Termux setups)
# ta-lib>=0.4.21

# Utilities
urllib3>=1.26.0
certifi>=2021.5.25

# Configuration and environment
python-dotenv>=0.19.0

# Lightweight alternatives for mobile
# Instead of matplotlib (heavy), we use text-based dashboard
# Instead of seaborn (heavy), we use simple statistics

# Optional: For advanced features (install if needed)
# scipy>=1.7.0  # May be heavy for mobile
# scikit-learn>=1.0.0  # May be heavy for mobile

# Development and testing (optional)
# pytest>=6.2.0
# pytest-cov>=2.12.0
