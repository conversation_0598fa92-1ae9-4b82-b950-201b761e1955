# Enhanced Binance Futures Trading Bot

This trading bot has been enhanced to follow the guidelines in `context.txt`, implementing a comprehensive futures trading strategy for altcoins with advanced risk management, market condition awareness, and profit-taking mechanisms.

## 🚀 Key Features

### 📊 Enhanced Entry/Exit Conditions
- **EMA + RSI Combo**: Long when EMA Fast > EMA Slow + RSI < 30, Short when EMA Fast < EMA Slow + RSI > 70
- **Volume Spike Confirmation**: Requires volume increase to confirm signals
- **Multi-Indicator Validation**: MACD confirmation for stronger signals
- **Market Trend Context**: Avoids sideways/noise zones by checking EMA spread and volatility

### 🛡️ Advanced Risk Management
- **Dynamic Leverage Control**: Max 5x leverage with automatic adjustment based on volatility
- **Position Sizing**: 2-5% of portfolio with dynamic adjustment based on market conditions
- **Stop Loss**: -1.5% stop loss as specified in context.txt
- **Consecutive Loss Protection**: Reduces position size after consecutive losses
- **Daily Loss Limits**: Stops trading after reaching daily loss threshold

### 🌐 Market Condition Awareness
- **Global Market Analysis**: Evaluates overall market health before trading
- **Volume Pattern Detection**: Identifies low volume periods across altcoins
- **Volatility Monitoring**: Detects high volatility periods and adjusts strategy
- **Major Coin Influence**: Detects when BTC/ETH are leading with high volatility
- **Trading Pause**: Automatically pauses trading during dangerous market conditions

### 💰 Enhanced Profit-Taking
- **Immediate Profit Exit**: Closes position as soon as it becomes profitable (context.txt rule)
- **Trailing Stop**: Optional trailing stop functionality for capturing more profit
- **Break-Even Logic**: Sets stop to break-even after reaching profit threshold
- **Dynamic Profit Targets**: Adjusts profit targets based on market volatility

### ⏱️ Pro-Level Trading Intelligence
- **Time-Based Filters**: Avoids low-volume hours (configurable)
- **Cooldown Timer**: Waits 15-30 minutes between trades to prevent overtrading
- **Market Trend Context**: Evaluates trend strength and direction
- **Dynamic Altcoin Discovery**: Automatically scans all available Binance Futures altcoins
- **Altcoin Focus**: Specifically designed for altcoin trading (excluding BTC, ETH)

### 🔍 Dynamic Altcoin Scanning
- **Automatic Discovery**: Scans all available altcoins on Binance Futures
- **Volume Filtering**: Selects only liquid altcoins with sufficient trading volume
- **Real-Time Updates**: Refreshes altcoin list daily to include new listings
- **Smart Exclusions**: Automatically excludes major coins (BTC, ETH, BNB) and stablecoins
- **Top Selection**: Focuses on top altcoins by volume for best trading opportunities

### 📝 Comprehensive Logging
- **Trade Database**: Records all trades with detailed metrics
- **Performance Tracking**: Tracks win rate, profit factor, drawdown, etc.
- **Market Condition Logging**: Records market conditions at trade time
- **CSV Export**: Exports trade data for external analysis
- **Alert System**: Optional Telegram notifications for trades

### 🧪 Backtesting Framework
- **Historical Data Testing**: Tests strategy on real historical altcoin data
- **Context.txt Validation**: Validates implementation of context.txt rules
- **Performance Analysis**: Comprehensive metrics and visualization
- **Parameter Optimization**: Ability to fine-tune strategy parameters

## 📋 Implementation Details

### Enhanced Strategy Components

1. **EnhancedFuturesStrategy**: Implements the EMA + RSI strategy with all pro-level intelligence features
2. **RiskManager**: Advanced risk management with futures-specific controls
3. **ProfitManager**: Manages profit-taking mechanisms and position tracking
4. **MarketMonitor**: Analyzes overall market conditions and health
5. **TradeLogger**: Comprehensive trade logging and performance tracking

### Futures-Specific Enhancements

1. **Leverage Management**: Sets and adjusts leverage (max 5x) via Binance Futures API
2. **Position Monitoring**: Uses futures_position_information() to track unrealized PnL
3. **PnL-Based Exits**: Monitors and exits based on unrealized profit
4. **Futures Order Types**: Uses appropriate futures order types for entries and exits

## 🔧 Configuration

The enhanced configuration in `config/config.yaml` includes:

```yaml
# Enhanced Risk Management (Context.txt compliant)
risk_management:
  max_position_size: 0.05  # 5% max position size (context.txt: 2-5%)
  min_position_size: 0.02  # 2% min position size
  stop_loss_percentage: 0.015  # 1.5% stop loss (context.txt requirement)
  take_profit_percentage: 0.0001  # Any profit (context.txt: exit as soon as profitable)
  max_leverage: 5  # Max 5x leverage (context.txt requirement)
  # ... additional settings

# Enhanced Strategy Configuration
strategies:
  enhanced_futures:
    enabled: true  # Main strategy following context.txt
    # EMA + RSI combo (context.txt requirements)
    ema_fast_period: 12
    ema_slow_period: 26
    rsi_period: 14
    rsi_oversold: 30  # RSI < 30 for long entry
    rsi_overbought: 70  # RSI > 70 for short entry
    # ... additional settings
```

## 📈 Trade Lifecycle

1. **Market Analysis**: Bot evaluates overall market conditions
2. **Symbol Scanning**: Scans eligible altcoins for trading opportunities
3. **Pro-Level Filtering**: Applies volume, trend, and time-based filters
4. **Entry Decision**: Generates entry signal based on EMA + RSI with confirmations
5. **Risk Calculation**: Calculates position size and leverage based on risk parameters
6. **Position Opening**: Opens position with appropriate leverage
7. **Position Monitoring**: Continuously monitors position for exit conditions
8. **Profit Taking**: Exits as soon as position becomes profitable (context.txt rule)
9. **Logging & Analysis**: Records trade details and updates performance metrics
10. **Cooldown**: Waits for cooldown period before next trade

## 🚀 Getting Started

### Prerequisites
- Python 3.8+
- Binance account with Futures enabled
- API keys with Futures trading permissions
- Sufficient USDT balance in Futures wallet

### Installation
1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Configure your API keys in `.env` file:
   ```
   BINANCE_API_KEY=your_actual_api_key
   BINANCE_SECRET_KEY=your_actual_secret_key
   INITIAL_BALANCE=1000
   TELEGRAM_TOKEN=your_telegram_bot_token (optional)
   TELEGRAM_CHAT_ID=your_telegram_chat_id (optional)
   ```

### ⚠️ LIVE TRADING SETUP
**The bot is configured for LIVE TRADING. Please read the setup guide carefully:**

1. **Test Setup**: `python test_live_setup.py`
2. **Review Guide**: Read `LIVE_TRADING_SETUP.md` thoroughly
3. **Start Small**: Begin with small position sizes
4. **Monitor Closely**: Watch the first few trades carefully

### Running the Bot
- **Test setup first**: `python test_live_setup.py`
- **View all available altcoins**: `python show_altcoins.py`
- **Update altcoin list**: `python update_altcoins.py`
- **Start live trading**: `python main.py`
- **Run backtesting**: `python run_backtest.py`
- **Monitor performance**: `python monitor.py`

### 🛡️ Safety First
- Start with 2% position sizes maximum
- Use 2-3x leverage initially
- Set daily loss limits appropriately
- Never leave unattended initially

## ⚠️ Risk Warning

Trading cryptocurrency futures involves substantial risk of loss and is not suitable for all investors. The high degree of leverage can work against you as well as for you. Before deciding to trade cryptocurrency futures you should carefully consider your investment objectives, level of experience, and risk appetite.

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Based on the guidelines in context.txt
- Uses Binance Python API for futures trading
- Implements best practices for algorithmic trading
