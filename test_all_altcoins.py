"""
Test script to verify ALL altcoins trading functionality.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_altcoin_scanner_all():
    """Test altcoin scanner with ALL altcoins mode."""
    print("🪙 Testing ALL Altcoins Scanner")
    print("=" * 50)
    
    try:
        from binance_client import BinanceClient
        from altcoin_scanner import AltcoinScanner
        
        # Initialize client and scanner
        client = BinanceClient(testnet=True)
        
        # Scanner config for ALL altcoins
        scanner_config = {
            'min_volume_24h': 50000,      # Very low volume requirement
            'min_price': 0.00001,         # Very low minimum price
            'max_price': 10000,           # High maximum price
            'excluded_base_assets': ['BTC', 'ETH', 'BNB', 'USDT', 'USDC', 'BUSD', 'FDUSD', 'TUSD', 'DAI']
        }
        
        scanner = AltcoinScanner(client, scanner_config)
        
        print(f"📊 Scanner Configuration:")
        print(f"   Min volume: ${scanner_config['min_volume_24h']:,}")
        print(f"   Price range: ${scanner_config['min_price']} - ${scanner_config['max_price']}")
        print(f"   Excluded: {scanner_config['excluded_base_assets']}")
        
        # Test getting ALL altcoins
        print(f"\n🔍 Scanning for ALL altcoins...")
        all_altcoins = scanner.get_top_altcoins(limit=None)  # None = ALL
        
        print(f"\n📊 ALL Altcoins Results:")
        print(f"   Total altcoins found: {len(all_altcoins)}")
        print(f"   Sample altcoins: {', '.join(all_altcoins[:15])}")
        if len(all_altcoins) > 15:
            print(f"   ... and {len(all_altcoins) - 15} more!")
        
        # Test getting limited altcoins for comparison
        print(f"\n🔍 Scanning for top 50 altcoins (comparison)...")
        top_50 = scanner.get_top_altcoins(limit=50)
        
        print(f"\n📊 Top 50 Results:")
        print(f"   Top 50 altcoins: {len(top_50)}")
        print(f"   Sample: {', '.join(top_50[:10])}")
        
        # Verify ALL mode returns more than limited mode
        if len(all_altcoins) >= len(top_50):
            print(f"\n✅ ALL altcoins mode working correctly")
            print(f"   ALL mode: {len(all_altcoins)} altcoins")
            print(f"   Limited mode: {len(top_50)} altcoins")
            return True
        else:
            print(f"\n❌ ALL altcoins mode not working correctly")
            return False
        
    except Exception as e:
        print(f"❌ Error testing altcoin scanner: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_all_altcoins():
    """Test configuration for ALL altcoins."""
    print("\n⚙️ Testing ALL Altcoins Configuration")
    print("=" * 50)
    
    try:
        from utils import load_config
        
        # Load config
        config = load_config('config/config.yaml')
        trading_config = config.get('trading', {})
        
        print(f"📊 Trading Configuration:")
        print(f"   Dynamic scanning: {trading_config.get('use_dynamic_scanning', False)}")
        print(f"   Max symbols: {trading_config.get('max_symbols', 'NOT SET')}")
        print(f"   Excluded symbols: {trading_config.get('exclude_symbols', [])}")
        
        # Check if max_symbols is None (ALL altcoins mode)
        max_symbols = trading_config.get('max_symbols')
        if max_symbols is None:
            print(f"\n✅ ALL altcoins mode configured correctly")
            print(f"   max_symbols = null (trades ALL altcoins)")
        elif isinstance(max_symbols, int):
            print(f"\n⚠️  Limited altcoins mode")
            print(f"   max_symbols = {max_symbols} (trades top {max_symbols} altcoins)")
        else:
            print(f"\n❌ Invalid max_symbols configuration: {max_symbols}")
            return False
        
        # Check strategy configuration
        strategies = config.get('strategies', {})
        for strategy_name, strategy_config in strategies.items():
            if strategy_config.get('enabled', False):
                top_n = strategy_config.get('top_n_tokens')
                min_volume = strategy_config.get('min_volume_24h', 0)
                
                print(f"\n📊 {strategy_name} Strategy:")
                print(f"   Enabled: {strategy_config.get('enabled', False)}")
                print(f"   Top N tokens: {top_n}")
                print(f"   Min volume: ${min_volume:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

def show_all_altcoins_features():
    """Show the ALL altcoins trading features."""
    print("\n🎯 ALL Altcoins Trading Features")
    print("=" * 60)
    
    print("🪙 COMPREHENSIVE ALTCOIN COVERAGE:")
    print("   • Trades ALL available altcoins on Binance")
    print("   • No limit on number of altcoins")
    print("   • Includes micro-cap and new altcoins")
    print("   • Excludes only major coins (BTC, ETH, BNB)")
    print()
    
    print("📊 DYNAMIC SCANNING:")
    print("   • Automatically discovers new altcoins")
    print("   • Updates altcoin list every 24 hours")
    print("   • Filters by volume and price criteria")
    print("   • Removes delisted or inactive altcoins")
    print()
    
    print("⚙️ FLEXIBLE CONFIGURATION:")
    print("   • max_symbols: null = ALL altcoins")
    print("   • max_symbols: number = limit to top N")
    print("   • Configurable volume and price filters")
    print("   • Customizable exclusion list")
    print()
    
    print("🚀 TRADING BENEFITS:")
    print("   • Maximum diversification across altcoins")
    print("   • Capture opportunities in all altcoin markets")
    print("   • Early access to new and emerging altcoins")
    print("   • Comprehensive altcoin portfolio coverage")

def main():
    """Run ALL altcoins trading tests."""
    print("🧪 ALL Altcoins Trading Test Suite")
    print("=" * 70)
    
    show_all_altcoins_features()
    
    tests = [
        ("Altcoin Scanner ALL Mode", test_altcoin_scanner_all),
        ("Configuration ALL Mode", test_config_all_altcoins)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 ALL ALTCOINS TRADING READY!")
        print("\n✅ Features Configured:")
        print("   ✅ ALL altcoins scanning enabled")
        print("   ✅ No symbol limit (max_symbols = null)")
        print("   ✅ Low volume filters for more altcoins")
        print("   ✅ Wide price range for all altcoin types")
        print("   ✅ Major coins excluded (BTC, ETH, BNB)")
        
        print("\n🚀 Expected Bot Behavior:")
        print("   • Scans and trades ALL available altcoins")
        print("   • Discovers hundreds of altcoin opportunities")
        print("   • Includes micro-cap and new altcoins")
        print("   • Updates altcoin list automatically")
        print("   • Maximum altcoin market coverage")
        
        print("\n🪙 Estimated Altcoin Count:")
        print("   • Typical Binance altcoins: 200-500+")
        print("   • After filtering: 100-300+")
        print("   • Active trading pairs: 50-200+")
        print("   • Depends on market conditions")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
