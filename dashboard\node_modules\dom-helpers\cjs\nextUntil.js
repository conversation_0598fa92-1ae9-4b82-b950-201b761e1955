"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");

exports.__esModule = true;
exports.default = nextUntil;

var _collectSiblings = _interopRequireDefault(require("./collectSiblings"));

/**
 * Collects all next sibling elements of an element until a given selector is matched.
 * 
 * @param node the referene node
 * @param selector the selector to match
 */
function nextUntil(node, selector) {
  return (0, _collectSiblings.default)(node, node, selector);
}

module.exports = exports["default"];