#!/usr/bin/env python3
"""
Test Symbol Validation Fix
Tests that invalid symbols are filtered out before API calls.
"""

import sys
import os
sys.path.append('src')

def test_symbol_validation():
    """Test the symbol validation system."""
    print("🔧 TESTING SYMBOL VALIDATION")
    print("=" * 40)
    
    try:
        from binance_client import BinanceClient
        
        # Initialize client
        print("Initializing Binance client...")
        client = BinanceClient(testnet=True)
        print("✅ BinanceClient initialized")
        
        # Test invalid symbols that were causing errors
        invalid_symbols = ['LITUSDT', '1000PEPEUSDT', 'DDOGEUSDT', 'INVALIDUSDT']
        valid_symbols = ['BTCUSDT', 'ETHUSDT', 'DOGEUSDT', 'XLMUSDT']
        
        print(f"\n🔍 Testing INVALID symbols:")
        for symbol in invalid_symbols:
            is_valid = client.is_valid_symbol(symbol)
            status = "✅ VALID" if is_valid else "❌ INVALID (correctly filtered)"
            print(f"   {symbol}: {status}")
        
        print(f"\n🔍 Testing VALID symbols:")
        for symbol in valid_symbols:
            is_valid = client.is_valid_symbol(symbol)
            status = "✅ VALID" if is_valid else "❌ INVALID (unexpected)"
            print(f"   {symbol}: {status}")
        
        # Test getting all valid symbols
        all_valid = client.get_valid_symbols()
        print(f"\n📊 Total valid symbols: {len(all_valid)}")
        print(f"Sample valid symbols: {list(all_valid)[:10]}")
        
        # Test data fetching with invalid symbol
        print(f"\n🔍 Testing data fetch with invalid symbol:")
        try:
            data = client.get_historical_klines('INVALIDUSDT', '1h', 10)
            if data.empty:
                print("   ✅ Invalid symbol correctly returned empty DataFrame")
            else:
                print("   ❌ Invalid symbol unexpectedly returned data")
        except Exception as e:
            print(f"   ❌ Error (should be handled): {e}")
        
        # Test data fetching with valid symbol
        print(f"\n🔍 Testing data fetch with valid symbol:")
        try:
            data = client.get_historical_klines('BTCUSDT', '1h', 10)
            if not data.empty:
                print(f"   ✅ Valid symbol returned {len(data)} rows of data")
            else:
                print("   ⚠️  Valid symbol returned empty data (might be network issue)")
        except Exception as e:
            print(f"   ❌ Error fetching valid symbol: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_altcoin_scanner():
    """Test that altcoin scanner uses valid symbols."""
    print(f"\n🔧 TESTING ALTCOIN SCANNER")
    print("=" * 40)
    
    try:
        from altcoin_scanner import AltcoinScanner
        from binance_client import BinanceClient
        
        # Initialize
        client = BinanceClient(testnet=True)
        scanner_config = {
            'min_volume_24h': 10000,
            'min_price': 0.000001,
            'max_price': 100000,
            'quote_asset': 'USDT',
            'scan_interval_hours': 1
        }
        scanner = AltcoinScanner(client, scanner_config)
        
        print("✅ AltcoinScanner initialized")
        
        # Test scanning
        print("🔍 Scanning for altcoins...")
        altcoins = scanner.scan_altcoins(force_rescan=True)
        
        print(f"📊 Found {len(altcoins)} qualifying altcoins")
        print(f"Sample altcoins: {altcoins[:10]}")
        
        # Check if any invalid symbols made it through
        invalid_found = []
        for symbol in altcoins[:20]:  # Check first 20
            if not client.is_valid_symbol(symbol):
                invalid_found.append(symbol)
        
        if invalid_found:
            print(f"❌ Found invalid symbols: {invalid_found}")
            return False
        else:
            print("✅ All scanned symbols are valid")
            return True
        
    except Exception as e:
        print(f"❌ Altcoin scanner test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🤖 SYMBOL VALIDATION FIX TEST")
    print("Testing that invalid symbols are filtered out")
    print("=" * 60)
    
    # Test 1: Symbol validation
    test1_passed = test_symbol_validation()
    
    # Test 2: Altcoin scanner
    test2_passed = test_altcoin_scanner()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED!")
        print("🚀 Invalid symbols should no longer cause API errors")
        print("💡 Bot will only trade valid symbols from Binance")
        print("🔄 Restart the bot: python main.py")
    else:
        print("⚠️  SOME TESTS FAILED")
        print("There might still be issues with symbol validation")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test crashed: {e}")
