#!/bin/bash
# Android/Termux Setup Script for Trading Bot
# Fixes common network and dependency issues on mobile

echo "🤖 ANDROID/TERMUX TRADING BOT SETUP"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running on Android/Termux
check_environment() {
    print_info "Checking environment..."
    
    if [[ "$OSTYPE" == *"android"* ]] || [[ -n "$ANDROID_ROOT" ]] || command -v termux-info &> /dev/null; then
        print_status "Detected Android/Termux environment"
        return 0
    else
        print_warning "Not running on Android/Termux"
        print_info "This script is optimized for Android/Termux"
        return 1
    fi
}

# Update packages
update_packages() {
    print_info "Updating Termux packages..."
    
    if pkg update -y && pkg upgrade -y; then
        print_status "Packages updated successfully"
    else
        print_error "Failed to update packages"
        print_info "Try: termux-change-repo and select a different mirror"
        return 1
    fi
}

# Install required packages
install_dependencies() {
    print_info "Installing required packages..."
    
    local packages=(
        "python"
        "python-pip"
        "curl"
        "wget"
        "git"
        "openssh"
        "termux-api"
    )
    
    for package in "${packages[@]}"; do
        print_info "Installing $package..."
        if pkg install -y "$package"; then
            print_status "$package installed"
        else
            print_warning "Failed to install $package (might already be installed)"
        fi
    done
}

# Install Python packages
install_python_packages() {
    print_info "Installing Python packages for trading bot..."
    
    # Upgrade pip first
    python -m pip install --upgrade pip
    
    local python_packages=(
        "requests"
        "python-binance"
        "pandas"
        "numpy"
        "pyyaml"
        "python-dotenv"
        "urllib3"
        "certifi"
    )
    
    for package in "${python_packages[@]}"; do
        print_info "Installing Python package: $package"
        if python -m pip install "$package"; then
            print_status "$package installed"
        else
            print_error "Failed to install $package"
        fi
    done
}

# Fix network issues
fix_network_issues() {
    print_info "Applying network fixes for Android/Termux..."
    
    # Set up storage access
    if command -v termux-setup-storage &> /dev/null; then
        print_info "Setting up storage access..."
        termux-setup-storage
    fi
    
    # Enable wake lock to prevent network interruption
    if command -v termux-wake-lock &> /dev/null; then
        print_info "Enabling wake lock to prevent sleep..."
        termux-wake-lock
        print_status "Wake lock enabled (prevents network interruption)"
    fi
    
    # Test network connectivity
    print_info "Testing network connectivity..."
    if curl -I https://api.binance.com --connect-timeout 10; then
        print_status "Network connectivity to Binance API: OK"
    else
        print_error "Cannot connect to Binance API"
        print_info "Possible fixes:"
        echo "  1. Switch from WiFi to Mobile Data (or vice versa)"
        echo "  2. Use a VPN (pkg install openvpn)"
        echo "  3. Check if your carrier blocks crypto APIs"
        echo "  4. Try different DNS servers (8.8.8.8, 1.1.1.1)"
    fi
}

# Create environment file
create_env_file() {
    print_info "Creating environment configuration..."
    
    if [[ ! -f ".env" ]]; then
        cat > .env << 'EOF'
# Binance API Configuration
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here

# Trading Configuration
TESTNET_MODE=true
MAX_POSITION_SIZE=100
RISK_PER_TRADE=0.02

# Mobile-specific settings
MOBILE_MODE=true
NETWORK_TIMEOUT=30
RETRY_ATTEMPTS=5
EOF
        print_status "Created .env file with default configuration"
        print_warning "Please edit .env file with your Binance API keys"
    else
        print_info ".env file already exists"
    fi
}

# Test trading bot
test_trading_bot() {
    print_info "Testing trading bot setup..."
    
    if python test_mobile_network.py; then
        print_status "Network test passed"
        
        print_info "Testing trading bot import..."
        if python -c "import sys; sys.path.append('src'); from binance_client import BinanceClient; print('✅ Trading bot modules can be imported')"; then
            print_status "Trading bot setup successful!"
            print_info "You can now run: python main.py"
        else
            print_error "Trading bot import failed"
            print_info "Check if all Python packages are installed correctly"
        fi
    else
        print_error "Network test failed"
        print_info "Please fix network issues before running trading bot"
    fi
}

# Main setup function
main() {
    echo
    print_info "Starting Android/Termux setup for trading bot..."
    echo
    
    # Check environment
    if ! check_environment; then
        print_warning "Continuing anyway..."
    fi
    
    # Update packages
    if ! update_packages; then
        print_error "Package update failed"
        exit 1
    fi
    
    # Install dependencies
    install_dependencies
    
    # Install Python packages
    install_python_packages
    
    # Fix network issues
    fix_network_issues
    
    # Create environment file
    create_env_file
    
    # Test setup
    test_trading_bot
    
    echo
    print_status "Android/Termux setup completed!"
    echo
    print_info "Next steps:"
    echo "  1. Edit .env file with your Binance API keys"
    echo "  2. Run: python test_mobile_network.py (to test network)"
    echo "  3. Run: python main.py (to start trading bot)"
    echo
    print_warning "Important for mobile trading:"
    echo "  • Keep Termux app in foreground or use wake lock"
    echo "  • Ensure stable internet connection"
    echo "  • Monitor battery usage and charging"
    echo "  • Consider using WiFi for stability"
}

# Run main function
main "$@"
