"""
Advanced Multi-Strategy Manager for Enhanced Win Rate
Combines multiple strategies with intelligent filtering and confirmation.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import talib

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Strategy types for diversification."""
    MOMENTUM = "momentum"
    REVERSAL = "reversal"
    BREAKOUT = "breakout"
    MEAN_REVERSION = "mean_reversion"
    VOLUME_PROFILE = "volume_profile"
    CANDLESTICK = "candlestick"
    TREND_FOLLOWING = "trend_following"
    LIQUIDITY_SWEEP = "liquidity_sweep"

@dataclass
class StrategySignal:
    """Individual strategy signal."""
    strategy_type: StrategyType
    signal: str  # BUY, SELL, HOLD
    confidence: float
    strength: float
    reason: str
    entry_price: float
    target_price: float
    stop_loss: float
    risk_reward_ratio: float
    timeframe: str

class AdvancedStrategyManager:
    """
    Advanced strategy manager that combines multiple trading strategies
    with intelligent filtering and confirmation systems.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Strategy weights (can be adjusted based on performance)
        self.strategy_weights = {
            StrategyType.MOMENTUM: 0.20,
            StrategyType.REVERSAL: 0.15,
            StrategyType.BREAKOUT: 0.20,
            StrategyType.MEAN_REVERSION: 0.15,
            StrategyType.VOLUME_PROFILE: 0.10,
            StrategyType.CANDLESTICK: 0.10,
            StrategyType.TREND_FOLLOWING: 0.10
        }
        
        # Performance tracking for adaptive weighting
        self.strategy_performance = {strategy: {'wins': 0, 'losses': 0, 'total_pnl': 0.0} 
                                   for strategy in StrategyType}
        
        # Enhanced filtering thresholds
        self.min_confirmation_strategies = config.get('min_confirmation_strategies', 3)
        self.min_overall_confidence = config.get('min_overall_confidence', 75)
        self.min_risk_reward_ratio = config.get('min_risk_reward_ratio', 2.0)
        
        logger.info("Advanced Strategy Manager initialized with 7 strategies")
    
    def analyze_all_strategies(self, data: pd.DataFrame, symbol: str) -> List[StrategySignal]:
        """Analyze all strategies and return signals."""
        signals = []
        
        try:
            # 1. Momentum Strategy
            momentum_signal = self.momentum_strategy(data, symbol)
            if momentum_signal:
                signals.append(momentum_signal)
            
            # 2. Reversal Strategy
            reversal_signal = self.reversal_strategy(data, symbol)
            if reversal_signal:
                signals.append(reversal_signal)
            
            # 3. Enhanced Breakout Strategy
            breakout_signal = self.enhanced_breakout_strategy(data, symbol)
            if breakout_signal:
                signals.append(breakout_signal)
            
            # 4. Mean Reversion Strategy
            mean_reversion_signal = self.mean_reversion_strategy(data, symbol)
            if mean_reversion_signal:
                signals.append(mean_reversion_signal)
            
            # 5. Volume Profile Strategy
            volume_signal = self.volume_profile_strategy(data, symbol)
            if volume_signal:
                signals.append(volume_signal)
            
            # 6. Candlestick Pattern Strategy
            candlestick_signal = self.candlestick_pattern_strategy(data, symbol)
            if candlestick_signal:
                signals.append(candlestick_signal)
            
            # 7. Trend Following Strategy
            trend_signal = self.trend_following_strategy(data, symbol)
            if trend_signal:
                signals.append(trend_signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error analyzing strategies for {symbol}: {e}")
            return []
    
    def momentum_strategy(self, data: pd.DataFrame, symbol: str) -> Optional[StrategySignal]:
        """MACD + RSI momentum strategy."""
        try:
            if len(data) < 26:
                return None
            
            # Calculate MACD
            macd_line, macd_signal, macd_hist = talib.MACD(data['close'].values)
            
            # Calculate RSI
            rsi = talib.RSI(data['close'].values, timeperiod=14)
            
            # Get current values
            current_macd = macd_line[-1]
            current_signal = macd_signal[-1]
            current_rsi = rsi[-1]
            current_price = data['close'].iloc[-1]
            
            # Momentum conditions
            macd_bullish = current_macd > current_signal
            rsi_momentum = 30 < current_rsi < 70  # Avoid extreme levels
            
            # Volume confirmation
            volume_surge = data['volume'].iloc[-1] > data['volume'].tail(20).mean() * 1.5
            
            if macd_bullish and rsi_momentum and volume_surge:
                confidence = 70 + (current_rsi - 50) if current_rsi > 50 else 70 - (50 - current_rsi)
                confidence = min(confidence, 90)
                
                target = current_price * 1.02  # 2% target
                stop_loss = current_price * 0.99  # 1% stop
                
                return StrategySignal(
                    strategy_type=StrategyType.MOMENTUM,
                    signal='BUY',
                    confidence=confidence,
                    strength=abs(current_macd - current_signal),
                    reason=f"MACD bullish crossover + RSI momentum ({current_rsi:.1f})",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(target - current_price) / (current_price - stop_loss),
                    timeframe='15m'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error in momentum strategy: {e}")
            return None
    
    def reversal_strategy(self, data: pd.DataFrame, symbol: str) -> Optional[StrategySignal]:
        """RSI divergence + oversold/overbought reversal strategy."""
        try:
            if len(data) < 20:
                return None
            
            rsi = talib.RSI(data['close'].values, timeperiod=14)
            current_rsi = rsi[-1]
            current_price = data['close'].iloc[-1]
            
            # Bollinger Bands for reversal confirmation
            bb_upper, bb_middle, bb_lower = talib.BBANDS(data['close'].values)
            
            # Reversal conditions
            oversold_reversal = current_rsi < 25 and current_price <= bb_lower[-1]
            overbought_reversal = current_rsi > 75 and current_price >= bb_upper[-1]
            
            if oversold_reversal:
                confidence = 80 + (25 - current_rsi)  # Higher confidence for more oversold
                target = bb_middle[-1]
                stop_loss = current_price * 0.98
                
                return StrategySignal(
                    strategy_type=StrategyType.REVERSAL,
                    signal='BUY',
                    confidence=min(confidence, 95),
                    strength=25 - current_rsi,
                    reason=f"Oversold reversal: RSI {current_rsi:.1f} + BB lower touch",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(target - current_price) / (current_price - stop_loss),
                    timeframe='15m'
                )
            
            elif overbought_reversal:
                confidence = 80 + (current_rsi - 75)
                target = bb_middle[-1]
                stop_loss = current_price * 1.02
                
                return StrategySignal(
                    strategy_type=StrategyType.REVERSAL,
                    signal='SELL',
                    confidence=min(confidence, 95),
                    strength=current_rsi - 75,
                    reason=f"Overbought reversal: RSI {current_rsi:.1f} + BB upper touch",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(current_price - target) / (stop_loss - current_price),
                    timeframe='15m'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error in reversal strategy: {e}")
            return None
    
    def enhanced_breakout_strategy(self, data: pd.DataFrame, symbol: str) -> Optional[StrategySignal]:
        """Enhanced breakout with volume and volatility confirmation."""
        try:
            if len(data) < 20:
                return None
            
            # Calculate support/resistance
            highs = data['high'].tail(20)
            lows = data['low'].tail(20)
            
            resistance = highs.max()
            support = lows.min()
            current_price = data['close'].iloc[-1]
            
            # ATR for volatility-based stops
            atr = talib.ATR(data['high'].values, data['low'].values, data['close'].values, timeperiod=14)
            current_atr = atr[-1]
            
            # Volume analysis
            avg_volume = data['volume'].tail(20).mean()
            current_volume = data['volume'].iloc[-1]
            volume_surge = current_volume > avg_volume * 2.0  # Strong volume requirement
            
            # Breakout conditions
            bullish_breakout = current_price > resistance * 1.002 and volume_surge
            bearish_breakout = current_price < support * 0.998 and volume_surge
            
            if bullish_breakout:
                confidence = 85 if volume_surge else 70
                target = current_price + (current_atr * 2)  # ATR-based target
                stop_loss = resistance * 0.998  # Just below breakout level
                
                return StrategySignal(
                    strategy_type=StrategyType.BREAKOUT,
                    signal='BUY',
                    confidence=confidence,
                    strength=(current_price - resistance) / resistance,
                    reason=f"Bullish breakout above {resistance:.6f} with volume surge",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(target - current_price) / (current_price - stop_loss),
                    timeframe='15m'
                )
            
            elif bearish_breakout:
                confidence = 85 if volume_surge else 70
                target = current_price - (current_atr * 2)
                stop_loss = support * 1.002
                
                return StrategySignal(
                    strategy_type=StrategyType.BREAKOUT,
                    signal='SELL',
                    confidence=confidence,
                    strength=(support - current_price) / support,
                    reason=f"Bearish breakout below {support:.6f} with volume surge",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(current_price - target) / (stop_loss - current_price),
                    timeframe='15m'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error in breakout strategy: {e}")
            return None
    
    def mean_reversion_strategy(self, data: pd.DataFrame, symbol: str) -> Optional[StrategySignal]:
        """Bollinger Bands mean reversion with Z-score confirmation."""
        try:
            if len(data) < 20:
                return None
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = talib.BBANDS(data['close'].values, timeperiod=20, nbdevup=2, nbdevdn=2)
            
            current_price = data['close'].iloc[-1]
            current_upper = bb_upper[-1]
            current_lower = bb_lower[-1]
            current_middle = bb_middle[-1]
            
            # Z-score calculation
            price_std = data['close'].tail(20).std()
            price_mean = data['close'].tail(20).mean()
            z_score = (current_price - price_mean) / price_std
            
            # Mean reversion conditions
            oversold_mean_reversion = current_price <= current_lower and z_score < -1.5
            overbought_mean_reversion = current_price >= current_upper and z_score > 1.5
            
            if oversold_mean_reversion:
                confidence = 75 + min(abs(z_score) * 5, 20)  # Higher confidence for extreme Z-scores
                target = current_middle
                stop_loss = current_price * 0.985
                
                return StrategySignal(
                    strategy_type=StrategyType.MEAN_REVERSION,
                    signal='BUY',
                    confidence=min(confidence, 95),
                    strength=abs(z_score),
                    reason=f"Mean reversion buy: Z-score {z_score:.2f}, BB lower touch",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(target - current_price) / (current_price - stop_loss),
                    timeframe='15m'
                )
            
            elif overbought_mean_reversion:
                confidence = 75 + min(abs(z_score) * 5, 20)
                target = current_middle
                stop_loss = current_price * 1.015
                
                return StrategySignal(
                    strategy_type=StrategyType.MEAN_REVERSION,
                    signal='SELL',
                    confidence=min(confidence, 95),
                    strength=abs(z_score),
                    reason=f"Mean reversion sell: Z-score {z_score:.2f}, BB upper touch",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(current_price - target) / (stop_loss - current_price),
                    timeframe='15m'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error in mean reversion strategy: {e}")
            return None
    
    def volume_profile_strategy(self, data: pd.DataFrame, symbol: str) -> Optional[StrategySignal]:
        """Volume profile anomaly detection."""
        try:
            if len(data) < 20:
                return None
            
            # Volume analysis
            volume_ma = data['volume'].tail(20).mean()
            volume_std = data['volume'].tail(20).std()
            current_volume = data['volume'].iloc[-1]
            
            # Price-volume relationship
            price_change = (data['close'].iloc[-1] - data['close'].iloc[-2]) / data['close'].iloc[-2]
            volume_anomaly = current_volume > volume_ma + (2 * volume_std)
            
            # Volume-price divergence
            if volume_anomaly and abs(price_change) > 0.005:
                current_price = data['close'].iloc[-1]
                
                if price_change > 0:  # Price up with volume spike
                    confidence = 70 + min(current_volume / volume_ma * 10, 25)
                    target = current_price * 1.015
                    stop_loss = current_price * 0.995
                    
                    return StrategySignal(
                        strategy_type=StrategyType.VOLUME_PROFILE,
                        signal='BUY',
                        confidence=min(confidence, 90),
                        strength=current_volume / volume_ma,
                        reason=f"Volume spike ({current_volume/volume_ma:.1f}x) with price rise",
                        entry_price=current_price,
                        target_price=target,
                        stop_loss=stop_loss,
                        risk_reward_ratio=(target - current_price) / (current_price - stop_loss),
                        timeframe='15m'
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"Error in volume profile strategy: {e}")
            return None
    
    def candlestick_pattern_strategy(self, data: pd.DataFrame, symbol: str) -> Optional[StrategySignal]:
        """Candlestick pattern recognition."""
        try:
            if len(data) < 10:
                return None
            
            # Get OHLC data
            open_prices = data['open'].values
            high_prices = data['high'].values
            low_prices = data['low'].values
            close_prices = data['close'].values
            
            current_price = close_prices[-1]
            
            # Bullish patterns
            hammer = talib.CDLHAMMER(open_prices, high_prices, low_prices, close_prices)
            engulfing = talib.CDLENGULFING(open_prices, high_prices, low_prices, close_prices)
            morning_star = talib.CDLMORNINGSTAR(open_prices, high_prices, low_prices, close_prices)
            
            # Bearish patterns
            shooting_star = talib.CDLSHOOTINGSTAR(open_prices, high_prices, low_prices, close_prices)
            evening_star = talib.CDLEVENINGSTAR(open_prices, high_prices, low_prices, close_prices)
            
            # Check for bullish patterns
            if hammer[-1] > 0 or engulfing[-1] > 0 or morning_star[-1] > 0:
                pattern_name = "Hammer" if hammer[-1] > 0 else ("Engulfing" if engulfing[-1] > 0 else "Morning Star")
                confidence = 75
                target = current_price * 1.02
                stop_loss = current_price * 0.985
                
                return StrategySignal(
                    strategy_type=StrategyType.CANDLESTICK,
                    signal='BUY',
                    confidence=confidence,
                    strength=1.0,
                    reason=f"Bullish {pattern_name} pattern",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(target - current_price) / (current_price - stop_loss),
                    timeframe='15m'
                )
            
            # Check for bearish patterns
            elif shooting_star[-1] < 0 or evening_star[-1] < 0:
                pattern_name = "Shooting Star" if shooting_star[-1] < 0 else "Evening Star"
                confidence = 75
                target = current_price * 0.98
                stop_loss = current_price * 1.015
                
                return StrategySignal(
                    strategy_type=StrategyType.CANDLESTICK,
                    signal='SELL',
                    confidence=confidence,
                    strength=1.0,
                    reason=f"Bearish {pattern_name} pattern",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(current_price - target) / (stop_loss - current_price),
                    timeframe='15m'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error in candlestick strategy: {e}")
            return None
    
    def trend_following_strategy(self, data: pd.DataFrame, symbol: str) -> Optional[StrategySignal]:
        """SuperTrend + EMA trend following strategy."""
        try:
            if len(data) < 50:
                return None
            
            # Calculate EMAs
            ema_20 = talib.EMA(data['close'].values, timeperiod=20)
            ema_50 = talib.EMA(data['close'].values, timeperiod=50)
            
            current_price = data['close'].iloc[-1]
            current_ema_20 = ema_20[-1]
            current_ema_50 = ema_50[-1]
            
            # Trend conditions
            strong_uptrend = current_price > current_ema_20 > current_ema_50
            strong_downtrend = current_price < current_ema_20 < current_ema_50
            
            # ATR for dynamic stops
            atr = talib.ATR(data['high'].values, data['low'].values, data['close'].values, timeperiod=14)
            current_atr = atr[-1]
            
            if strong_uptrend:
                confidence = 70
                target = current_price + (current_atr * 1.5)
                stop_loss = current_ema_20 - (current_atr * 0.5)
                
                return StrategySignal(
                    strategy_type=StrategyType.TREND_FOLLOWING,
                    signal='BUY',
                    confidence=confidence,
                    strength=(current_price - current_ema_50) / current_ema_50,
                    reason=f"Strong uptrend: Price > EMA20 > EMA50",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(target - current_price) / (current_price - stop_loss),
                    timeframe='15m'
                )
            
            elif strong_downtrend:
                confidence = 70
                target = current_price - (current_atr * 1.5)
                stop_loss = current_ema_20 + (current_atr * 0.5)
                
                return StrategySignal(
                    strategy_type=StrategyType.TREND_FOLLOWING,
                    signal='SELL',
                    confidence=confidence,
                    strength=(current_ema_50 - current_price) / current_ema_50,
                    reason=f"Strong downtrend: Price < EMA20 < EMA50",
                    entry_price=current_price,
                    target_price=target,
                    stop_loss=stop_loss,
                    risk_reward_ratio=(current_price - target) / (stop_loss - current_price),
                    timeframe='15m'
                )
            
            return None

        except Exception as e:
            logger.error(f"Error in trend following strategy: {e}")
            return None

    def combine_signals(self, signals: List[StrategySignal]) -> Optional[StrategySignal]:
        """Combine multiple strategy signals into one high-confidence signal."""
        try:
            if not signals:
                return None

            # Group signals by direction
            buy_signals = [s for s in signals if s.signal == 'BUY']
            sell_signals = [s for s in signals if s.signal == 'SELL']

            # Require minimum confirmation strategies
            if len(buy_signals) < self.min_confirmation_strategies and len(sell_signals) < self.min_confirmation_strategies:
                return None

            # Choose dominant direction
            if len(buy_signals) > len(sell_signals):
                dominant_signals = buy_signals
                signal_direction = 'BUY'
            elif len(sell_signals) > len(buy_signals):
                dominant_signals = sell_signals
                signal_direction = 'SELL'
            else:
                return None  # No clear consensus

            # Calculate weighted confidence
            total_weight = sum(self.strategy_weights[s.strategy_type] for s in dominant_signals)
            weighted_confidence = sum(s.confidence * self.strategy_weights[s.strategy_type]
                                    for s in dominant_signals) / total_weight

            # Check minimum confidence threshold
            if weighted_confidence < self.min_overall_confidence:
                return None

            # Calculate combined risk/reward
            avg_risk_reward = np.mean([s.risk_reward_ratio for s in dominant_signals
                                     if s.risk_reward_ratio > 0])

            # Check minimum risk/reward threshold
            if avg_risk_reward < self.min_risk_reward_ratio:
                return None

            # Create combined signal
            avg_entry = np.mean([s.entry_price for s in dominant_signals])
            avg_target = np.mean([s.target_price for s in dominant_signals])
            avg_stop = np.mean([s.stop_loss for s in dominant_signals])

            # Combine reasons
            strategy_names = [s.strategy_type.value for s in dominant_signals]
            combined_reason = f"Multi-strategy confirmation: {', '.join(strategy_names)}"

            return StrategySignal(
                strategy_type=StrategyType.MOMENTUM,  # Use as default
                signal=signal_direction,
                confidence=weighted_confidence,
                strength=np.mean([s.strength for s in dominant_signals]),
                reason=combined_reason,
                entry_price=avg_entry,
                target_price=avg_target,
                stop_loss=avg_stop,
                risk_reward_ratio=avg_risk_reward,
                timeframe='15m'
            )

        except Exception as e:
            logger.error(f"Error combining signals: {e}")
            return None

    def update_strategy_performance(self, strategy_type: StrategyType, pnl: float):
        """Update strategy performance for adaptive weighting."""
        try:
            if pnl > 0:
                self.strategy_performance[strategy_type]['wins'] += 1
            else:
                self.strategy_performance[strategy_type]['losses'] += 1

            self.strategy_performance[strategy_type]['total_pnl'] += pnl

            # Adaptive weight adjustment
            self._adjust_strategy_weights()

        except Exception as e:
            logger.error(f"Error updating strategy performance: {e}")

    def _adjust_strategy_weights(self):
        """Adjust strategy weights based on performance."""
        try:
            for strategy_type in StrategyType:
                perf = self.strategy_performance[strategy_type]
                total_trades = perf['wins'] + perf['losses']

                if total_trades >= 10:  # Minimum trades for adjustment
                    win_rate = perf['wins'] / total_trades
                    avg_pnl = perf['total_pnl'] / total_trades

                    # Adjust weight based on performance
                    if win_rate > 0.6 and avg_pnl > 0:
                        self.strategy_weights[strategy_type] *= 1.1  # Increase weight
                    elif win_rate < 0.4 or avg_pnl < 0:
                        self.strategy_weights[strategy_type] *= 0.9  # Decrease weight

                    # Normalize weights
                    total_weight = sum(self.strategy_weights.values())
                    for st in self.strategy_weights:
                        self.strategy_weights[st] /= total_weight

            logger.info(f"Strategy weights adjusted: {self.strategy_weights}")

        except Exception as e:
            logger.error(f"Error adjusting strategy weights: {e}")

    def get_performance_summary(self) -> Dict:
        """Get performance summary for all strategies."""
        summary = {}

        for strategy_type, perf in self.strategy_performance.items():
            total_trades = perf['wins'] + perf['losses']
            win_rate = perf['wins'] / total_trades if total_trades > 0 else 0
            avg_pnl = perf['total_pnl'] / total_trades if total_trades > 0 else 0

            summary[strategy_type.value] = {
                'total_trades': total_trades,
                'wins': perf['wins'],
                'losses': perf['losses'],
                'win_rate': win_rate * 100,
                'avg_pnl': avg_pnl,
                'current_weight': self.strategy_weights[strategy_type]
            }

        return summary
