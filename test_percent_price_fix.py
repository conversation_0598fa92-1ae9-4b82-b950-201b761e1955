#!/usr/bin/env python3
"""
Test PERCENT_PRICE Filter Fix
Tests the price validation improvements.
"""

import sys
import os
sys.path.append('src')

def test_price_validation():
    """Test the new price validation system."""
    print("🔧 TESTING PERCENT_PRICE FIX")
    print("=" * 40)
    
    try:
        from binance_client import BinanceClient
        
        # Initialize client
        print("Initializing Binance client...")
        client = BinanceClient(testnet=True)
        print("✅ BinanceClient initialized")
        
        # Test symbols that might have PERCENT_PRICE issues
        test_symbols = ['DOGEUSDT', 'XLMUSDT', 'TRXUSDT', 'ADAUSDT']
        
        for symbol in test_symbols:
            print(f"\n🔍 Testing price validation for {symbol}:")
            
            try:
                # Test current price
                current_price = client.get_current_price(symbol)
                if current_price:
                    print(f"   💰 Current price: ${current_price:.6f}")
                else:
                    print(f"   ❌ Could not get current price")
                    continue
                
                # Test price validation
                is_valid = client._validate_order_price(symbol, 'BUY')
                if is_valid:
                    print(f"   ✅ Price validation PASSED - safe to trade")
                else:
                    print(f"   ⚠️  Price validation FAILED - too volatile")
                
            except Exception as e:
                print(f"   ❌ Error testing {symbol}: {e}")
        
        print(f"\n🎯 SUMMARY:")
        print(f"Price validation should now prevent PERCENT_PRICE errors")
        print(f"Bot will skip volatile symbols and retry in next cycle")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run the test."""
    print("🤖 PERCENT_PRICE FILTER FIX TEST")
    print("Testing improvements to handle volatile market conditions")
    print("=" * 60)
    
    success = test_price_validation()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ TEST COMPLETED!")
        print("🚀 Your trading bot should now handle PERCENT_PRICE errors better")
        print("💡 Bot will skip volatile symbols and retry later")
        print("🔄 Restart the bot to apply fixes: python main.py")
    else:
        print("❌ TEST FAILED")
        print("There might still be issues with price validation")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test crashed: {e}")
