#!/usr/bin/env python3
"""
Termux-Optimized Trading Bot Launcher
Optimized for mobile devices with reduced resource usage.
"""

import os
import sys
import time
import signal
import logging
from datetime import datetime

def setup_termux_environment():
    """Setup Termux-specific environment."""
    print("🤖 Setting up Termux environment...")
    
    # Set environment variables for mobile optimization
    os.environ['PYTHONUNBUFFERED'] = '1'  # Unbuffered output
    os.environ['TERMUX_MODE'] = 'true'
    
    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    
    print("✅ Termux environment ready")

def check_dependencies():
    """Check if all required dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'pandas', 'numpy', 'requests', 'yaml', 'binance'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All dependencies available")
    return True

def setup_mobile_logging():
    """Setup mobile-optimized logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/termux_trading.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Limit log file size for mobile storage
    logger = logging.getLogger()
    for handler in logger.handlers:
        if isinstance(handler, logging.FileHandler):
            handler.setLevel(logging.INFO)

def signal_handler(signum, frame):
    """Handle termination signals gracefully."""
    print(f"\n🛑 Received signal {signum}. Shutting down gracefully...")
    sys.exit(0)

def display_mobile_banner():
    """Display mobile-optimized banner."""
    print("=" * 50)
    print("📱 TERMUX TRADING BOT")
    print("🚀 Enhanced Mobile Trading System")
    print("=" * 50)
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📱 Platform: Termux Android")
    print(f"🔧 Mode: Mobile Optimized")
    print("=" * 50)

def check_network_connectivity():
    """Check network connectivity."""
    import requests
    
    try:
        response = requests.get('https://api.binance.com/api/v3/ping', timeout=10)
        if response.status_code == 200:
            print("✅ Network connectivity: OK")
            return True
        else:
            print("❌ Network connectivity: Failed")
            return False
    except Exception as e:
        print(f"❌ Network error: {e}")
        return False

def run_mobile_trading_bot():
    """Run the trading bot with mobile optimizations."""
    try:
        # Import the main trading bot
        from main import TradingBot
        import yaml
        
        # Load mobile-optimized configuration
        config_file = 'termux_config.yaml' if os.path.exists('termux_config.yaml') else 'config/config.yaml'
        
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        # Override with mobile-specific settings
        config['trading_dashboard'] = {
            'enabled': True,
            'type': 'text',
            'update_interval': 60,
            'max_history': 20
        }
        
        # Initialize and run bot
        print("🚀 Starting mobile trading bot...")
        bot = TradingBot(config)
        
        # Run with mobile-friendly intervals
        print("📊 Bot running in mobile mode...")
        print("💡 Use Ctrl+C to stop gracefully")
        
        bot.run()
        
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"❌ Error running bot: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main Termux launcher."""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Display banner
    display_mobile_banner()
    
    # Setup environment
    setup_termux_environment()
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Please install missing dependencies first")
        sys.exit(1)
    
    # Check network
    if not check_network_connectivity():
        print("❌ Please check your internet connection")
        sys.exit(1)
    
    # Setup logging
    setup_mobile_logging()
    
    # Run the bot
    run_mobile_trading_bot()

if __name__ == "__main__":
    main()
