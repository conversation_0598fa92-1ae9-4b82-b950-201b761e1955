trading:
  mode: "testnet"  # TESTNET ONLY - Safe demo trading on Binance Futures Testnet (NO REAL MONEY)

  # Dynamic Altcoin Scanning (automatically finds all available altcoins)
  use_dynamic_scanning: true  # Set to false to use manual symbol list
  max_symbols: null  # null = ALL altcoins, number = limit to top N

  # Manual symbol list (used only if use_dynamic_scanning is false)
  symbols:
    # ALTCOINS ONLY - No BTC/ETH
    - "ADAUSDT"    # Cardano
    - "SOLUSDT"    # Solana
    - "DOTUSDT"    # Polkadot
    - "LINKUSDT"   # Chainlink
    - "ETHUSDT"    # Ethereum (available on testnet)
    - "AVAXUSDT"   # Avalanche
    - "ATOMUSDT"   # Cosmos
    - "NEARUSDT"   # Near Protocol
    - "BTCUSDT"    # Bitcoin (available on testnet)
    - "ALGOUSDT"   # Algorand
  base_currency: "USDT"

  # Altcoin filtering criteria
  exclude_symbols:
    - "BTCUSDT"    # Exclude Bitcoin
    - "ETHUSDT"    # Exclude Ethereum
    - "BNBUSDT"    # Exclude Binance Coin
    - "USDCUSDT"   # Exclude stablecoins
    - "BUSDUSDT"
    - "TUSDUSDT"
  
strategies:
  advanced_ai:
    enabled: true
    min_confidence: 65          # Lower threshold for altcoins (more opportunities)
    max_positions: 5            # More positions for altcoin diversification
    scan_interval: 3            # Scan more frequently for altcoins (every 15 min)
    min_volume_24h: 50000       # Very low volume requirement for ALL altcoins ($50K)
    top_n_tokens: null          # null = ALL altcoins, number = limit to top N

    # ALTCOIN-SPECIFIC SETTINGS (TRADE ALL ALTCOINS)
    altcoin_focus: true         # Enable altcoin-only mode
    min_price: 0.00001         # Very low minimum price (includes micro altcoins)
    max_price: 10000           # High maximum price (includes all altcoins)
    volatility_preference: "medium_high"  # Prefer more volatile altcoins

  sma_crossover:
    enabled: false              # Disabled in favor of AI strategy
    short_window: 10
    long_window: 30

  # Enhanced Futures Strategy (Context.txt compliant)
  enhanced_futures:
    enabled: true  # Main strategy following context.txt
    # EMA + RSI combo (context.txt requirements)
    ema_fast_period: 12
    ema_slow_period: 26
    rsi_period: 14
    rsi_oversold: 30  # RSI < 30 for long entry (CONTEXT.TXT COMPLIANT)
    rsi_overbought: 70  # RSI > 70 for short entry (CONTEXT.TXT COMPLIANT)
    # Volume confirmation - RELAXED FOR MORE TRADES
    volume_spike_threshold: 1.1  # Only 10% above average (was 50% - too restrictive)
    volume_lookback: 20
    # Market condition filters - EXTREMELY PERMISSIVE FOR MAXIMUM TRADING
    min_ema_spread: 0.0005  # 0.05% minimum EMA spread (VERY PERMISSIVE)
    volatility_threshold: 0.10  # 10% volatility threshold (VERY PERMISSIVE)
    # ATR-based volatility filter - EXTREMELY PERMISSIVE
    min_atr_pct: 0.001  # 0.1% minimum ATR percentage (EXTREMELY PERMISSIVE)
    atr_period: 14      # ATR calculation period
    # Time-based filters - DISABLED FOR 24/7 TRADING
    avoid_low_volume_hours: false  # Disabled to allow trading at all times
    low_volume_hours: []  # Empty list - no hours avoided
    # Cooldown and confirmation - RELAXED FOR MORE TRADES
    cooldown_minutes: 5   # Reduced cooldown for more frequent trades
    require_macd_confirmation: false  # Disabled MACD confirmation for more trades
    macd_fast: 12
    macd_slow: 26
    macd_signal: 9

  rsi_strategy:
    enabled: false
    rsi_period: 14
    oversold_threshold: 30
    overbought_threshold: 70

  scalping:
    enabled: true
    scalp_timeframe: "1m"
    quick_ema_period: 5
    slow_ema_period: 13
    rsi_period: 7
    bb_period: 10
    bb_std: 1.5
    min_profit_pct: 0.3      # 0.3% minimum profit target
    max_loss_pct: 0.15       # 0.15% maximum loss
    volume_spike_threshold: 1.2  # 20% volume spike required
    max_scalp_positions: 3   # Maximum 3 scalp positions at once
    scalp_position_size: 0.02  # 2% of portfolio per scalp
    min_hold_seconds: 30     # Minimum 30 seconds hold
    max_hold_seconds: 300    # Maximum 5 minutes hold

  # NEW ADVANCED STRATEGIES (KEEP INTERVAL-BASED PROFIT TAKING)
  moving_average_crossover:
    enabled: true
    short_window: 50    # Short-term MA period (50-day)
    long_window: 200    # Long-term MA period (200-day)

  bollinger_bands:
    enabled: true
    window: 20          # Moving average window (20-day)
    num_std: 2          # Number of standard deviations
    touch_threshold: 0.001  # 0.1% threshold for "touching" bands

  mean_reversion:
    enabled: true
    window: 20          # Moving average window (20-day)
    num_std: 1.5        # Number of standard deviations for bounds

  breakout:
    enabled: true
    window: 20          # Lookback window for high/low (20-day)
    breakout_threshold: 0.001  # 0.1% threshold for breakout confirmation

# ADVANCED TRADING ALGORITHM CONFIGURATION
advanced_signal_filter:
  enabled: true
  min_signal_strength: 65     # Minimum signal strength (0-100)
  min_confidence: 70          # Minimum signal confidence (0-100)
  max_risk_score: 35          # Maximum acceptable risk score (0-100, lower is better)
  min_overall_score: 75       # Minimum overall signal score (0-100)
  volume_multiplier_threshold: 1.5  # Volume surge threshold
  trend_strength_threshold: 0.6     # Trend alignment threshold

market_regime_detector:
  enabled: true
  trend_threshold: 0.02       # 2% threshold for trend detection
  volatility_threshold: 0.03  # 3% threshold for volatility classification
  volume_threshold: 1.5       # 1.5x threshold for volume analysis
  lookback_period: 50         # Periods to analyze for regime detection

scalping_fallback:
  enabled: false              # DISABLED in single position mode
  min_price_movement: 0.002   # 0.2% minimum price movement for scalping
  quick_profit_target: 0.005  # 0.5% profit target for scalping
  scalp_stop_loss: 0.003      # 0.3% stop loss for scalping
  max_hold_minutes: 10        # Maximum 10 minutes hold time
  volume_spike_threshold: 1.3 # 1.3x volume spike threshold
  rsi_oversold_scalp: 35      # RSI < 35 for scalping BUY (less strict)
  rsi_overbought_scalp: 65    # RSI > 65 for scalping SELL (less strict)

# CHART-BASED SINGLE POSITION TRADING CONFIGURATION
chart_analysis:
  enabled: true
  support_resistance_periods: 20    # Periods for S/R calculation
  breakout_threshold: 0.005         # 0.5% threshold for breakout detection
  volume_surge_threshold: 1.5       # 1.5x volume surge for confirmation
  min_confidence_threshold: 70      # Minimum 70% confidence for trade
  min_risk_reward_ratio: 1.5        # Minimum 1.5:1 risk/reward ratio
  ma_short: 10                      # Short-term moving average
  ma_medium: 20                     # Medium-term moving average
  ma_long: 50                       # Long-term moving average

single_position:
  enabled: true
  max_positions: 1                  # SINGLE POSITION ONLY
  min_time_between_trades: 2        # 2 minutes between trades
  profit_taking_intervals:
    5_minutes:
      min_profit: 0.0               # Take any profit > $0 after 5 minutes
    15_minutes:
      max_loss: 2.0                 # Close if loss < $2 after 15 minutes
    30_minutes:
      force_close: true             # Force close after 30 minutes regardless of P&L

# REAL-TIME TRADING DASHBOARD CONFIGURATION
trading_dashboard:
  enabled: true                     # Enable real-time dashboard
  update_interval: 30               # Update every 30 seconds
  max_history: 100                  # Keep 100 data points per symbol
  grid_cols: 4                      # 4 columns of mini charts
  grid_rows: 3                      # 3 rows (2 for charts, 1 for panels)
  auto_save: true                   # Auto-save dashboard images
  save_interval: 300                # Save every 5 minutes
  filters:
    min_volatility: 0.002           # Minimum BB width to display
    max_volatility: 0.05            # Maximum BB width to display
    min_volume_ratio: 1.0           # Minimum volume ratio to display
    show_errors_only: false         # Show only symbols with errors
    signal_types: ['BUY', 'SELL', 'HOLD']  # Signal types to display

# Enhanced Risk Management (Context.txt compliant)
risk_management:
  # STRICT POSITION LIMITS
  max_position_value_usd: 100  # MAX $100 per trade (HARD LIMIT)
  max_concurrent_positions: 3  # MAX 3 positions at once (HARD LIMIT)

  # INTERVAL-BASED PROFIT TAKING
  profit_check_interval_minutes: 5    # Check profits every 5 minutes
  min_profit_hold_minutes: 5          # Hold for 5 min before taking any profit
  breakeven_hold_minutes: 15          # After 15 min, close if loss < $2
  max_hold_minutes: 30                # Force close after 30 minutes
  max_acceptable_loss: 2.0            # Max $2 loss before force close

  # CONTEXT.TXT COMPLIANT SETTINGS
  max_position_size: 0.05  # 5% max position size (context.txt: 2-5%)
  min_position_size: 0.02  # 2% min position size
  stop_loss_percentage: 0.015  # 1.5% stop loss (context.txt requirement)
  take_profit_percentage: 0.0001  # Any profit (context.txt: exit as soon as profitable)
  max_daily_loss: 0.05  # 5% daily loss limit
  max_leverage: 5  # Max 5x leverage (context.txt requirement)
  leverage_adjustment: true
  max_daily_trades: 20
  trade_cooldown_minutes: 15  # 15-30 min cooldown (context.txt)
  max_consecutive_losses: 3
  market_pause_duration: 30  # 30 min pause for bad market conditions
  volatility_threshold: 0.05  # 5% volatility threshold
  high_volatility_reduction: 0.5  # 50% position size reduction in high volatility
  
timeframes:
  primary: "5m"  # 5-minute primary for futures trading
  secondary: "15m"
  long_term: "1h"

# Market Monitor Configuration
market_monitor:
  low_volume_threshold: 0.7  # 30% below average
  high_volatility_threshold: 0.05  # 5%
  sideways_threshold: 0.02  # 2% price range
  analysis_interval: 300  # 5 minutes
  short_period: 5
  medium_period: 20
  long_period: 50

# Enhanced Profit Management
profit_management:
  # Trailing stop functionality
  trailing_stop_enabled: true
  trailing_stop_percentage: 0.005  # 0.5%
  trailing_activation_profit: 0.01  # 1% profit to activate
  # Break-even logic
  break_even_enabled: true
  break_even_trigger_profit: 0.015  # 1.5% profit
  break_even_buffer: 0.002  # 0.2% buffer above entry
  # Dynamic profit targets
  dynamic_targets_enabled: true
  base_profit_target: 0.02  # 2% base target
  max_profit_target: 0.08  # 8% max target
  # Partial profit taking (disabled for context.txt compliance)
  partial_profit_enabled: false

# Comprehensive Trade Logging
trade_logging:
  log_directory: "logs"
  csv_filename: "trades.csv"
  db_filename: "trading_bot.db"
  performance_filename: "performance.json"
  log_to_csv: true
  log_to_db: true
  log_market_conditions: true
  # Alert system
  telegram_alerts: false  # Set to true and configure tokens to enable
  telegram_token: ""  # Set via environment variable TELEGRAM_TOKEN
  telegram_chat_id: ""  # Set via environment variable TELEGRAM_CHAT_ID
  email_alerts: false

# Backtesting Configuration
backtesting:
  initial_balance: 10000
  start_date: "2024-01-01"
  end_date: "2024-11-01"
  timeframe: "5m"
  test_symbols:
    - "ADAUSDT"
    - "DOTUSDT"
    - "LINKUSDT"
    - "SOLUSDT"
    - "AVAXUSDT"
    - "MATICUSDT"
    - "ALGOUSDT"
    - "ATOMUSDT"

logging:
  level: "INFO"
  file: "logs/trading_bot.log"

notifications:
  enabled: false
  webhook_url: ""
