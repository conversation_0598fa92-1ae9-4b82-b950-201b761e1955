"""
Test script to verify the bot follows context.txt trading rules exactly.
"""

import os
import sys
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from binance_client import BinanceClient
from strategies.enhanced_futures_strategy import EnhancedFuturesStrategy
from utils import load_config

def test_entry_conditions():
    """Test that entry conditions match context.txt exactly."""
    print("🎯 Testing CONTEXT.TXT Entry Conditions")
    print("=" * 60)
    
    try:
        client = BinanceClient(testnet=True)
        config = load_config('config/config.yaml')
        strategy_config = config.get('strategies', {}).get('enhanced_futures', {})
        
        strategy = EnhancedFuturesStrategy(client, strategy_config)
        
        print("📊 Entry Condition Requirements:")
        print("   BUY (Long): EMA Fast > EMA Slow AND RSI < 30")
        print("   SELL (Short): EMA Fast < EMA Slow AND RSI > 70")
        
        # Test with sample data
        test_cases = [
            {
                'name': 'Valid BUY signal',
                'ema_fast': 100.0,
                'ema_slow': 99.0,
                'rsi': 25.0,
                'expected': 'BUY'
            },
            {
                'name': 'Valid SELL signal', 
                'ema_fast': 99.0,
                'ema_slow': 100.0,
                'rsi': 75.0,
                'expected': 'SELL'
            },
            {
                'name': 'Invalid - EMA correct but RSI wrong for BUY',
                'ema_fast': 100.0,
                'ema_slow': 99.0,
                'rsi': 50.0,
                'expected': 'HOLD'
            },
            {
                'name': 'Invalid - RSI correct but EMA wrong for BUY',
                'ema_fast': 99.0,
                'ema_slow': 100.0,
                'rsi': 25.0,
                'expected': 'HOLD'
            }
        ]
        
        for test_case in test_cases:
            print(f"\n📊 {test_case['name']}:")
            print(f"   EMA Fast: {test_case['ema_fast']}, EMA Slow: {test_case['ema_slow']}")
            print(f"   RSI: {test_case['rsi']}")
            
            # Create mock data
            data = pd.DataFrame({
                'close': [100.0] * 50,
                'high': [101.0] * 50,
                'low': [99.0] * 50,
                'volume': [1000.0] * 50
            })
            
            # Add indicators
            data['EMA_fast'] = test_case['ema_fast']
            data['EMA_slow'] = test_case['ema_slow']
            data['RSI'] = test_case['rsi']
            
            # Test signal generation
            signal = strategy.generate_signal(data, 'TESTUSDT')
            
            print(f"   Generated: {signal}, Expected: {test_case['expected']}")
            
            if signal == test_case['expected']:
                print(f"   ✅ CORRECT - Follows context.txt rules")
            else:
                print(f"   ❌ INCORRECT - Does not follow context.txt rules")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing entry conditions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_profit_taking_rule():
    """Test immediate profit-taking rule from context.txt."""
    print("\n💰 Testing CONTEXT.TXT Profit-Taking Rule")
    print("=" * 60)
    
    print("📋 CONTEXT.TXT Rule: 'Close position as soon as PnL > 0'")
    
    # Test profit calculation scenarios
    test_scenarios = [
        {
            'name': 'Small profit - should close immediately',
            'entry_price': 1.0,
            'current_price': 1.001,  # 0.1% profit
            'side': 'BUY',
            'should_close': True
        },
        {
            'name': 'Large profit - should close immediately',
            'entry_price': 1.0,
            'current_price': 1.05,   # 5% profit
            'side': 'BUY',
            'should_close': True
        },
        {
            'name': 'Small loss - should hold',
            'entry_price': 1.0,
            'current_price': 0.999,  # 0.1% loss
            'side': 'BUY',
            'should_close': False
        },
        {
            'name': 'Break-even - should hold',
            'entry_price': 1.0,
            'current_price': 1.0,    # 0% change
            'side': 'BUY',
            'should_close': False
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📊 {scenario['name']}:")
        
        entry_price = scenario['entry_price']
        current_price = scenario['current_price']
        side = scenario['side']
        
        # Calculate profit (same logic as bot)
        if side == 'BUY':
            price_diff = current_price - entry_price
        else:
            price_diff = entry_price - current_price
        
        profit_pct = (price_diff / entry_price) * 100
        profit_usdt = price_diff * 100  # Assuming 100 USDT position
        
        print(f"   Entry: {entry_price}, Current: {current_price}")
        print(f"   Profit: {profit_pct:+.2f}% ({profit_usdt:+.2f} USDT)")
        
        should_close = profit_usdt > 0
        expected = scenario['should_close']
        
        print(f"   Bot decision: {'CLOSE' if should_close else 'HOLD'}")
        print(f"   Expected: {'CLOSE' if expected else 'HOLD'}")
        
        if should_close == expected:
            print(f"   ✅ CORRECT - Follows context.txt profit rule")
        else:
            print(f"   ❌ INCORRECT - Does not follow context.txt profit rule")
    
    return True

def test_risk_management():
    """Test risk management rules from context.txt."""
    print("\n🛡️ Testing CONTEXT.TXT Risk Management")
    print("=" * 60)
    
    try:
        config = load_config('config/config.yaml')
        risk_config = config.get('risk_management', {})
        
        print("📋 CONTEXT.TXT Risk Rules:")
        print("   • Max leverage: 5x")
        print("   • Position size: 2-5% of balance")
        print("   • Stop loss: -1.5%")
        print("   • Cooldown: 15-30 minutes")
        
        print(f"\n📊 Current Configuration:")
        print(f"   max_leverage: {risk_config.get('max_leverage', 'NOT SET')}")
        print(f"   max_position_size: {risk_config.get('max_position_size', 'NOT SET')}")
        print(f"   stop_loss_percentage: {risk_config.get('stop_loss_percentage', 'NOT SET')}")
        print(f"   trade_cooldown_minutes: {risk_config.get('trade_cooldown_minutes', 'NOT SET')}")
        
        # Verify compliance
        compliance_checks = [
            ('Max Leverage ≤ 5x', risk_config.get('max_leverage', 10) <= 5),
            ('Position Size ≤ 5%', risk_config.get('max_position_size', 0.1) <= 0.05),
            ('Stop Loss = 1.5%', risk_config.get('stop_loss_percentage', 0) == 0.015),
            ('Cooldown ≥ 15min', risk_config.get('trade_cooldown_minutes', 0) >= 15)
        ]
        
        all_compliant = True
        for check_name, is_compliant in compliance_checks:
            status = "✅ COMPLIANT" if is_compliant else "❌ NON-COMPLIANT"
            print(f"   {check_name}: {status}")
            if not is_compliant:
                all_compliant = False
        
        return all_compliant
        
    except Exception as e:
        print(f"❌ Error testing risk management: {e}")
        return False

def test_altcoin_focus():
    """Test that bot focuses on altcoins as per context.txt."""
    print("\n🪙 Testing CONTEXT.TXT Altcoin Focus")
    print("=" * 60)
    
    print("📋 CONTEXT.TXT Rule: Focus on altcoins, exclude BTC, ETH, USDT")
    
    # Test symbol filtering
    all_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOGEUSDT', 'LINKUSDT']
    excluded = {'BTCUSDT', 'ETHUSDT', 'BNBUSDT'}
    
    altcoin_symbols = [s for s in all_symbols if s not in excluded]
    
    print(f"   All symbols: {all_symbols}")
    print(f"   Excluded: {list(excluded)}")
    print(f"   Altcoins: {altcoin_symbols}")
    
    if 'BTCUSDT' not in altcoin_symbols and 'ETHUSDT' not in altcoin_symbols:
        print("   ✅ CORRECT - BTC and ETH excluded")
        return True
    else:
        print("   ❌ INCORRECT - BTC or ETH not excluded")
        return False

def main():
    """Run all context.txt compliance tests."""
    print("🧪 CONTEXT.TXT Compliance Test Suite")
    print("=" * 80)
    
    print("📖 Testing bot compliance with context.txt trading rules:")
    print("   • Entry: EMA + RSI combo")
    print("   • Exit: Immediate profit-taking (PnL > 0)")
    print("   • Risk: Max 5x leverage, 2-5% position size")
    print("   • Focus: Altcoins only")
    print("   • Cooldown: 15-30 minutes between trades")
    
    tests = [
        ("Entry Conditions", test_entry_conditions),
        ("Profit-Taking Rule", test_profit_taking_rule),
        ("Risk Management", test_risk_management),
        ("Altcoin Focus", test_altcoin_focus)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Compliance Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 FULL CONTEXT.TXT COMPLIANCE ACHIEVED!")
        print("\n✅ Bot follows all context.txt rules:")
        print("   ✅ EMA Fast > EMA Slow + RSI < 30 for BUY")
        print("   ✅ EMA Fast < EMA Slow + RSI > 70 for SELL")
        print("   ✅ Immediate profit-taking when PnL > 0")
        print("   ✅ Max 5x leverage enforcement")
        print("   ✅ 2-5% position sizing")
        print("   ✅ 15-minute cooldown periods")
        print("   ✅ Altcoin focus (excludes BTC/ETH)")
        print("   ✅ Market condition awareness")
        
        print("\n🚀 Ready for context.txt compliant trading!")
    else:
        print("⚠️  Some compliance issues found. Fix before trading.")
    
    return passed == total

if __name__ == "__main__":
    main()
