#!/usr/bin/env python3
"""
Test BaseAsset Fix
Tests that the 'baseAsset' KeyError is fixed.
"""

import sys
import os
sys.path.append('src')

def test_altcoin_scanner_fix():
    """Test that altcoin scanner no longer has baseAsset errors."""
    print("🔧 TESTING BASEASSET FIX")
    print("=" * 40)
    
    try:
        from altcoin_scanner import AltcoinScanner
        from binance_client import BinanceClient
        
        # Initialize
        print("Initializing Binance client...")
        client = BinanceClient(testnet=True)
        print("✅ BinanceClient initialized")
        
        scanner_config = {
            'min_volume_24h': 10000,
            'min_price': 0.000001,
            'max_price': 100000,
            'quote_asset': 'USDT',
            'scan_interval_hours': 1
        }
        
        print("Initializing AltcoinScanner...")
        scanner = AltcoinScanner(client, scanner_config)
        print("✅ AltcoinScanner initialized")
        
        # Test scanning - this should not crash with baseAsset error
        print("🔍 Testing altcoin scanning...")
        try:
            altcoins = scanner.scan_altcoins(force_rescan=True)
            print(f"✅ Scanning completed successfully!")
            print(f"📊 Found {len(altcoins)} qualifying altcoins")
            
            if len(altcoins) > 0:
                print(f"Sample altcoins: {altcoins[:10]}")
                return True
            else:
                print("⚠️  No altcoins found (might be due to strict filters)")
                return True  # Still success if no crash
                
        except KeyError as e:
            if 'baseAsset' in str(e):
                print(f"❌ baseAsset error still exists: {e}")
                return False
            else:
                print(f"❌ Other KeyError: {e}")
                return False
        except Exception as e:
            print(f"❌ Other error during scanning: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_symbol_processing():
    """Test symbol processing logic."""
    print(f"\n🔧 TESTING SYMBOL PROCESSING")
    print("=" * 40)
    
    # Test symbol parsing
    test_symbols = [
        {'symbol': 'BTCUSDT', 'status': 'TRADING'},
        {'symbol': 'ETHUSDT', 'status': 'TRADING'},
        {'symbol': 'DOGEUSDT', 'status': 'TRADING'},
        {'symbol': 'BTCBUSD', 'status': 'TRADING'},  # Should be skipped (not USDT)
        {'symbol': 'XLMUSDT'},  # Missing status (should default)
    ]
    
    processed = []
    for symbol_info in test_symbols:
        try:
            symbol = symbol_info.get('symbol', '')
            if not symbol:
                continue
            
            # Extract base and quote assets from symbol name
            if symbol.endswith('USDT'):
                base_asset = symbol[:-4]  # Remove 'USDT' suffix
                quote_asset = 'USDT'
            else:
                # Skip non-USDT pairs
                continue
                
            status = symbol_info.get('status', 'TRADING')
            
            processed.append({
                'symbol': symbol,
                'base_asset': base_asset,
                'quote_asset': quote_asset,
                'status': status
            })
            
        except Exception as e:
            print(f"❌ Error processing {symbol_info}: {e}")
            return False
    
    print(f"✅ Processed {len(processed)} symbols successfully")
    for item in processed:
        print(f"   {item['symbol']}: {item['base_asset']}/{item['quote_asset']} ({item['status']})")
    
    return True

def main():
    """Run all tests."""
    print("🤖 BASEASSET FIX TEST")
    print("Testing that 'baseAsset' KeyError is resolved")
    print("=" * 60)
    
    # Test 1: Symbol processing logic
    test1_passed = test_symbol_processing()
    
    # Test 2: Altcoin scanner
    test2_passed = test_altcoin_scanner_fix()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS")
    print("=" * 60)
    
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED!")
        print("🚀 The 'baseAsset' KeyError should be fixed")
        print("💡 Bot should now start successfully")
        print("🔄 Restart the bot: python main.py")
    else:
        print("❌ SOME TESTS FAILED")
        print("There might still be issues with symbol processing")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
    except Exception as e:
        print(f"\n❌ Test crashed: {e}")
