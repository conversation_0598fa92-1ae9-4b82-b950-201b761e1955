# 🚀 Quick Start Guide - Crypto Trading Bot

## 📋 Prerequisites

1. **Python 3.8+** installed on your system
2. **Internet connection** for API access
3. **Binance account** (for live trading) or just use paper trading

## ⚡ Super Quick Setup (2 minutes)

### Windows Users:
```bash
# Double-click this file or run in Command Prompt:
install.bat
```

### Linux/Mac Users:
```bash
# Make executable and run:
chmod +x install.sh
./install.sh
```

### Manual Setup:
```bash
# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env

# Run tests
python test_bot.py
```

## 🧪 Testing the Bot (SAFE - No Real Money)

### 1. Run the Test Suite
```bash
python test_bot.py
```
**Expected**: All tests should pass ✅

### 2. Test Paper Trading
Edit `.env` file:
```
TRADING_MODE=paper
INITIAL_BALANCE=10000
BINANCE_API_KEY=test_key
BINANCE_SECRET_KEY=test_secret
```

Run the bot:
```bash
python main.py
```

### 3. Monitor the Bot
In another terminal:
```bash
python monitor.py
```

### 4. Run a Backtest
```bash
python cli.py backtest sma_crossover BTCUSDT 2023-12-01
```

## 🔧 Configuration for Real Trading

### 1. Get Binance API Keys
- Go to [Binance API Management](https://www.binance.com/en/my/settings/api-management)
- Create new API key
- **For testing**: Only enable "Enable Reading"
- **For trading**: Enable "Enable Trading" (be careful!)

### 2. Update .env File
```
BINANCE_API_KEY=your_real_api_key_here
BINANCE_SECRET_KEY=your_real_secret_key_here
TRADING_MODE=paper  # Keep as 'paper' for testing
```

### 3. Test API Connection
```bash
python -c "
import sys, os
sys.path.append('src')
from binance_client import BinanceClient
client = BinanceClient()
price = client.get_current_price('BTCUSDT')
print(f'BTC Price: ${price:,.2f}')
"
```

## 📊 Understanding the Output

### When Running the Bot:
```
2024-01-15 10:30:00 - INFO - Trading Bot initialized in paper mode
2024-01-15 10:30:00 - INFO - Monitoring symbols: BTCUSDT, ETHUSDT
2024-01-15 10:30:00 - INFO - Active strategies: SMA_Crossover
2024-01-15 10:30:00 - INFO - Running trading cycle
2024-01-15 10:30:05 - INFO - Signal: BUY for BTCUSDT at 43250.50
2024-01-15 10:30:05 - INFO - PAPER TRADE: BUY 0.023148 BTCUSDT at 43250.50
```

### Portfolio Status:
```
Portfolio Status:
  Balance: 10000.00 USDT
  Total P&L: 0.0000
  Total Return: 0.00%
  Total Trades: 0
  Win Rate: 0.00%
  Open Positions: 1
    BTCUSDT: BUY 0.023148 @ 43250.5000 (P&L: 12.3456)
```

## 🎯 Testing Checklist

- [ ] ✅ All tests pass (`python test_bot.py`)
- [ ] ✅ Bot starts without errors
- [ ] ✅ API connection works (if using real keys)
- [ ] ✅ Strategies generate signals
- [ ] ✅ Paper trades execute
- [ ] ✅ Monitor shows data
- [ ] ✅ No critical errors in logs

## 🔍 Common Test Scenarios

### Test 1: Strategy Signal Generation
```bash
# Should generate BUY/SELL/HOLD signals
python -c "
import sys, os, pandas as pd, numpy as np
sys.path.append('src')
from strategies.sma_crossover import SMACrossoverStrategy

# Create trending data
dates = pd.date_range('2023-01-01', periods=50, freq='1H')
prices = 100 + np.cumsum(np.random.randn(50) * 0.5)
data = pd.DataFrame({
    'open': prices, 'high': prices*1.01, 'low': prices*0.99,
    'close': prices, 'volume': np.random.randint(1000, 10000, 50)
}, index=dates)

strategy = SMACrossoverStrategy({'short_window': 5, 'long_window': 15})
signal = strategy.generate_signal(data, 'BTCUSDT')
print(f'Generated signal: {signal}')
"
```

### Test 2: Risk Management
```bash
# Should calculate appropriate position sizes
python -c "
import sys, os
sys.path.append('src')
from risk_management import RiskManager

config = {'max_position_size': 0.1, 'stop_loss_percentage': 0.02}
risk_manager = RiskManager(config)
size = risk_manager.calculate_position_size(10000, 50000)
print(f'Position size for $10k portfolio at $50k BTC: {size:.6f} BTC')
print(f'Position value: ${size * 50000:.2f}')
"
```

### Test 3: Portfolio Tracking
```bash
# Should track positions and P&L
python -c "
import sys, os
sys.path.append('src')
from portfolio import Portfolio

portfolio = Portfolio(10000, 'USDT')
portfolio.add_position('BTCUSDT', 'BUY', 0.1, 50000)
portfolio.update_position_pnl('BTCUSDT', 51000)
print(f'Positions: {len(portfolio.positions)}')
print(f'Unrealized P&L: {portfolio.positions[\"BTCUSDT\"][\"unrealized_pnl\"]:.2f}')
"
```

## 🚨 Safety First!

### Before Live Trading:
1. ✅ Run paper trading for at least 24-48 hours
2. ✅ Analyze the results and understand the strategy
3. ✅ Start with very small amounts (like $50-100)
4. ✅ Monitor closely for the first few days
5. ✅ Have stop-loss mechanisms in place

### Red Flags (Stop Immediately):
- ❌ Unexpected large trades
- ❌ API errors or connection issues
- ❌ Strategies behaving erratically
- ❌ Rapid losses beyond expected limits

## 📞 Getting Help

### Check Logs:
```bash
# View recent logs
tail -f logs/trading_bot.log
```

### Common Issues:
1. **"Module not found"** → Run `pip install -r requirements.txt`
2. **"API connection failed"** → Check internet and API keys
3. **"Config error"** → Verify `config/config.yaml` syntax
4. **"Permission denied"** → Check file permissions

### Debug Mode:
Edit `config/config.yaml`:
```yaml
logging:
  level: "DEBUG"  # More detailed logs
```

## 🎉 Success Indicators

You'll know the bot is working correctly when:
- ✅ Tests pass consistently
- ✅ Bot runs without crashes
- ✅ Strategies generate logical signals
- ✅ Paper trades execute as expected
- ✅ Monitor shows real-time data
- ✅ Logs show normal activity

**Remember**: Start with paper trading, test thoroughly, and only use small amounts for initial live trading!
