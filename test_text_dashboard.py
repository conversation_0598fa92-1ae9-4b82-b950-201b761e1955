"""
Test Text-Based Dashboard (No matplotlib required)
"""

import sys
import os
import time
import random
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_mock_data():
    """Create mock trading data."""
    symbols = ['SOLUSDT', 'ADAUSDT', 'XRPUSDT', 'SUIUSDT', 'LINKUSDT', 'BCHUSDT', 'LTCUSDT', 'XLMUSDT']
    
    mock_data = {}
    
    for symbol in symbols:
        # Generate realistic data
        base_price = random.uniform(0.1, 100)
        bb_position = random.uniform(10, 90)
        bb_width = random.uniform(0.002, 0.05)
        confidence = random.uniform(30, 95)
        volume_ratio = random.uniform(0.5, 3.0)
        
        # Signal generation with weights
        signals = ['BUY', 'SELL', 'HOLD']
        signal_weights = [0.25, 0.25, 0.5]  # 25% BUY, 25% SELL, 50% HOLD
        signal = random.choices(signals, weights=signal_weights)[0]
        
        # Error simulation
        has_error = random.random() < 0.15  # 15% chance of error
        error_msg = random.choice([
            "Connection timeout",
            "Data fetch failed", 
            "Analysis error",
            "Invalid response"
        ]) if has_error else None
        
        mock_data[symbol] = {
            'price': base_price,
            'signal': signal,
            'confidence': confidence,
            'bb_position': bb_position,
            'bb_width': bb_width,
            'volume_ratio': volume_ratio,
            'error': error_msg
        }
    
    return mock_data

def test_text_dashboard():
    """Test the text dashboard functionality."""
    print("📊 Testing Text-Based Trading Dashboard")
    print("=" * 60)
    
    try:
        from text_dashboard import TextDashboard
        
        # Initialize dashboard
        config = {
            'max_history': 20,
            'display_symbols': 8
        }
        
        dashboard = TextDashboard(config)
        
        print("✅ Text dashboard created successfully")
        print("📊 Simulating trading data updates...")
        
        # Generate and update mock data
        mock_data = create_mock_data()
        
        for symbol, data in mock_data.items():
            dashboard.update_symbol_data(symbol, data)
            print(f"   📈 Updated {symbol}: {data['signal']} @ ${data['price']:.6f}")
        
        # Add some errors
        dashboard.log_error('TESTUSDT', 'Connection Error', 'Mock connection timeout')
        dashboard.log_error('MOCKUSDT', 'Analysis Error', 'Mock analysis failure')
        
        # Add some trades
        dashboard.log_trade('SOLUSDT', 'BUY', 1.234567, 100.0, 'Chart breakout pattern')
        dashboard.log_trade('ADAUSDT', 'SELL', 0.567890, 200.0, 'Resistance rejection')
        
        # Update scan statistics
        scan_stats = {
            'total_symbols': len(mock_data),
            'successful_scans': len(mock_data) - 1,
            'failed_scans': 1,
            'opportunities_found': 3,
            'success_rate': 87.5
        }
        dashboard.update_scan_stats(scan_stats)
        
        print("✅ Mock data loaded successfully")
        print(f"   📊 {len(mock_data)} symbols updated")
        print(f"   🚨 {len(dashboard.error_log)} errors logged")
        print(f"   💰 {len(dashboard.trade_history)} trades logged")
        
        return dashboard
        
    except Exception as e:
        print(f"❌ Error testing text dashboard: {e}")
        import traceback
        traceback.print_exc()
        return None

def demo_live_updates(dashboard):
    """Demo live dashboard updates."""
    print("\n🚀 Live Text Dashboard Demo")
    print("=" * 60)
    
    if dashboard is None:
        print("❌ No dashboard to demo")
        return
    
    print("📊 Starting live text dashboard demo...")
    print("⏱️  Demo will run for 30 seconds with updates every 3 seconds")
    print("🔄 Press Ctrl+C to stop early")
    
    try:
        for update_count in range(10):  # 10 updates over 30 seconds
            print(f"\n📈 Update {update_count + 1}/10...")
            
            # Update some symbols with new data
            symbols = list(dashboard.symbol_data.keys())
            
            for symbol in symbols[:4]:  # Update first 4 symbols
                # Simulate price movement
                if dashboard.symbol_data[symbol]['prices']:
                    last_price = dashboard.symbol_data[symbol]['prices'][-1]
                    price_change = random.uniform(-0.05, 0.05) * last_price
                    new_price = max(last_price + price_change, 0.001)
                else:
                    new_price = random.uniform(0.1, 100)
                
                # Generate new signal occasionally
                if random.random() < 0.3:  # 30% chance of signal change
                    new_signal = random.choice(['BUY', 'SELL', 'HOLD'])
                else:
                    new_signal = dashboard.symbol_data[symbol]['signals'][-1] if dashboard.symbol_data[symbol]['signals'] else 'HOLD'
                
                new_data = {
                    'price': new_price,
                    'signal': new_signal,
                    'confidence': random.uniform(50, 95),
                    'bb_position': random.uniform(20, 80),
                    'bb_width': random.uniform(0.005, 0.03),
                    'volume_ratio': random.uniform(0.8, 2.5),
                    'error': None
                }
                
                dashboard.update_symbol_data(symbol, new_data)
            
            # Occasionally add an error
            if random.random() < 0.2:  # 20% chance
                error_symbol = random.choice(symbols)
                dashboard.log_error(error_symbol, 'Live Error', f'Simulated error at update {update_count + 1}')
            
            # Display updated dashboard
            dashboard.display_dashboard()
            
            # Wait before next update
            time.sleep(3)
        
        print("\n✅ Live dashboard demo completed successfully")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")

def test_dashboard_export(dashboard):
    """Test dashboard export functionality."""
    print("\n💾 Testing Dashboard Export")
    print("=" * 40)
    
    if dashboard is None:
        print("❌ No dashboard to export")
        return
    
    try:
        # Test text dashboard save
        dashboard.save_dashboard("test_text_dashboard.txt")
        print("✅ Dashboard saved as text file")
        
        # Test data export
        dashboard.export_data("test_dashboard_export.txt")
        print("✅ Dashboard data exported")
        
        print("📄 Files created:")
        print("   📊 test_text_dashboard.txt - Full dashboard snapshot")
        print("   📄 test_dashboard_export.txt - Data summary")
        
        return True
        
    except Exception as e:
        print(f"❌ Export error: {e}")
        return False

def show_installation_guide():
    """Show installation guide for visual dashboard."""
    print("💡 Visual Dashboard Installation Guide")
    print("=" * 60)
    
    print("📊 CURRENT STATUS:")
    print("   ✅ Text-based dashboard: Working (no dependencies)")
    print("   ❌ Visual dashboard: Requires matplotlib")
    print()
    
    print("🔧 TO ENABLE VISUAL DASHBOARD:")
    print("   1. Install required packages:")
    print("      pip install matplotlib seaborn")
    print()
    print("   2. Restart the trading bot:")
    print("      python main.py")
    print()
    print("   3. Visual dashboard will automatically activate")
    print()
    
    print("📊 DASHBOARD COMPARISON:")
    print("   Text Dashboard:")
    print("   • ✅ No dependencies required")
    print("   • ✅ Works in any terminal")
    print("   • ✅ Low resource usage")
    print("   • ✅ Real-time updates")
    print("   • ❌ No visual charts")
    print()
    print("   Visual Dashboard:")
    print("   • ✅ Interactive charts")
    print("   • ✅ Bollinger Bands visualization")
    print("   • ✅ Signal markers")
    print("   • ✅ Error panels")
    print("   • ❌ Requires matplotlib/seaborn")

def main():
    """Run text dashboard tests."""
    print("🧪 Text-Based Trading Dashboard Test Suite")
    print("=" * 80)
    
    show_installation_guide()
    
    # Test text dashboard
    dashboard = test_text_dashboard()
    
    if dashboard:
        print("\n📊 Displaying current dashboard state:")
        dashboard.display_dashboard()
        
        # Test export
        export_success = test_dashboard_export(dashboard)
        
        # Ask for live demo
        print("\n🚀 Would you like to see a live dashboard demo?")
        response = input("Enter 'y' for live demo or any other key to skip: ").lower().strip()
        
        if response == 'y':
            demo_live_updates(dashboard)
        
        print("\n🎉 TEXT DASHBOARD READY!")
        print("✅ The trading bot will now display a real-time text dashboard")
        print("💡 Install matplotlib for visual charts: pip install matplotlib seaborn")
        print("🚀 Run the bot: python main.py")
    else:
        print("❌ Text dashboard test failed")

if __name__ == "__main__":
    main()
