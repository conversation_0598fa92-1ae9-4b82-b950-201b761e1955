"""
Command Line Interface for the trading bot.
"""

import argparse
import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import load_config, save_config
from backtest import Backtester

def setup_bot():
    """Setup the trading bot."""
    print("Setting up Crypto Trading Bot...")
    
    # Run setup script
    try:
        import setup
        setup.main()
    except Exception as e:
        print(f"Setup failed: {e}")
        return False
    
    return True

def run_bot(config_path='config/config.yaml'):
    """Run the trading bot."""
    print("Starting trading bot...")
    
    try:
        from main import TradingBot
        bot = TradingBot(config_path)
        bot.start()
    except Exception as e:
        print(f"Failed to start bot: {e}")
        return False
    
    return True

def run_backtest(strategy, symbol, start_date, end_date=None, balance=10000):
    """Run backtest for a strategy."""
    print(f"Running backtest for {strategy} on {symbol}")
    
    try:
        backtester = Backtester()
        results = backtester.run_backtest(
            strategy_name=strategy,
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            initial_balance=balance
        )
        
        if results:
            print("\nBacktest Results:")
            print(f"Strategy: {results['strategy']}")
            print(f"Symbol: {results['symbol']}")
            print(f"Period: {results['start_date']} to {results['end_date']}")
            print(f"Initial Balance: ${results['initial_balance']:,.2f}")
            print(f"Final Balance: ${results['final_balance']:,.2f}")
            print(f"Total Return: {results['total_return']:.2f}%")
            print(f"Max Drawdown: {results['max_drawdown']:.2f}%")
            print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
            print(f"Total Trades: {results['total_trades']}")
            print(f"Win Rate: {results['win_rate']:.2%}")
            
            # Save results
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"backtest_results/{strategy}_{symbol}_{timestamp}.png"
            backtester.plot_results(results, filename)
            
        return True
        
    except Exception as e:
        print(f"Backtest failed: {e}")
        return False

def monitor_bot():
    """Monitor the trading bot."""
    print("Starting bot monitor...")
    
    try:
        from monitor import TradingBotMonitor
        monitor = TradingBotMonitor()
        monitor.run()
    except Exception as e:
        print(f"Monitor failed: {e}")
        return False
    
    return True

def configure_bot():
    """Interactive configuration of the bot."""
    print("Configuring Trading Bot...")
    
    config = load_config('config/config.yaml')
    if not config:
        print("Error loading configuration file")
        return False
    
    print("\nCurrent Configuration:")
    print(f"Trading Mode: {config.get('trading', {}).get('mode', 'paper')}")
    print(f"Symbols: {', '.join(config.get('trading', {}).get('symbols', []))}")
    print(f"Base Currency: {config.get('trading', {}).get('base_currency', 'USDT')}")
    
    # Ask for changes
    print("\nConfiguration Options:")
    print("1. Change trading mode")
    print("2. Modify symbols")
    print("3. Configure strategies")
    print("4. Adjust risk management")
    print("5. Save and exit")
    
    while True:
        choice = input("\nSelect option (1-5): ").strip()
        
        if choice == '1':
            mode = input("Enter trading mode (paper/live): ").strip().lower()
            if mode in ['paper', 'live']:
                config['trading']['mode'] = mode
                print(f"Trading mode set to: {mode}")
            else:
                print("Invalid mode. Use 'paper' or 'live'")
        
        elif choice == '2':
            symbols_input = input("Enter symbols (comma-separated, e.g., BTCUSDT,ETHUSDT): ").strip()
            if symbols_input:
                symbols = [s.strip().upper() for s in symbols_input.split(',')]
                config['trading']['symbols'] = symbols
                print(f"Symbols set to: {', '.join(symbols)}")
        
        elif choice == '3':
            print("\nStrategy Configuration:")
            print("SMA Crossover Strategy:")
            sma_enabled = input("Enable SMA strategy? (y/n): ").strip().lower() == 'y'
            config['strategies']['sma_crossover']['enabled'] = sma_enabled
            
            if sma_enabled:
                short_window = input("Short window (default 10): ").strip()
                long_window = input("Long window (default 30): ").strip()
                
                if short_window.isdigit():
                    config['strategies']['sma_crossover']['short_window'] = int(short_window)
                if long_window.isdigit():
                    config['strategies']['sma_crossover']['long_window'] = int(long_window)
            
            print("\nRSI Strategy:")
            rsi_enabled = input("Enable RSI strategy? (y/n): ").strip().lower() == 'y'
            config['strategies']['rsi_strategy']['enabled'] = rsi_enabled
        
        elif choice == '4':
            print("\nRisk Management Configuration:")
            
            max_pos = input("Max position size (0.1 = 10%, default 0.1): ").strip()
            if max_pos:
                try:
                    config['risk_management']['max_position_size'] = float(max_pos)
                except ValueError:
                    print("Invalid value")
            
            stop_loss = input("Stop loss percentage (0.02 = 2%, default 0.02): ").strip()
            if stop_loss:
                try:
                    config['risk_management']['stop_loss_percentage'] = float(stop_loss)
                except ValueError:
                    print("Invalid value")
            
            take_profit = input("Take profit percentage (0.04 = 4%, default 0.04): ").strip()
            if take_profit:
                try:
                    config['risk_management']['take_profit_percentage'] = float(take_profit)
                except ValueError:
                    print("Invalid value")
        
        elif choice == '5':
            save_config(config, 'config/config.yaml')
            print("Configuration saved!")
            break
        
        else:
            print("Invalid choice. Please select 1-5.")
    
    return True

def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(description='Crypto Trading Bot CLI')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Setup command
    subparsers.add_parser('setup', help='Setup the trading bot')
    
    # Run command
    run_parser = subparsers.add_parser('run', help='Run the trading bot')
    run_parser.add_argument('--config', default='config/config.yaml', help='Configuration file path')
    
    # Backtest command
    backtest_parser = subparsers.add_parser('backtest', help='Run strategy backtest')
    backtest_parser.add_argument('strategy', choices=['sma_crossover', 'rsi_strategy'], help='Strategy to test')
    backtest_parser.add_argument('symbol', help='Trading symbol (e.g., BTCUSDT)')
    backtest_parser.add_argument('start_date', help='Start date (YYYY-MM-DD)')
    backtest_parser.add_argument('--end-date', help='End date (YYYY-MM-DD)')
    backtest_parser.add_argument('--balance', type=float, default=10000, help='Initial balance')
    
    # Monitor command
    subparsers.add_parser('monitor', help='Monitor the trading bot')
    
    # Configure command
    subparsers.add_parser('config', help='Configure the trading bot')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == 'setup':
        setup_bot()
    
    elif args.command == 'run':
        run_bot(args.config)
    
    elif args.command == 'backtest':
        run_backtest(
            strategy=args.strategy,
            symbol=args.symbol,
            start_date=args.start_date,
            end_date=args.end_date,
            balance=args.balance
        )
    
    elif args.command == 'monitor':
        monitor_bot()
    
    elif args.command == 'config':
        configure_bot()

if __name__ == "__main__":
    main()
