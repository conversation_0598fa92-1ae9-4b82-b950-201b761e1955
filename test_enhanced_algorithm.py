"""
Test script for Enhanced Trading Algorithm
"""

import os
import sys
from dotenv import load_dotenv
import pandas as pd
import numpy as np

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_test_data():
    """Create comprehensive test data for algorithm testing."""
    np.random.seed(42)
    
    # Create trending data with various market conditions
    dates = pd.date_range('2024-01-01', periods=100, freq='5min')
    
    # Base price with trend and noise
    base_price = 100
    trend = np.linspace(0, 10, 100)  # Upward trend
    noise = np.random.normal(0, 1, 100)
    volatility_spikes = np.random.choice([0, 5], 100, p=[0.9, 0.1])  # Occasional volatility
    
    close_prices = base_price + trend + noise + volatility_spikes
    
    # Generate OHLCV data
    data = pd.DataFrame({
        'timestamp': dates,
        'open': close_prices * (1 + np.random.normal(0, 0.005, 100)),
        'high': close_prices * (1 + np.abs(np.random.normal(0, 0.01, 100))),
        'low': close_prices * (1 - np.abs(np.random.normal(0, 0.01, 100))),
        'close': close_prices,
        'volume': np.random.uniform(1000, 10000, 100) * (1 + np.random.choice([0, 2], 100, p=[0.8, 0.2]))  # Volume spikes
    })
    
    # Add technical indicators
    data['RSI'] = 50 + 30 * np.sin(np.arange(100) * 0.1) + np.random.normal(0, 5, 100)
    data['RSI'] = np.clip(data['RSI'], 0, 100)
    
    return data

def test_signal_filter():
    """Test Advanced Signal Filter."""
    print("🔍 Testing Advanced Signal Filter")
    print("=" * 50)
    
    try:
        from advanced_signal_filter import AdvancedSignalFilter
        
        # Initialize filter
        config = {
            'min_signal_strength': 65,
            'min_confidence': 70,
            'max_risk_score': 35,
            'min_overall_score': 75
        }
        
        filter_system = AdvancedSignalFilter(config)
        
        # Create test data
        data = create_test_data()
        
        print(f"📊 Filter Configuration:")
        print(f"   Min signal strength: {config['min_signal_strength']}%")
        print(f"   Min confidence: {config['min_confidence']}%")
        print(f"   Max risk score: {config['max_risk_score']}%")
        print(f"   Min overall score: {config['min_overall_score']}%")
        
        # Test signal quality assessment
        signal_quality = filter_system.assess_signal_quality(data, 'BUY', 'TESTUSDT', 'MovingAverageCrossover')
        
        print(f"\n📊 Signal Quality Assessment:")
        print(f"   Strength: {signal_quality.strength:.1f}%")
        print(f"   Confidence: {signal_quality.confidence:.1f}%")
        print(f"   Risk Score: {signal_quality.risk_score:.1f}%")
        print(f"   Market Alignment: {signal_quality.market_alignment:.1f}%")
        print(f"   Volume Confirmation: {signal_quality.volume_confirmation}")
        print(f"   Trend Alignment: {signal_quality.trend_alignment}")
        print(f"   Overall Score: {signal_quality.overall_score:.1f}%")
        
        # Test trading decision
        should_trade, reason = filter_system.should_trade_signal(signal_quality, 'TESTUSDT', 'BUY')
        
        print(f"\n🎯 Trading Decision:")
        print(f"   Should Trade: {should_trade}")
        print(f"   Reason: {reason}")
        
        if signal_quality.overall_score > 0:
            print(f"   ✅ Signal filter working correctly")
            return True
        else:
            print(f"   ❌ Signal filter not working")
            return False
        
    except Exception as e:
        print(f"❌ Error testing signal filter: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_market_regime_detector():
    """Test Market Regime Detector."""
    print("\n🌍 Testing Market Regime Detector")
    print("=" * 50)
    
    try:
        from market_regime_detector import MarketRegimeDetector
        
        # Initialize detector
        config = {
            'trend_threshold': 0.02,
            'volatility_threshold': 0.03,
            'lookback_period': 50
        }
        
        detector = MarketRegimeDetector(config)
        
        # Create test data
        data = create_test_data()
        
        print(f"📊 Detector Configuration:")
        print(f"   Trend threshold: {config['trend_threshold']*100}%")
        print(f"   Volatility threshold: {config['volatility_threshold']*100}%")
        print(f"   Lookback period: {config['lookback_period']}")
        
        # Test regime analysis
        regime_analysis = detector.analyze_market_regime(data, 'TESTUSDT')
        
        print(f"\n📊 Market Regime Analysis:")
        print(f"   Primary Regime: {regime_analysis.primary_regime.value}")
        print(f"   Secondary Regime: {regime_analysis.secondary_regime.value if regime_analysis.secondary_regime else 'None'}")
        print(f"   Confidence: {regime_analysis.confidence:.1f}%")
        print(f"   Trend Strength: {regime_analysis.trend_strength:.3f}")
        print(f"   Volatility Level: {regime_analysis.volatility_level:.3f}")
        print(f"   Volume Trend: {regime_analysis.volume_trend:.3f}")
        print(f"   Risk Adjustment: {regime_analysis.risk_adjustment:.2f}x")
        print(f"   Recommended Strategies: {', '.join(regime_analysis.recommended_strategies)}")
        
        # Test regime description
        description = detector.get_regime_description(regime_analysis)
        print(f"   Description: {description}")
        
        if regime_analysis.confidence > 0:
            print(f"   ✅ Market regime detector working correctly")
            return True
        else:
            print(f"   ❌ Market regime detector not working")
            return False
        
    except Exception as e:
        print(f"❌ Error testing market regime detector: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_position_sizing():
    """Test Advanced Position Sizing."""
    print("\n💰 Testing Advanced Position Sizing")
    print("=" * 50)
    
    try:
        from risk_management import RiskManager
        
        # Initialize risk manager
        config = {
            'max_position_size': 0.05,
            'max_leverage': 5,
            'max_position_value_usd': 100
        }
        
        risk_manager = RiskManager(config)
        
        print(f"📊 Risk Manager Configuration:")
        print(f"   Max position size: {config['max_position_size']*100}%")
        print(f"   Max leverage: {config['max_leverage']}x")
        print(f"   Max position value: ${config['max_position_value_usd']}")
        
        # Test scenarios
        test_scenarios = [
            {
                'name': 'High Quality Signal + Bull Market',
                'portfolio_value': 1000,
                'price': 1.0,
                'signal_quality': 85,
                'market_regime_adjustment': 1.2,
                'volatility': 0.3
            },
            {
                'name': 'Low Quality Signal + Bear Market',
                'portfolio_value': 1000,
                'price': 1.0,
                'signal_quality': 55,
                'market_regime_adjustment': 0.8,
                'volatility': 0.6
            },
            {
                'name': 'Medium Quality + High Volatility',
                'portfolio_value': 1000,
                'price': 1.0,
                'signal_quality': 70,
                'market_regime_adjustment': 1.0,
                'volatility': 0.8
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n📊 {scenario['name']}:")
            
            # Test advanced position sizing
            quantity, leverage = risk_manager.calculate_advanced_position_size(
                portfolio_value=scenario['portfolio_value'],
                price=scenario['price'],
                signal_quality=scenario['signal_quality'],
                market_regime_adjustment=scenario['market_regime_adjustment'],
                volatility=scenario['volatility'],
                symbol='TESTUSDT'
            )
            
            position_value = (quantity * scenario['price']) / leverage
            
            print(f"   Signal Quality: {scenario['signal_quality']}%")
            print(f"   Market Adjustment: {scenario['market_regime_adjustment']:.2f}x")
            print(f"   Volatility: {scenario['volatility']:.3f}")
            print(f"   Result: {quantity:.6f} @ {leverage}x (${position_value:.2f})")
        
        print(f"\n✅ Advanced position sizing working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error testing advanced position sizing: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_algorithm_features():
    """Show enhanced algorithm features."""
    print("🚀 Enhanced Trading Algorithm Features")
    print("=" * 70)
    
    print("🔍 ADVANCED SIGNAL FILTERING:")
    print("   • Multi-factor signal quality assessment")
    print("   • Strength, confidence, and risk scoring")
    print("   • Volume and trend confirmation")
    print("   • Market alignment analysis")
    print("   • Configurable quality thresholds")
    print()
    
    print("🌍 MARKET REGIME DETECTION:")
    print("   • Bull/Bear trending identification")
    print("   • Sideways/ranging market detection")
    print("   • Volatility regime classification")
    print("   • Breakout pending detection")
    print("   • Strategy recommendations per regime")
    print("   • Risk adjustment multipliers")
    print()
    
    print("💰 RISK-ADJUSTED POSITION SIZING:")
    print("   • Signal quality-based sizing")
    print("   • Market regime adjustments")
    print("   • Volatility-adjusted positions")
    print("   • Dynamic risk management")
    print("   • Maximum position limits")
    print()
    
    print("🎯 TRADING BENEFITS:")
    print("   • Higher quality trade selection")
    print("   • Better risk-adjusted returns")
    print("   • Adaptive to market conditions")
    print("   • Reduced false signals")
    print("   • Optimized position sizing")

def main():
    """Run enhanced algorithm tests."""
    print("🧪 Enhanced Trading Algorithm Test Suite")
    print("=" * 80)
    
    show_algorithm_features()
    
    tests = [
        ("Advanced Signal Filter", test_signal_filter),
        ("Market Regime Detector", test_market_regime_detector),
        ("Advanced Position Sizing", test_advanced_position_sizing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 ENHANCED TRADING ALGORITHM READY!")
        print("\n✅ Enhancements Implemented:")
        print("   ✅ Advanced signal quality filtering")
        print("   ✅ Market regime detection and adaptation")
        print("   ✅ Risk-adjusted position sizing")
        print("   ✅ Multi-factor trade analysis")
        print("   ✅ Dynamic risk management")
        
        print("\n🚀 Expected Performance Improvements:")
        print("   • Higher win rate through better signal selection")
        print("   • Better risk-adjusted returns")
        print("   • Adaptive position sizing")
        print("   • Reduced drawdowns in volatile markets")
        print("   • Optimized performance across market regimes")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
